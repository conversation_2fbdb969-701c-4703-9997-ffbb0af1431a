export interface Entity {
  id: number;
  text?: string;
  value?: number;
}

export interface Category extends Entity {
  categoryName: string;
  parentId: string;
}

export interface Remark extends Entity {
  remarkId: number;
  remarkNO: string;
  remarkName: string;
}

export interface Grade extends Entity {
  gradeName: string;
  parentId: string;
}
export interface UserInfo extends Entity {
  messages: string[];
  notifications: string[];
  tasks: string[];
}

export interface User extends Entity {
  firstname: string;
  lastname: string;
  email: string;
  avatar: string;
  mobile: string;
  homephone: string;
  workphone: string;
}

export interface Address extends Entity {
  address: string;
  city: string;
  zipcode: string;
  country: string;
}

export interface Employee extends Entity {
  employId: number;
  employNO: string;
  userName: string,
  employName: string;
  userRole: string;
  userPassword: string,
  userEmail: string
}

export interface Product extends Entity {
  furnaceName: string;
  productName: string;
  ProductDate: string;
  categoryId: string;
  category: Category;
  categoryName?: string;
  remarkId: string;
  remark: Remark;
  remarkName?: string;
  gradeId: string;
  grade: Grade;
  gradeName?: string;
  bushingNO: string;
  positionName: string;
  dryTime: string;
  cakeWeight: number;
  workDate: string;
  twisterId: string;
  twisterNO?: string;
  spindleNO: string;
  texName: string;
  biName: string;
  batchName: string;
  lmcCode: string;
  codeName: string;
  m_product_id: string;
  m_twqrcodeline_id: string;
}

export interface Track extends Entity {
  furnaceName: string;
  productName: string;
  twistName: string;
  trackDate: string;
  trackTime: string;
  twisterNO: string;
  trackT1Qty: number;
  trackT2Qty: number;
}
export interface Tracksheet extends Entity {
  furnaceName: string;
  productName: string;
  trackshetTime: string;
  gradeName: string;
  tracksheetQty: number;
  tracksheetNetWeight: number;
  trackTime: string;
  twisterNO: string;
  trackT1Qty: number;
  trackT2Qty: number;
  icountT1Qty:number;
  icountT2Qty:number;
  tracksheetNO: string;
  documentNO: string;
  m_product_id: string;
  m_twqrcodeline_id: string;
  m_cake_id: string;
}

export interface Inspect extends Entity {
  inspectlines: Inspectline[];
  classDate: string;
  shiftName: string;
  employId: number;
  employName?: string;
  groupName: string;
  typeName: string;
  quantity: number;
}

export interface Inspectline extends Entity {
  inspectId: number;
  furnaceName: string;
  productName: string;
  productDate: string;
  categoryId: string;
  category: Category;
  categoryName?: string;
  remarkId: string;
  remark: Remark;
  remarkName?: string;
  gradeId: string;
  grade: Grade;
  gradeName?: string;
  bushingNO: string;
  workDate: string;
  positionName: string;
  cakeWeight: number;
  twisterNO: string;
  spindleNO: string;
  texName: string;
  biName: string;
  batchName: string;
  codeName: string;
  documentNO: number;
  tracksheetNO: string;
  m_product_id: string;
  m_twqrcodeline_id: string;
  created: string;
}

export interface Work extends Entity {
  worklines: Workline[];
  quantity: number;
  workDate: string;
  group: string;
  shift: string;
  amount: number;
}

export interface Workline extends Entity {
  workId: number;
  worklineDate: string;
  productName: string;
  twistName: string;
  trackDate: string;
  twisterNO: string;
  trackT1Qty: number;
  trackT2Qty: number;
  workT1Qty: number;
  workT2Qty: number;
}


export interface ICount extends Entity {
  icountlines: ICountline[];
  classDate: string;
  shiftName: string;
  employId: number;
  employName?: string;
  groupName: string;
  typeName: string;
  quantity: number;
  created: string;
  updated: string;
}

export interface ICountline extends Entity {
  icountId: number;
  classDate: string;
  furnaceName: string;
  productName: string;
  twistName: string;
  trackDate: string;
  twisterNO: string;
  icountT1Qty: number;
  icountT2Qty: number;
  created: string;
  m_product_id: number;
  m_twqrcode_id: number;
}

export interface PInventory extends Entity {
  pinventorylines: PInventoryline[];
  classDate: string;
  shiftName: string;
  employId: number;
  employName?: string;
  typeName: string;
  groupName: string;
  quantity: number;
  created: string;
  updated: string;
}

export interface PInventoryline extends Entity {
  pInventoryId: number;
  furnaceName: string;
  productName: string;
  pinventoryT1Qty: number;
  pinventoryT2Qty: number;
  documentno: number;
  tracksheetno: string;
  m_product_id: number;
  created: string;
}

export interface Downgrade extends Entity {
  downgradelines: Downgradeline[];
  classDate: string;
  shiftName: string;
  employId: number;
  employName?: string;
  groupName: string;
  typeName: string;
  quantity: number;
  created: string;
  updated: string;
}

export interface Downgradeline extends Entity {
  downgradeId: number;
  furnaceName: string;
  productName: string;
  categoryId: string;
  category: Category;
  categoryName?: string;
  remarkId: string;
  remark: Category;
  remarkName?: string;
  gradeId: string;
  grade: Grade;
  gradeName?: string;
  twisterNO: string;
  spindleNO: string;
  codeName: string;
  created: string;
  m_product_id: string;
  m_twqrcodeline_id: string;
}
export interface Relabel extends Entity {
  relabellines:Relabelline[];
  classDate: string;
  shiftName: string;
  employId: number;
  employName?: string;
  groupName: string;
  typeName: string;
  quantity: number;
  created: string;
  updated: string;
}

export interface Relabelline extends Entity {
  RelabelId: number;
  furnaceName: string;
  productName: string;
  categoryId: string;
  category: Category;
  categoryName?: string;
  remarkId: string;
  remark: Category;
  remarkName?: string;
  gradeId: string;
  grade: Grade;
  gradeName?: string;
  twisterNO: string;
  spindleNO: string;
  codeName: string;
  isMDF?: string;
  created: string;
  m_product_id: string;
  m_twqrcodeline_id: string;
}


export interface Task extends Entity {
  tracks: Track[];
  quantity: number;
  taskDate: string;
  group: string;
  shift: string;
}

export interface State {
  items: Entity[];
  pagination: Pagination;
  loading: boolean;
  mode: string;
  snackbar: boolean;
  notice: string;
  inspects: Inspect[];
  tasks: Task[];
}


export type SearchFilter = {
  equal?: TODO,
  contain?: TODO,
  startsWith?: TODO,
  endsWith?: TODO,
  lessThan?: TODO,
  greaterThan?: TODO,
  lessThanOrEqual?: TODO,
  greaterThanOrEqual?: TODO,
  // between?: TODO,
  filters?: TODO

}
