import { describe, it, expect, vi } from 'vitest'

// 🎯 這是一個簡單的測試範例，你可以立即運行它來熟悉測試流程

describe('基礎測試範例', () => {
  // ✅ 測試基本的 JavaScript 功能
  describe('基本功能測試', () => {
    it('應該正確執行數學運算', () => {
      expect(1 + 1).toBe(2)
      expect(2 * 3).toBe(6)
      expect(10 / 2).toBe(5)
    })

    it('應該正確處理字符串', () => {
      const str = 'Hello World'
      expect(str.length).toBe(11)
      expect(str.toUpperCase()).toBe('HELLO WORLD')
      expect(str.includes('World')).toBe(true)
    })

    it('應該正確處理數組', () => {
      const arr = [1, 2, 3, 4, 5]
      expect(arr.length).toBe(5)
      expect(arr[0]).toBe(1)
      expect(arr.includes(3)).toBe(true)
      expect(arr.filter(x => x > 3)).toEqual([4, 5])
    })
  })

  // ✅ 測試對象和類型
  describe('對象和類型測試', () => {
    it('應該正確處理對象', () => {
      const user = {
        id: 1,
        name: '張三',
        email: '<EMAIL>',
        active: true
      }

      expect(user.id).toBe(1)
      expect(user.name).toBe('張三')
      expect(user.active).toBe(true)
      expect(typeof user.email).toBe('string')
    })

    it('應該正確檢查類型', () => {
      expect(typeof 'hello').toBe('string')
      expect(typeof 123).toBe('number')
      expect(typeof true).toBe('boolean')
      expect(typeof {}).toBe('object')
      expect(Array.isArray([])).toBe(true)
    })
  })

  // ✅ 測試異步功能
  describe('異步功能測試', () => {
    it('應該正確處理 Promise', async () => {
      const promise = Promise.resolve('成功')
      const result = await promise
      expect(result).toBe('成功')
    })

    it('應該正確處理異步函數', async () => {
      const asyncFunction = async () => {
        return new Promise(resolve => {
          setTimeout(() => resolve('延遲結果'), 10)
        })
      }

      const result = await asyncFunction()
      expect(result).toBe('延遲結果')
    })

    it('應該正確處理錯誤', async () => {
      const errorFunction = async () => {
        throw new Error('測試錯誤')
      }

      await expect(errorFunction()).rejects.toThrow('測試錯誤')
    })
  })

  // ✅ 測試 Mock 功能
  describe('Mock 功能測試', () => {
    it('應該正確使用 Mock 函數', () => {
      const mockFn = vi.fn()
      
      // 調用 mock 函數
      mockFn('參數1', '參數2')
      mockFn('參數3')

      // 驗證調用
      expect(mockFn).toHaveBeenCalledTimes(2)
      expect(mockFn).toHaveBeenCalledWith('參數1', '參數2')
      expect(mockFn).toHaveBeenLastCalledWith('參數3')
    })

    it('應該正確設置 Mock 返回值', () => {
      const mockFn = vi.fn()
      mockFn.mockReturnValue('模擬返回值')

      const result = mockFn()
      expect(result).toBe('模擬返回值')
    })

    it('應該正確模擬異步返回', async () => {
      const mockAsyncFn = vi.fn()
      mockAsyncFn.mockResolvedValue('異步模擬值')

      const result = await mockAsyncFn()
      expect(result).toBe('異步模擬值')
    })
  })

  // ✅ 測試實際的業務邏輯範例
  describe('業務邏輯測試範例', () => {
    // 模擬一個簡單的用戶驗證函數
    const validateUser = (user: any) => {
      const errors: string[] = []
      
      if (!user.name || user.name.trim() === '') {
        errors.push('姓名為必填')
      }
      
      if (!user.email || !user.email.includes('@')) {
        errors.push('請輸入有效的電子郵件')
      }
      
      if (user.age && (user.age < 0 || user.age > 150)) {
        errors.push('年齡必須在 0-150 之間')
      }
      
      return {
        isValid: errors.length === 0,
        errors
      }
    }

    it('應該驗證有效的用戶數據', () => {
      const validUser = {
        name: '李四',
        email: '<EMAIL>',
        age: 25
      }

      const result = validateUser(validUser)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('應該檢測無效的用戶數據', () => {
      const invalidUser = {
        name: '',
        email: 'invalid-email',
        age: -5
      }

      const result = validateUser(invalidUser)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('姓名為必填')
      expect(result.errors).toContain('請輸入有效的電子郵件')
      expect(result.errors).toContain('年齡必須在 0-150 之間')
    })

    it('應該處理部分無效數據', () => {
      const partialUser = {
        name: '王五',
        email: '<EMAIL>',
        age: 200  // 無效年齡
      }

      const result = validateUser(partialUser)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0]).toBe('年齡必須在 0-150 之間')
    })
  })

  // ✅ 測試日期和時間處理
  describe('日期時間測試', () => {
    it('應該正確格式化日期', () => {
      const formatDate = (date: Date) => {
        return date.toISOString().slice(0, 10)
      }

      const testDate = new Date('2023-12-01T10:30:00Z')
      expect(formatDate(testDate)).toBe('2023-12-01')
    })

    it('應該正確計算日期差異', () => {
      const daysBetween = (date1: Date, date2: Date) => {
        const diffTime = Math.abs(date2.getTime() - date1.getTime())
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      }

      const date1 = new Date('2023-12-01')
      const date2 = new Date('2023-12-05')
      expect(daysBetween(date1, date2)).toBe(4)
    })
  })
})

// 🎯 這個測試文件展示了：
// 1. 基本的測試結構和語法
// 2. 如何測試同步和異步功能
// 3. 如何使用 Mock 函數
// 4. 如何測試實際的業務邏輯
// 5. 各種常見的斷言方法

// 💡 運行這個測試：
// npm run test:unit src/tests/example.test.ts
