<template>
  <v-container fluid fill-height>
    <v-row align="center" justify="center">
      <v-col cols="12" sm="8" md="4">
        <v-card class="elevation-12">
          <v-toolbar dark color="primary">
            <v-toolbar-title>Login</v-toolbar-title>
          </v-toolbar>
          <v-card-text>
            <v-form ref="form" v-model="valid" lazy-validation>
              <v-text-field
                v-model="username"
                :rules="usernameRules"
                label="Username"
                required
                variant="outlined"
                prepend-icon="mdi-account"
                autocomplete="username"
                :disabled="loading"
              ></v-text-field>
              <v-text-field
                v-model="password"
                :rules="passwordRules"
                label="Password"
                type="password"
                required
                variant="outlined"
                prepend-icon="mdi-lock"
                autocomplete="current-password"
                :disabled="loading"
                @keyup.enter="login"
              ></v-text-field>
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="primary"
              :loading="loading"
              :disabled="!valid || loading"
              @click="login"
            >
              Login
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '../stores/user'
import { useAppStore } from '../stores/app'

interface ValidForm {
  validate: () => boolean
}

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const appStore = useAppStore()

// Reactive state
const form = ref<ValidForm>()
const valid = ref(false)
const username = ref('')
const password = ref('')

// Validation rules
const usernameRules = [
  (v: string) => !!v || 'Username is required'
]

const passwordRules = [
  (v: string) => !!v || 'Password is required'
]

// Computed properties
const loading = computed(() => appStore.loading)

// Methods
async function login() {
  console.log('Login function called')
  console.log('Form valid:', valid.value)
  console.log('Username:', username.value)
  console.log('Password:', password.value)

  if (form.value?.validate()) {
    console.log('Form validation passed')
    try {
      appStore.setLoading(true)
      console.log('Calling userStore.login...')

      const result = await userStore.login({
        username: username.value,
        password: password.value
      })

      console.log('Login successful:', result)
      console.log('Token set:', userStore.token)
      console.log('User logged in:', userStore.isLoggedIn)
      appStore.sendSuccessNotice('Login successful!')

      // Wait a bit for reactivity to update
      await new Promise(resolve => setTimeout(resolve, 100))

      const redirect = route.query.redirect as string
      console.log('Redirecting to:', redirect || '/')
      console.log('User still logged in after delay:', userStore.isLoggedIn)

      await router.push(redirect || '/')
      console.log('Navigation completed')

      // Force page reload if still on login page
      if (router.currentRoute.value.name === 'login') {
        console.log('Still on login page, forcing reload')
        window.location.href = redirect || '/'
      }
    } catch (err: any) {
      console.error('Login error:', err)
      appStore.sendErrorNotice(err.message || 'Login failed')
    } finally {
      appStore.setLoading(false)
    }
  } else {
    console.log('Form validation failed')
  }
}
</script>
