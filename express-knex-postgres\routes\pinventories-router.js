const router = require("express").Router();
const url = require('url');
const moment = require('moment');

const pinventoriesDB = require("../models/pinventories-model.js");
const employeesDB = require("../models/employees-model.js");
const pinventorylinesDB = require("../models/pinventorylines-model.js");

// GET ALL PINVENTORIES OF CAKE
router.get("/cake", async (req, res) => {
  try {
    const urlPath = req.originalUrl.split('/')[3];
    const type = urlPath.toUpperCase(); 
    if (!type){
      return res.status(400).json({ error: "Type parameter is missing." });
    }
    const pinventories = await pinventoriesDB.findByType(type);
    const employees = await employeesDB.findOfTW();
    // Map employees to pinventory
    pinventories.forEach(mem => {
      const employee = employees.find(info => info.employId === mem.employId);
      if (employee) {
        mem.employName = `${employee.employNO} ${employee.userName}`;
      }
    });
    // Format dates
    pinventories.forEach(mem => {
      mem.classDate = moment(mem.classDate).format("YYYY-M-D");
    });
    res.status(200).json(pinventories);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET ALL PINVENTORIES OF YARN
router.get("/yarn", async (req, res) => {
  try {
    const urlPath = req.originalUrl.split('/')[3]; 
    const type = urlPath.toUpperCase(); 
    if (!type){
      return res.status(400).json({ error: "Type parameter is missing." });
    }
    const pinventories = await pinventoriesDB.findByType(type);
    const employees = await employeesDB.findOfTW();
    // Map employees to pinventory
    pinventories.forEach(mem => {
      const employee = employees.find(info => info.employId === mem.employId);
      if (employee) {
        mem.employName = `${employee.employNO} ${employee.userName}`;
      }
    });
    // Format dates
    pinventories.forEach(mem => {
      mem.classDate = moment(mem.classDate).format("YYYY-M-D");
    });
    res.status(200).json(pinventories);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET ALL PINVENTORIES OF PACK
router.get("/pack", async (req, res) => {
  try {
    const urlPath = req.originalUrl.split('/')[3];
    const type = urlPath.toUpperCase(); 
    if (!type){
      return res.status(400).json({ error: "Type parameter is missing." });
    }
    const pinventories = await pinventoriesDB.findByType(type);
    const employees = await employeesDB.findOfTW();
    // Map employees to pinventory
    pinventories.forEach(mem => {
      const employee = employees.find(info => info.employId === mem.employId);
      if (employee) {
        mem.employName = `${employee.employNO} ${employee.userName}`;
      }
    });
    // Format dates
    pinventories.forEach(mem => {
      mem.classDate = moment(mem.classDate).format("YYYY-M-D");
    });
    res.status(200).json(pinventories);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET PINVENTORY BY ID
router.get("/:id", async (req, res) => {
  const pinventoryId = req.params.id;
  try {
    const pinventory = await pinventoriesDB.findById(pinventoryId);
    if (!pinventory) {
      return res.status(404).json({ err: "The specified id does not exist" });
    }
    const pinventorylines = await pinventorylinesDB.findById(pinventory[0].id);
    const employee = await employeesDB.findById(pinventory[0].employId);
    // Map employees to pinventory
    pinventory.forEach(mem => {     
        mem.employName = `${employee[0].employNO} ${employee[0].userName}`;     
    });
    // Format dates
    pinventory.forEach(mem => {
      mem.classDate = moment(mem.classDate).format("YYYY-M-D");
    });
    pinventorylines.forEach(mem => {
      mem.workDate = moment(mem.workDate).format("YYYY-M-D HH:mm");
    });
    // Assign pinventorylines to respective pinventory
    pinventory.forEach(mem => {
      mem.pinventorylines = pinventorylines.filter(d => d.pinventoryId === mem.id);
    });
    res.status(200).json(pinventory);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// INSERT PINVENTORY INTO DB
router.post("/", async (req, res) => {
  const newPInventory = req.body;
  if (!newPInventory.shiftName) {
    res.status(404).json({ err:  "You are missing information" });
  } else {
    try {
      const pinventory = await pinventoriesDB.addPInventory(newPInventory);
      res.status(201).json(pinventory);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

// UPDATE PINVENTORY INTO DB
router.put("/:id", async (req, res) => {
  const pinventoryId = req.params.id;
  const newChanges = req.body;
  if (!newChanges.shiftName) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const addChanges = await pinventoriesDB.updatePInventory(pinventoryId, newChanges);
      res.status(200).json(addChanges);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

// REMOVE PINVENTORY INTO DB
router.delete("/:id", async (req, res) => {
  const pinventoryId = req.params.id;
  try {
    const deleting = await pinventoriesDB.removePInventory(pinventoryId);
    res.status(204).json(deleting);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;
