@echo off
echo 🧪 Vue 3 測試運行器
echo ==================

:menu
echo.
echo 請選擇要執行的測試：
echo 1. 運行基礎範例測試
echo 2. 運行 Store 測試
echo 3. 運行所有測試
echo 4. 運行測試並生成覆蓋率報告
echo 5. 監視模式運行測試
echo 6. 退出
echo.

set /p choice=請輸入選項 (1-6): 

if "%choice%"=="1" (
    echo.
    echo 🎯 運行基礎範例測試...
    npx vitest src/tests/example.test.ts
    goto menu
)

if "%choice%"=="2" (
    echo.
    echo 🏪 運行 Store 測試...
    npx vitest src/tests/stores/inspects.test.ts
    goto menu
)

if "%choice%"=="3" (
    echo.
    echo 📊 運行所有測試...
    npx vitest run
    goto menu
)

if "%choice%"=="4" (
    echo.
    echo 📈 運行測試並生成覆蓋率報告...
    npx vitest run --coverage
    echo.
    echo 📊 覆蓋率報告已生成在 coverage/ 目錄
    goto menu
)

if "%choice%"=="5" (
    echo.
    echo 👀 啟動監視模式...
    echo 💡 提示：文件變更時會自動重新運行測試，按 q 退出
    npx vitest
    goto menu
)

if "%choice%"=="6" (
    echo.
    echo 👋 再見！
    exit /b 0
)

echo.
echo ❌ 無效選項，請重新選擇
goto menu
