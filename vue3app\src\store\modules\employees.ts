import { Component, Vue } from 'vue-facing-decorator'
import { createApp } from 'vue'
import { getData, putData, postData, deleteData } from '@/utils/backend-api'
import { Employee, Entity, Pagination, TableHeader } from '@/types'
import { getDefaultPagination, getPagination } from '@/utils/store-util'
import { appModule } from '@/stores/app'
import { get } from 'lodash'

export interface EmployeeState {
  items: Entity[]
  pagination: Pagination
  loading: boolean
  employee: Employee
  employees: Employee[]
}

@Component({
  name: 'EmployeeModule'
})
class EmployeeModule extends Vue implements EmployeeState {
  items: Entity[] = []
  pagination = getDefaultPagination()
  loading = true
  employee = {} as Employee
  employees: Employee[] = []

  get getEmployees() {
    return this.employees
  }

  getEmployeeById = (id: string): void => {
    this.setLoading(true)
    if (id) {
      getData('employees/' + id).then(
        res => {
          const employee = { ...res.data }
          employee.avatar = `..${employee.avatar}`
          this.setEmployee(employee)
          this.setLoading(false)
        },
        err => {
          console.log(err)
        }
      )
    } else {
      this.setEmployee({} as Employee)
      this.setLoading(false)
    }
  }

  getAllEmployees = (): void => {
    this.setLoading(true)
    getData('employees').then(res => {
      const employees = res.data
      this.setDataTable(employees)
      this.setLoading(false)
    })
  }

  searchEmployees = (searchQuery: string): void => {
    this.setLoading(true)
    getData('employees&' + searchQuery).then(res => {
      const employees = res.data
      employees.forEach((p: any) => {
        p.orderAmount = p.orders?.length
      })
      this.setDataTable(employees)
      this.setLoading(false)
    })
  }

  quickSearch = (headers: TableHeader[], qsFilter: string): void => {
    this.setLoading(true)
    getData('employees').then(res => {
      const employees = res.data.filter((r: any) =>
        headers.some((header: TableHeader) => {
          const val = get(r, [header.value])
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter)) ||
            false
          )
        })
      )
      this.setDataTable(employees)
      this.setLoading(false)
    })
  }

  deleteEmployee = (id: number): void => {
    deleteData(`employees/${id.toString()}`)
      .then(_res => {
        this.getAllEmployees()
        appModule.sendSuccessNotice('Operation is done.')
        appModule.closeNoticeWithDelay(3000)
      })
      .catch(err => {
        appModule.sendErrorNotice('Operation failed! Please try again later. ')
        appModule.closeNoticeWithDelay(5000)
      })
  }

  saveEmployee = (employee: Employee): void => {
    if (!employee.id) {
      postData('employees/', employee)
        .then(res => {
          const employee = res.data
          this.setEmployee(employee)
          appModule.sendSuccessNotice('New record has been added.')
          appModule.closeNoticeWithDelay(3000)
        })
        .catch(err => {
          console.log(err)
          appModule.sendErrorNotice('Operation failed! Please try again later. ')
          appModule.closeNoticeWithDelay(5000)
        })
    } else {
      putData('employees/' + employee.id.toString(), employee)
        .then(res => {
          const employee = res.data
          this.setEmployee(employee)
          appModule.sendSuccessNotice('The record has been updated.')
          appModule.closeNoticeWithDelay(3000)
        })
        .catch(err => {
          console.log(err)
          appModule.sendErrorNotice('Operation failed! Please try again later. ')
          appModule.closeNoticeWithDelay(5000)
        })
    }
  }

  setDataTable = (items: Employee[]) => {
    const pagination = getPagination(items)
    this.setPagination(pagination)
    this.setItems(items)
  }

  setItems = (employees: Employee[]): void => {
    this.items = employees
  }

  setPagination = (pagination: Pagination): void => {
    this.pagination = pagination
  }

  setLoading = (loading: boolean): void => {
    this.loading = loading
  }

  setEmployee = (employee: Employee): void => {
    this.employee = employee
  }
}

// Create and export a singleton instance
const app = createApp(EmployeeModule)
const vm = app.mount(document.createElement('div'))
export const employeeModule = vm as InstanceType<typeof EmployeeModule>
