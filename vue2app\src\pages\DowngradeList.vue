<template>
  <v-container fluid>
    <v-flex xs12>
      <v-card>
        <v-card-title>
          <span class="title"
            >{{title}} {{ pagination ? "(" + pagination.totalItems + ")" : "" }}
          </span>
          <v-spacer></v-spacer>
          <table-header-buttons
            :add="add"
            :reloadData="reloadData"
          ></table-header-buttons>
        </v-card-title>
        <Table
          v-if="loading === false"
          :headers="headers"
          :items="items"
          :pagination="pagination"
          :setSearch="true"
          :setEdit="true"
          :setRemove="true"
          @edit="edit"
          @remove="remove"
        ></Table>
      </v-card>
    </v-flex>
    <search-panel
      :rightDrawer="rightDrawer"
      @cancelSearch="cancelSearch"
      @searchData="searchDowngrades"
    >
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="reference"
            label="Reference"
            light
            v-model="searchFilter.contain.reference"
          ></v-text-field>
        </v-flex>
      </v-layout>
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="customer"
            label="Customer"
            light
            v-model="searchFilter.contain.customer"
          ></v-text-field>
        </v-flex>
      </v-layout>
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="minAmount"
            type="number"
            label="Min Amount"
            light
            v-model="searchFilter.greaterThanOrEqual.amount"
          ></v-text-field>
        </v-flex>
      </v-layout>
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="maxAmount"
            type="number"
            label="Max Amount"
            light
            v-model="searchFilter.lessThanOrEqual.amount"
          ></v-text-field>
        </v-flex>
      </v-layout>
    </search-panel>
    <confirm-dialog
      :dialog="dialog"
      :dialogTitle="dialogTitle"
      :dialogText="dialogText"
      @onConfirm="onConfirm"
      @onCancel="onCancel"
    ></confirm-dialog>
    <v-snackbar
      v-if="loading === false"
      :top="'top'"
      :right="true"
      :timeout="5000"
      :color="mode"
      v-model="snackbar"
    >
    <div class="text-center">
      {{ notice }}
      <v-btn dark text @click.native="closeSnackbar">Close</v-btn>
    </div>
    </v-snackbar>
  </v-container>
</template>
<script lang="ts">
import Table from "@/components/Table.vue";
import TableHeaderButtons from "@/components/TableHeaderButtons.vue";
import SearchPanel from "@/components/SearchPanel.vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
import { debounce } from "lodash";
import {
  buildSearchFilters,
  buildJsonServerQuery,
  clearSearchFilters
} from "@/utils/app-util";
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { downgradeModule } from "@/store/modules/downgrades";
import { appModule } from "@/store/modules/app";

@Component({
  components: {
    Table,
    TableHeaderButtons,
    SearchPanel,
    ConfirmDialog
  }
})
export default class DowngradeList extends Vue {
  public dialog = false;
  public dialogTitle = "Downgrade T2(主檔)刪除確認";
  public dialogText = "刪除該筆記錄?";
  public showSearchPanel = false;
  public right = true;
  public search = "";
  public headers = [
    { text: "單號", left: true, value: "id" },
    { text: "日期", value: "classDate" },
    { text: "勤別", value: "shiftName" },
    { text: "人員", value: "employName" },
    { text: "組別", value: "groupName" },
    { text: "個數", value: "quantity" },
    { text: "", value: "actions", sortable: false }
  ];
  private searchFilter = {
    contain: {
      reference: "",
      customer: ""
    },
    greaterThanOrEqual: {
      amount: 0
    },
    lessThanOrEqual: {
      amount: 0
    }
  };
  private downgradeId = '';
  private title = "";
  private type = "T2";
  private query = "";
  private color = "";
  private quickSearchFilter = "";
  private itemId = -1;
  private dateFormat: "DD-MM-YYYY";

  edit(item) {
    this.$router.push(`downgrade/${item.id}`);
  }
  add() {
    this.$router.push("newdowngrade");
  }
  remove(item) {
    this.itemId = item.id;
    this.dialog = true;
  }
  onConfirm() {
    downgradeModule.deleteDowngrade(this.itemId).then(() => {
    this.dialog = false;
    this.$nextTick(() => {
      downgradeModule.getAllDowngradesByType(this.type);
      });
    });
  }
  onCancel() {
    this.itemId = -1;
    this.dialog = false;
  }
  searchDowngrades() {
    this.showSearchPanel = !this.showSearchPanel;
    buildSearchFilters(this.searchFilter);
    this.query = buildJsonServerQuery(this.searchFilter);
    this.quickSearch = "";
    downgradeModule.searchDowngrades(this.query);
  }

  clearSearchFilters() {
    this.showSearchPanel = !this.showSearchPanel;
    clearSearchFilters(this.searchFilter);
    downgradeModule.getAllDowngradesByType(this.type);
  }

  reloadData() {
    this.query = "";
    downgradeModule.getAllDowngradesByType(this.type);
  }

  updateSearchPanel() {
    this.rightDrawer = !this.rightDrawer;
  }

  cancelSearch() {
    this.showSearchPanel = false;
  }

  closeSnackbar() {
    appModule.closeNotice();
  }

  quickSearchDowngrades = debounce(function() {
    downgradeModule.quickSearch(this.headers, this.quickSearchFilter);
  }, 500);

  get items() {
    return downgradeModule.items;
  }
  get pagination() {
    return downgradeModule.pagination;
  }
  get loading() {
    return appModule.loading;
  }
  get setSearch() {
    return true;
  }

  get mode() {
    return appModule.mode;
  }
  get snackbar() {
    return appModule.snackbar;
  }
  get notice() {
    return appModule.notice;
  }

  get rightDrawer() {
    return this.showSearchPanel;
  }

  set rightDrawer(_showSearchPanel: boolean) {
    this.showSearchPanel = _showSearchPanel;
  }

  get quickSearch() {
    return this.quickSearchFilter;
  }
  set quickSearch(val) {
    this.quickSearchFilter = val;
    this.quickSearchFilter && this.quickSearchDowngrades();
  }

  created() {
    downgradeModule.getAllDowngradesByType(this.type);
  }
  mounted() {
    this.$nextTick(() => {
      this.title = "Downgrade T2";
    });
  }
}
</script>
