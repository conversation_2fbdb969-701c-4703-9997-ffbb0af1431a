const db = require("../config/dbConfig.js");
require("dotenv").config();
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET ALL PINVENTORYS By Type
const findByType = type => {
  return db("aits_pinventories")
    .select({
      id: "aits_pinventories.id",
      classDate: "aits_pinventories.classdate",
      shiftName: "aits_pinventories.shiftname",
      employId: "aits_pinventories.employid",
      groupName: "aits_pinventories.groupname",
      typeName: "aits_pinventories.typename",
      created: "aits_pinventories.created",
      updated: "aits_pinventories.updated",
      quantity: db.raw("count(aits_pinventorylines.pinventoryid)")  
    })
    .leftJoin("aits_pinventorylines", "aits_pinventories.id", "aits_pinventorylines.pinventoryid")
    .where("aits_pinventories.typename", type)
    .where("aits_pinventories.classdate", ">=", process.env.DUE_TO_QUERY_DATE) 
    .groupBy("aits_pinventories.id", "aits_pinventories.classdate", "aits_pinventories.shiftname", "aits_pinventories.employid", "aits_pinventories.groupname", "aits_pinventories.typename")
    .orderBy("aits_pinventories.id", "desc")
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
  });
};

// GET SPECIFIC PINVENTORY BY ID
const findById = id => {
  return db("aits_pinventories")    
    .select({
      id: "aits_pinventories.id",
      classDate: "aits_pinventories.classdate",
      shiftName: "aits_pinventories.shiftname",
      employId: "aits_pinventories.employid",
      groupName: "aits_pinventories.groupname",
      typeName: "aits_pinventories.typename",
      created: "aits_pinventories.created",
      updated: "aits_pinventories.updated",
    })
    .where("id", id)
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
  });
};

// ADD A PINVENTORY
const addPInventory = pinventory => {
  return db.transaction(trx => {
    return db("aits_pinventories")
      .insert({
        classdate: pinventory.classDate,
        shiftname: pinventory.shiftName,
        employid: pinventory.employId,
        groupname: pinventory.groupName,
        typename: pinventory.typeName,
        created: db.fn.now()
        }, "id")
      .then(trx.commit)
      .catch(trx.rollback);
    })
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

// UPDATE PINVENTORY
const updatePInventory = (id, pinventory) => {
  let origin,result;
  return db.transaction(trx => {
    return db("aits_pinventories")
      .where("aits_pinventories.id", id)
      .then(old_pinventory => {
        origin = old_pinventory;
        if (old_pinventory) {
          return db("aits_pinventories")
          .where("aits_pinventories.id", id)
          .update({
            classdate: pinventory.classDate,
            shiftname: pinventory.shiftName,
            employid: pinventory.employId,
            groupname: pinventory.groupName,
            typename: pinventory.typeName,
            updated: db.fn.now()
          })
          .then(trx.commit)
          .catch(trx.rollback);
        }
    })
  })
  .then(()=>{
    return db("aits_pinventories")
    .where("aits_pinventories.id", id)
    .then(new_pinventory => { 
      result = new_pinventory;
      infoLogger.info(`update pinventory content: ${JSON.stringify({origin,result})}`)
    })
  }) 
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

// REMOVE PINVENTORY
const removePInventory = id => {
  let result;
  return db.transaction(trx => {
    return db("aits_pinventories")
      .where("aits_pinventories.id", id)
      .then(pinventory => {   
        result = pinventory;      
        if (pinventory) { 
          return db("aits_pinventories") 
            .where("aits_pinventories.id", id) 
            .del() 
            .then(trx.commit) 
            .catch(trx.rollback); 
        } 
      }) 
    }) 
  .then(() => { 
    infoLogger.info(`remove pinventory content: ${JSON.stringify(result)}`)
  })
  .catch((err) => { 
    console.error(err); 
    errorLogger.error(err); 
    throw err; 
  }); 
}; 

module.exports = {
  findByType,
  findById,  
  addPInventory,
  updatePInventory,
  removePInventory
};
