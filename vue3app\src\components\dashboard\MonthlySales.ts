import { Component, Vue, Ref, Prop } from 'vue-facing-decorator'
import { Bar } from 'vue-chartjs'
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js'

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend)

@Component({
  template: '<div :style="styles"><canvas ref="chart" :height="height"></canvas></div>',
  name: 'MonthlySalesChart'
})
export default class MonthlySalesChart extends Vue {
  @Prop({ default: 150 }) readonly height!: number
  @Ref('chart') readonly chartRef!: HTMLCanvasElement

  private get styles() {
    return {
      "margin-left": '0px',
      "background": '#e8757857'
    }
  }

  mounted() {
    const chart = new ChartJS(this.chartRef, {
      type: 'bar',
      data: {
        labels: [
          "Jan",
          "Feb",
          "Mar",
          "Apr",
          "May",
          "Jun",
          "Jul",
          "Aug",
          "Sep",
          "Oct",
          "Nov",
          "Dec"
        ],
        datasets: [
          {
            label: "Sales",
            backgroundColor: "#36A2EB",
            data: [40, 39, 10, 40, 39, 80, 40, 20, 12, 11, 25, 36]
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }
}
