const db = require("../config/dbConfig.js");
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET SPECIFIC PINVENTORYLINE BY ID
const findById = id => {
  return db("aits_pinventorylines")
    .select({
    id: "aits_pinventorylines.id",
    pinventoryId: "aits_pinventorylines.pinventoryid",
    tracksheetNO: "aits_pinventorylines.tracksheetno",
    documentNO: "aits_pinventorylines.documentno",
    furnaceName: "aits_pinventorylines.furnacename",
    productName: "aits_pinventorylines.productname",
    pinventoryT1Qty: "aits_pinventorylines.pinventoryt1qty",
    pinventoryT2Qty: "aits_pinventorylines.pinventoryt2qty",
    m_product_id: "aits_pinventorylines.m_product_id",
    created: "aits_pinventorylines.created"
    })
    .where("aits_pinventorylines.pinventoryid", id)
    .orderBy("id", "desc") 
    .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
    });
};

// GET SPECIFIC PINVENTORYLINE BY CODE
const findByCode = tracksheetNO => {
  return db("aits_pinventorylines")
    .select({
    tracksheetNO: "aits_pinventorylines.tracksheetno",
    typeName: "aits_pinventorylines.typename"     
    }) 
    .where(db.raw("TRIM(aits_pinventorylines.tracksheetno)"),tracksheetNO)
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
  });
};

// ADD A PINVENTORYLINE
const addPInventoryline = pinventoryline => {
  return db.transaction(trx => {
    return trx("aits_pinventorylines")
      .insert({
          pinventoryid: pinventoryline.pinventoryId,
          tracksheetno: pinventoryline.tracksheetNO,
          documentno: pinventoryline.documentNO,
          furnacename: pinventoryline.furnaceName,
          productname: pinventoryline.productName,
          typename: pinventoryline.typeName,
          classdate: pinventoryline.classDate,
          pinventoryt1qty: pinventoryline.pinventoryT1Qty,
          pinventoryt2qty: pinventoryline.pinventoryT2Qty,
          m_product_id: pinventoryline.m_product_id,
          created: db.fn.now()
        }, "id")      
        .then(trx.commit)
        .catch(trx.rollback);
  })
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

// REMOVE PINVENTORYLINE
const removePInventoryline = id => {
  let result;
  return db.transaction(trx => {
    return db("aits_pinventorylines")
      .where("id", id)
      .then(pinventoryline => {
        result = pinventoryline;     
        if (pinventoryline) { 
          return trx("aits_pinventorylines") 
            .where("aits_pinventorylines.id", id) 
            .del();
        } 
      })
      .then(trx.commit)
      .catch(trx.rollback); 
    })
  .then(() => { 
    infoLogger.info(`remove pinventoryline content: ${JSON.stringify(result)}`)
  })
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

module.exports = {
  findById,
  findByCode,
  addPInventoryline,
  removePInventoryline
};
