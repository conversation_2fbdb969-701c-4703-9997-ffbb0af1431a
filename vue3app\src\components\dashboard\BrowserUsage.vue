<template>
  <canvas ref="chart"></canvas>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-facing-decorator'
import { Doughnut } from 'vue-chartjs'
import { Chart as ChartJS, ArcElement, Toolt<PERSON>, Legend } from 'chart.js'

ChartJS.register(ArcElement, Tooltip, Legend)

@Component({
  name: 'BrowserUsageChart'
})
export default class BrowserUsage<PERSON>hart extends Vue {
  mounted() {
    const chart = new ChartJS(this.$refs.chart as HTMLCanvasElement, {
      type: 'doughnut',
      data: {
        labels: ["Firefox", "Safari", "Chrome", "IE"],
        datasets: [
          {
            backgroundColor: [
              "#41B883",
              "#E46651",
              "#00D8FF",
              "#DD1B16"
            ],
            data: [40, 20, 80, 10]
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }
}
</script>
