import Vue from "vue";
import VueRouter, { RouteConfig } from "vue-router";
import { userModule } from "@/store/modules/user";
import ErrorPage from "@/components/404.vue";
import Dashboard from "@/pages/Dashboard.vue";
import InspectList from "@/pages/InspectList.vue";
import InspectForm from "@/pages/InspectForm.vue";
//import WorkList from "@/pages/WorkList.vue";
//import WorkForm from "@/pages/WorkForm.vue";
//import DoffList from "@/pages/DoffList.vue";
//import DoffForm from "@/pages/DoffForm.vue";
import DowngradeList from "@/pages/DowngradeList.vue";
import DowngradeForm from "@/pages/DowngradeForm.vue";
import DisposalList from "@/pages/DisposalList.vue";
import DisposalForm from "@/pages/DisposalForm.vue";
import ICountList from "@/pages/ICountList.vue";
import ICountForm from "@/pages/ICountForm.vue";
import RelabelList from "@/pages/RelabelList.vue";
import RelabelForm from "@/pages/RelabelForm.vue";
import PInventoryOfCakeList from "@/pages/PInventoryOfCakeList.vue";
import PInventoryOfCakeForm from "@/pages/PInventoryOfCakeForm.vue";
import PInventoryOfYarnList from "@/pages/PInventoryOfYarnList.vue";
import PInventoryOfYarnForm from "@/pages/PInventoryOfYarnForm.vue";
import PInventoryOfPackList from "@/pages/PInventoryOfPackList.vue";
import PInventoryOfPackForm from "@/pages/PInventoryOfPackForm.vue";
import About from "@/pages/About.vue";
//import Tracks from "@/pages/TrackList.vue";
//import TrackForm from "@/pages/TrackForm.vue";
import Tracksheets from "@/pages/TracksheetList.vue";
import Login from "@/pages/Login.vue";
import ChangePassword from "@/components/ChangePassword.vue";

function requireAuth(to: TODO, from: TODO, next: TODO) {
  console.log(`userModule.isSignedI ${userModule.isSignedIn}`);
  if (!userModule.isSignedIn) {
    next({
      path: "/login",
      query: { redirect: to.fullPath }
    });
  } else {
    next();
  }
}

Vue.use(VueRouter);

const routes: Array<RouteConfig> = [
  { path: "/404",
    component: ErrorPage,
    name: "ErrorPage" },
  {
    path: "/dashboard",
    component: Dashboard,
    name: "dashboard",
    beforeEnter: requireAuth
  },
  { path: "/about",
    component: About,
    name: "about",
    beforeEnter: requireAuth
  },
  {
    path: "/tracksheets",
    component: Tracksheets,
    name: "tracksheets",
    beforeEnter: requireAuth
  },
  {
    path: "/inspects",
    component: InspectList,
    name: "inspects",
    beforeEnter: requireAuth
  },
  {
    path: "/newinspect",
    component: InspectForm,
    name: "newInspect",
    beforeEnter: requireAuth
  },
  {
    path: "/inspect/:id",
    component: InspectForm,
    name: "Inspect",
    beforeEnter: requireAuth
  },
/*
  {
    path: "/works",
    component: WorkList,
    name: "works",
    beforeEnter: requireAuth
  },
  {
    path: "/newwork",
    component: WorkForm,
    name: "NewWork",
    beforeEnter: requireAuth
  },
  {
    path: "/work/:id",
    component: WorkForm,
    name: "Work",
    beforeEnter: requireAuth
  },
  {
    path: "/doffs",
    component: DoffList,
    name: "doffs",
    beforeEnter: requireAuth
  },
  {
    path: "/newdoff",
    component: DoffForm,
    name: "NewDoff",
    beforeEnter: requireAuth
  },
  {
    path: "/doff/:id",
    component: DoffForm,
    name: "Doff",
    beforeEnter: requireAuth
  },
  */
  {
    path: "/downgrades",
    component: DowngradeList,
    name: "downgrades",
    beforeEnter: requireAuth
  },
  {
    path: "/newdowngrade",
    component: DowngradeForm,
    name: "newdowngrade",
    beforeEnter: requireAuth
  },
  {
    path: "/downgrade/:id",
    component: DowngradeForm,
    name: "downgrade",
    beforeEnter: requireAuth
  },
    {
    path: "/disposals",
    component: DisposalList,
    name: "disposals",
    beforeEnter: requireAuth
  },
  {
    path: "/newdisposal",
    component: DisposalForm,
    name: "newdisposal",
    beforeEnter: requireAuth
  },
  {
    path: "/disposal/:id",
    component: DisposalForm,
    name: "disposal",
    beforeEnter: requireAuth
  },
  {
  path: "/relabels",
  component: RelabelList,
  name: "relabels",
  beforeEnter: requireAuth
  },
  {
    path: "/newrelabel",
    component: RelabelForm,
    name: "newrelabel",
    beforeEnter: requireAuth
  },
  {
    path: "/relabel/:id",
    component: RelabelForm,
    name: "relabel",
    beforeEnter: requireAuth
  },
  {
    path: "/icounts",
    component: ICountList,
    name: "icounts",
    beforeEnter: requireAuth
  },
  {
    path: "/newicount",
    component: ICountForm,
    name: "newicount",
    beforeEnter: requireAuth
  },
  {
    path: "/icount/:id",
    component: ICountForm,
    name: "icount",
    beforeEnter: requireAuth
  },
  {
    path: "/pinventoryofcake",
    component: PInventoryOfCakeList,
    name: "pinventoriesofcake",
    beforeEnter: requireAuth
  },
  {
    path: "/newpinventoryofcake",
    component: PInventoryOfCakeForm,
    name: "newpinventoryofcake",
    beforeEnter: requireAuth
  },
  {
    path: "/pinventoryofcake/:id",
    component: PInventoryOfCakeForm,
    name: "pinventoryofcake",
    beforeEnter: requireAuth
  },
  {
    path: "/pinventoriesofyarn",
    component: PInventoryOfYarnList,
    name: "pinventoriesofyarn",
    beforeEnter: requireAuth
  },
  {
    path: "/newpinventoryofyarn",
    component: PInventoryOfYarnForm,
    name: "newpinventoryofyarn",
    beforeEnter: requireAuth
  },
  {
    path: "/pinventoryofyarn/:id",
    component: PInventoryOfYarnForm,
    name: "pinventoryofyarn",
    beforeEnter: requireAuth
  },
  {
    path: "/pinventoriesofpack",
    component: PInventoryOfPackList,
    name: "pinventoriesofpack",
    beforeEnter: requireAuth
  },
  {
    path: "/newpinventoryofpack",
    component: PInventoryOfPackForm,
    name: "newpinventoryofpack",
    beforeEnter: requireAuth
  },
  {
    path: "/pinventoryofpack/:id",
    component: PInventoryOfPackForm,
    name: "pinventoryofpack",
    beforeEnter: requireAuth
  },
/*
  {
    path: "/track/:id",
    component: TrackForm,
    name: "Track",
    beforeEnter: requireAuth
  },
  {
    path: "/tracks",
    component: Tracks,
    name: "tracks",
    beforeEnter: requireAuth
  },
*/
  { path: "/login", component: Login, name: "Login" },
  {
    path: "/changePassword",
    component: ChangePassword,
    name: "ChangePassword"
  },
  /* { path: "/", redirect: "/dashboard" }, */
  { path: "/", redirect: "/tracksheets" },
  { path: "*", redirect: "/404" }
];

const router = new VueRouter({
  mode: "history",
  base: process.env.BASE_URL,
  routes
});

export default router;
