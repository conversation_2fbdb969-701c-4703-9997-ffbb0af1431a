const db = require("../config/dbConfig.js");
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET CAKE TRACKSHEET BY ID
const findTrackSheetByFFCode = id => {
  return db("m_cake")
    .select({
    documentNO: "m_cake.documentno",
    furnaceName: "m_cake.funo",
    productName: "rv_product_class.name1",
    tracksheetTime: "m_cake.recorddate",
    gradeName: "rv_product_class.prod_class",
    tracksheetQty: "m_cake.qtycake",
    tracksheetNetWeight: "m_cake.qtynwgt",
    m_product_id: "m_cake.m_product_id",
    })
    .innerJoin('rv_product_class', 'm_cake.m_product_id', 'rv_product_class.m_product_id')     
    .where("m_cake.documentno", id.trim())
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

// GET YARN TRACKSHEET BY ID
const findTrackSheetByTWCode = id => {
  return db("m_twqrcode")
    .select({
      documentNO: "m_twqrcode.documentno",
      furnaceName: "m_twqrcode.funo",
      twisterNO: "m_twqrcode.mach_tmis_no",
      productName: "rv_product_class.name1",
      tracksheetTime: "m_twqrcode.created",
      m_product_id: "m_twqrcode.m_product_id",
      m_twqrcode_id: "m_twqrcode.m_twqrcode_id"
    })
    .innerJoin('rv_product_class', 'm_twqrcode.m_product_id', 'rv_product_class.m_product_id')
    .where("m_twqrcode.documentno", id.trim())
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

// GET QI TRACKSHEET BY ID
const findTrackSheetByQICode = id => {
  return db("m_twqrcode")
    .select({
      documentNO: "m_twqrcode.documentno",
      tracksheetNO: "aits_icountlines.tracksheetno",
      furnaceName: "aits_icountlines.furnacename",
      twisterNO: "m_twqrcode.mach_tmis_no",
      productName: "rv_product_class.name1",
      tracksheetTime: "m_twqrcode.created",
      m_product_id: "m_twqrcode.m_product_id",
      m_twqrcode_id: "m_twqrcode.m_twqrcode_id",
      trackT1Qty: "m_twqrcode.t1qty",
      trackT2Qty: "m_twqrcode.t2qty",
      icountT1Qty: "aits_icountlines.icountt1qty",
      icountT2Qty: "aits_icountlines.icountt2qty"
    })
    .leftJoin("aits_icountlines", "m_twqrcode.documentno", "aits_icountlines.documentno")
    .innerJoin('rv_product_class', 'm_twqrcode.m_product_id', 'rv_product_class.m_product_id')
    .orderBy(['m_twqrcode.class_date', 'm_twqrcode.m_twqrcode_id', 'm_twqrcode.mach_tmis_no'])
    .where("m_twqrcode.documentno", id.trim())
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

// SEARCH TRACKSHEETS
const searchTracksheets = (searchQuery) => {
  const { keyword } = searchQuery;
  const trimmedKeyword = keyword ? keyword.trim() : '';

  if (!trimmedKeyword) {
    return Promise.resolve([]);
  }

  // First try exact match with CAKE format
  return findTrackSheetByFFCode(trimmedKeyword)
    .then(cakeResults => {
      if (cakeResults && cakeResults.length > 0) {
        return cakeResults;
      }
      // If no CAKE results, try QI format
      return findTrackSheetByQICode(trimmedKeyword);
    })
    .then(results => {
      if (results && results.length > 0) {
        return results;
      }
      // If no QI results, try YARN format
      return findTrackSheetByTWCode(trimmedKeyword);
    })
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

module.exports = {
  findTrackSheetByFFCode,
  findTrackSheetByTWCode,
  findTrackSheetByQICode,
  searchTracksheets
};
