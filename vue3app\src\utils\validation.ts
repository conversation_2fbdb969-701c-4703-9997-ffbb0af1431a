import type { ValidationRule, ValidationRules, ShiftType, GroupType, InspectType } from '@/types/inspect'

// 基礎驗證規則
export const createValidationRules = (): ValidationRules => ({
  required: [
    (value: any) => {
      if (value === null || value === undefined) return '此欄位為必填'
      if (typeof value === 'string' && value.trim() === '') return '此欄位為必填'
      if (Array.isArray(value) && value.length === 0) return '此欄位為必填'
      return true
    }
  ],

  number: [
    (value: any) => {
      if (value === null || value === undefined || value === '') return true // 允許空值，由 required 規則處理
      const num = Number(value)
      if (isNaN(num)) return '請輸入有效數字'
      return true
    }
  ],

  email: [
    (value: string) => {
      if (!value) return true // 允許空值，由 required 規則處理
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return emailRegex.test(value) || '請輸入有效的電子郵件地址'
    }
  ],

  date: [
    (value: string) => {
      if (!value) return true // 允許空值，由 required 規則處理
      const date = new Date(value)
      return !isNaN(date.getTime()) || '請輸入有效日期'
    }
  ],

  positive: [
    (value: any) => {
      if (value === null || value === undefined || value === '') return true
      const num = Number(value)
      if (isNaN(num)) return '請輸入有效數字'
      return num > 0 || '數值必須大於 0'
    }
  ],

  integer: [
    (value: any) => {
      if (value === null || value === undefined || value === '') return true
      const num = Number(value)
      if (isNaN(num)) return '請輸入有效數字'
      return Number.isInteger(num) || '請輸入整數'
    }
  ]
})

// 特定欄位驗證規則
export const shiftValidation: ValidationRule[] = [
  (value: string) => {
    if (!value) return '請選擇勤別'
    const validShifts: ShiftType[] = ['1', '2', '3']
    return validShifts.includes(value as ShiftType) || '請選擇有效的勤別'
  }
]

export const groupValidation: ValidationRule[] = [
  (value: string) => {
    if (!value) return '請選擇組別'
    const validGroups: GroupType[] = ['A', 'B', 'C', 'D']
    return validGroups.includes(value as GroupType) || '請選擇有效的組別'
  }
]

export const inspectTypeValidation: ValidationRule[] = [
  (value: string) => {
    if (!value) return '請選擇類型'
    const validTypes: InspectType[] = ['YARN', 'CAKE', 'PACK']
    return validTypes.includes(value as InspectType) || '請選擇有效的類型'
  }
]

export const employeeIdValidation: ValidationRule[] = [
  (value: any) => {
    if (!value || value === 0) return '請選擇人員'
    const num = Number(value)
    if (isNaN(num) || num <= 0) return '請選擇有效的人員'
    return true
  }
]

export const quantityValidation: ValidationRule[] = [
  (value: any) => {
    if (value === null || value === undefined || value === '') return true // 允許空值
    const num = Number(value)
    if (isNaN(num)) return '請輸入有效數字'
    if (num < 0) return '數量不能為負數'
    if (!Number.isInteger(num)) return '數量必須為整數'
    return true
  }
]

export const dateRangeValidation = (startDate: string, endDate: string): string | true => {
  if (!startDate || !endDate) return true

  const start = new Date(startDate)
  const end = new Date(endDate)

  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return '請輸入有效日期'
  }

  if (start > end) {
    return '開始日期不能晚於結束日期'
  }

  return true
}

// 複合驗證函數
export const validateInspectForm = (data: any): Record<string, string[]> => {
  const errors: Record<string, string[]> = {}
  const rules = createValidationRules()

  // 驗證必填欄位
  const requiredFields = ['classDate', 'shiftName', 'employId', 'groupName', 'typeName']

  requiredFields.forEach(field => {
    const fieldErrors: string[] = []

    // 執行必填驗證
    rules.required.forEach(rule => {
      const result = rule(data[field])
      if (result !== true) {
        fieldErrors.push(result)
      }
    })

    if (fieldErrors.length > 0) {
      errors[field] = fieldErrors
    }
  })

  // 特定欄位驗證
  if (data.shiftName) {
    const shiftErrors: string[] = []
    shiftValidation.forEach(rule => {
      const result = rule(data.shiftName)
      if (result !== true) {
        shiftErrors.push(result)
      }
    })
    if (shiftErrors.length > 0) {
      errors.shiftName = [...(errors.shiftName || []), ...shiftErrors]
    }
  }

  if (data.groupName) {
    const groupErrors: string[] = []
    groupValidation.forEach(rule => {
      const result = rule(data.groupName)
      if (result !== true) {
        groupErrors.push(result)
      }
    })
    if (groupErrors.length > 0) {
      errors.groupName = [...(errors.groupName || []), ...groupErrors]
    }
  }

  if (data.typeName) {
    const typeErrors: string[] = []
    inspectTypeValidation.forEach(rule => {
      const result = rule(data.typeName)
      if (result !== true) {
        typeErrors.push(result)
      }
    })
    if (typeErrors.length > 0) {
      errors.typeName = [...(errors.typeName || []), ...typeErrors]
    }
  }

  if (data.employId !== undefined) {
    const employeeErrors: string[] = []
    employeeIdValidation.forEach(rule => {
      const result = rule(data.employId)
      if (result !== true) {
        employeeErrors.push(result)
      }
    })
    if (employeeErrors.length > 0) {
      errors.employId = [...(errors.employId || []), ...employeeErrors]
    }
  }

  if (data.quantity !== undefined) {
    const quantityErrors: string[] = []
    quantityValidation.forEach(rule => {
      const result = rule(data.quantity)
      if (result !== true) {
        quantityErrors.push(result)
      }
    })
    if (quantityErrors.length > 0) {
      errors.quantity = [...(errors.quantity || []), ...quantityErrors]
    }
  }

  return errors
}

export const validateInspectlineForm = (data: any): Record<string, string[]> => {
  const errors: Record<string, string[]> = {}
  const rules = createValidationRules()

  // 驗證必填欄位
  const requiredFields = ['codeName', 'remarkName', 'quantity']

  requiredFields.forEach(field => {
    const fieldErrors: string[] = []

    rules.required.forEach(rule => {
      const result = rule(data[field])
      if (result !== true) {
        fieldErrors.push(result)
      }
    })

    if (fieldErrors.length > 0) {
      errors[field] = fieldErrors
    }
  })

  // 數量驗證
  if (data.quantity !== undefined) {
    const quantityErrors: string[] = []
    quantityValidation.forEach(rule => {
      const result = rule(data.quantity)
      if (result !== true) {
        quantityErrors.push(result)
      }
    })
    if (quantityErrors.length > 0) {
      errors.quantity = [...(errors.quantity || []), ...quantityErrors]
    }
  }

  return errors
}

// 類型守衛函數
export const isValidShift = (value: any): value is ShiftType => {
  return typeof value === 'string' && ['1', '2', '3'].includes(value)
}

export const isValidGroup = (value: any): value is GroupType => {
  return typeof value === 'string' && ['A', 'B', 'C', 'D'].includes(value)
}

export const isValidInspectType = (value: any): value is InspectType => {
  return typeof value === 'string' && ['YARN', 'CAKE', 'PACK'].includes(value)
}

// 數據清理函數
export const sanitizeInspectData = (data: any): any => {
  const numId = Number(data.id)
  const numEmployId = Number(data.employId)
  const numQuantity = Number(data.quantity)

  return {
    ...data,
    id: !isNaN(numId) && numId > 0 ? numId : null,
    employId: !isNaN(numEmployId) ? numEmployId : 0,
    quantity: !isNaN(numQuantity) ? numQuantity : 0,
    classDate: data.classDate ? String(data.classDate).slice(0, 10) : '',
    shiftName: isValidShift(data.shiftName) ? data.shiftName : '',
    groupName: isValidGroup(data.groupName) ? data.groupName : '',
    typeName: isValidInspectType(data.typeName) ? data.typeName : 'YARN'
  }
}

export const sanitizeInspectlineData = (data: any): any => {
  const numId = Number(data.id)
  const numInspectId = Number(data.inspectId)
  const numProductId = Number(data.productId)
  const numRemarkId = Number(data.remarkId)
  const numQuantity = Number(data.quantity)

  return {
    ...data,
    id: !isNaN(numId) && numId > 0 ? numId : null,
    inspectId: !isNaN(numInspectId) ? numInspectId : 0,
    productId: !isNaN(numProductId) && numProductId > 0 ? numProductId : null,
    remarkId: !isNaN(numRemarkId) && numRemarkId > 0 ? numRemarkId : null,
    quantity: !isNaN(numQuantity) && numQuantity > 0 ? numQuantity : 1,
    codeName: String(data.codeName || '').trim(),
    remarkName: String(data.remarkName || '').trim()
  }
}
