<template>
  <v-card-text>
    <div class="d-flex align-center justify-space-between mb-4">
      <div>
        <div class="text-h6">{{ title }}</div>
        <div class="text-subtitle-2">{{ subtitle }}</div>
      </div>
      <v-btn icon="mdi-dots-vertical" variant="text"></v-btn>
    </div>
    <v-sheet
      class="pa-4"
      height="300"
      style="position: relative;"
    >
      <div class="text-center text-subtitle-1 pt-4">
        Chart placeholder - Will integrate with Chart.js
      </div>
    </v-sheet>
  </v-card-text>
</template>

<script setup lang="ts">
interface Props {
  title?: string
  subtitle?: string
}

withDefaults(defineProps<Props>(), {
  title: 'Chart Title',
  subtitle: 'Chart Subtitle'
})
</script>
