<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <span class="text-h4">
              <v-icon class="mr-2">mdi-note-multiple-outline</v-icon>
              EM即時生產查詢
            </span>
            <v-spacer />
            <v-btn
              color="brown-lighten-1"
              size="small"
              icon
              @click="clearData"
              class="mr-2"
            >
              <v-icon>mdi-refresh</v-icon>
            </v-btn>
          </v-card-title>
          
          <v-card-text>
            <v-alert type="info" class="mb-4">
              <strong>🔍 EM即時生產查詢</strong>
              請輸入傳票QRCode或Cap QRCode進行查詢。
            </v-alert>
            
            <v-row>
              <v-col cols="12">
                <v-text-field
                  ref="searchInput"
                  v-model="searchQuery"
                  label="傳票QRCode 或 Cap QRCode"
                  append-icon="mdi-magnify"
                  @keyup.enter="performSearch"
                  @change="performSearch"
                  counter="27"
                  variant="outlined"
                  single-line
                  hide-details
                  clearable
                />
              </v-col>
            </v-row>
            
            <!-- Loading indicator -->
            <v-row v-if="loading" class="mt-4">
              <v-col cols="12">
                <v-progress-linear indeterminate color="primary" />
                <p class="text-center mt-2">查詢中...</p>
              </v-col>
            </v-row>
            
            <!-- Search Results -->
            <v-row v-if="searchAttempted && !loading" class="mt-4">
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-table</v-icon>
                    查詢結果
                    <v-spacer />
                    <v-chip color="info" size="small">
                      {{ items.length }} 筆資料
                    </v-chip>
                  </v-card-title>
                  
                  <v-card-text>
                    <v-data-table
                      :headers="selectedHeaders"
                      :items="items"
                      :items-per-page="10"
                      class="elevation-1"
                      no-data-text="查無資料"
                    >
                      <template v-slot:item.tracksheetTime="{ item }">
                        <span>{{ formatDateTime(item.tracksheetTime) }}</span>
                      </template>
                      
                      <template v-slot:item.tracksheetQty="{ item }">
                        <v-chip color="primary" size="small">
                          {{ item.tracksheetQty }}
                        </v-chip>
                      </template>
                      
                      <template v-slot:item.tracksheetNetWeight="{ item }">
                        <v-chip color="success" size="small">
                          {{ item.tracksheetNetWeight }} kg
                        </v-chip>
                      </template>
                      
                      <template v-slot:no-data>
                        <v-alert type="warning" class="ma-4">
                          查無相關資料，請檢查輸入的QRCode是否正確。
                        </v-alert>
                      </template>
                    </v-data-table>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            
            <!-- Query Type Information -->
            <v-row class="mt-4">
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-information</v-icon>
                    查詢類型識別
                  </v-card-title>
                  
                  <v-card-text>
                    <v-list>
                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon :color="isYarnQRcode ? 'success' : 'grey'">
                            {{ isYarnQRcode ? 'mdi-check-circle' : 'mdi-circle-outline' }}
                          </v-icon>
                        </template>
                        <v-list-item-title>Yarn QRCode</v-list-item-title>
                        <v-list-item-subtitle>
                          {{ isYarnQRcode ? '已識別為Yarn QRCode查詢' : '非Yarn QRCode' }}
                        </v-list-item-subtitle>
                      </v-list-item>
                      
                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon :color="isCakeTrackSheetNO ? 'success' : 'grey'">
                            {{ isCakeTrackSheetNO ? 'mdi-check-circle' : 'mdi-circle-outline' }}
                          </v-icon>
                        </template>
                        <v-list-item-title>Cake傳票</v-list-item-title>
                        <v-list-item-subtitle>
                          {{ isCakeTrackSheetNO ? '已識別為Cake傳票查詢' : '非Cake傳票' }}
                        </v-list-item-subtitle>
                      </v-list-item>
                      
                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon :color="isYarnTrackSheetNO ? 'success' : 'grey'">
                            {{ isYarnTrackSheetNO ? 'mdi-check-circle' : 'mdi-circle-outline' }}
                          </v-icon>
                        </template>
                        <v-list-item-title>Yarn傳票</v-list-item-title>
                        <v-list-item-subtitle>
                          {{ isYarnTrackSheetNO ? '已識別為Yarn傳票查詢' : '非Yarn傳票' }}
                        </v-list-item-subtitle>
                      </v-list-item>
                    </v-list>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            
            <!-- Test Status -->
            <v-row class="mt-4">
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-test-tube</v-icon>
                    測試狀態
                  </v-card-title>
                  
                  <v-card-text>
                    <v-list>
                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="success">mdi-check-circle</v-icon>
                        </template>
                        <v-list-item-title>Vue3 Composition API</v-list-item-title>
                        <v-list-item-subtitle>EM即時生產查詢響應式數據正常運作</v-list-item-subtitle>
                      </v-list-item>
                      
                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="success">mdi-check-circle</v-icon>
                        </template>
                        <v-list-item-title>即時查詢功能</v-list-item-title>
                        <v-list-item-subtitle>支援多種QRCode類型的即時查詢</v-list-item-subtitle>
                      </v-list-item>
                      
                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="success">mdi-check-circle</v-icon>
                        </template>
                        <v-list-item-title>路由參數</v-list-item-title>
                        <v-list-item-subtitle>
                          當前路由: {{ $route.path }}
                        </v-list-item-subtitle>
                      </v-list-item>
                    </v-list>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            
            <!-- Real-time Data Display -->
            <v-row class="mt-4">
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-database</v-icon>
                    即時數據 (開發模式)
                  </v-card-title>
                  
                  <v-card-text>
                    <pre>{{ JSON.stringify({ searchQuery, isYarnQRcode, isCakeTrackSheetNO, isYarnTrackSheetNO, items }, null, 2) }}</pre>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          
          <v-card-actions>
            <v-btn
              color="primary"
              @click="performSearch"
              :disabled="!searchQuery"
              prepend-icon="mdi-magnify"
            >
              查詢
            </v-btn>
            
            <v-btn
              color="secondary"
              @click="clearData"
              prepend-icon="mdi-refresh"
            >
              清除
            </v-btn>
            
            <v-btn
              color="error"
              @click="goBack"
              prepend-icon="mdi-arrow-left"
            >
              返回
            </v-btn>
            
            <v-spacer />
            
            <v-chip color="info">
              查詢狀態: {{ loading ? '查詢中' : '就緒' }}
            </v-chip>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <!-- 成功訊息 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          關閉
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 響應式數據
const searchInput = ref()
const loading = ref(false)
const searchAttempted = ref(false)
const searchQuery = ref('')
const items = ref([])

// QRCode類型識別
const isYarnQRcode = ref(false)
const isCakeTrackSheetNO = ref(false)
const isYarnTrackSheetNO = ref(false)

const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

// 表格標題定義
const defaultHeaders = [
  { title: '追踨傳票單號', key: 'tracksheetNO', align: 'start' },
  { title: '日期時間', key: 'tracksheetTime', align: 'start' },
  { title: '爐別', key: 'furnaceName', align: 'start' },
  { title: '品種', key: 'productName', align: 'start' },
  { title: '個數', key: 'tracksheetQty', align: 'center' },
  { title: '淨重(kg)', key: 'tracksheetNetWeight', align: 'center' }
]

const yarnQRcodeHeaders = [
  { title: 'BI檢測', key: 'biName', align: 'start' },
  { title: 'TEX檢測', key: 'texName', align: 'start' },
  { title: 'Lot NO', key: 'batchName', align: 'start' },
  { title: '乾燥時間(hrs)', key: 'dryTime', align: 'center' },
  { title: 'Cake重量(g)', key: 'cakeWeight', align: 'center' }
]

const cakeTrackSheetHeaders = [
  { title: '追踨傳票單號', key: 'tracksheetNO', align: 'start' },
  { title: '爐別', key: 'furnaceName', align: 'start' },
  { title: '品種', key: 'productName', align: 'start' },
  { title: '個數', key: 'tracksheetQty', align: 'center' },
  { title: '淨重(kg)', key: 'tracksheetNetWeight', align: 'center' }
]

const yarnTrackSheetHeaders = [
  { title: '追踨傳票單號', key: 'tracksheetNO', align: 'start' },
  { title: '爐別', key: 'furnaceName', align: 'start' },
  { title: '品種', key: 'productName', align: 'start' },
  { title: '個數', key: 'tracksheetQty', align: 'center' }
]

// 計算屬性
const selectedHeaders = computed(() => {
  if (isCakeTrackSheetNO.value) {
    return cakeTrackSheetHeaders
  } else if (isYarnTrackSheetNO.value) {
    return yarnTrackSheetHeaders
  } else if (isYarnQRcode.value) {
    return yarnQRcodeHeaders
  }
  return defaultHeaders
})

// 方法
const identifyQRCodeType = (query: string) => {
  // 重置所有類型
  isYarnQRcode.value = false
  isCakeTrackSheetNO.value = false
  isYarnTrackSheetNO.value = false
  
  if (!query) return
  
  // 根據長度和格式識別QRCode類型
  if (query.length === 14) {
    isYarnQRcode.value = true
  } else if (query.startsWith('CK') && query.length >= 10) {
    isCakeTrackSheetNO.value = true
  } else if (query.startsWith('YN') && query.length >= 10) {
    isYarnTrackSheetNO.value = true
  }
}

const performSearch = async () => {
  const query = searchQuery.value?.trim()
  
  if (!query) {
    showMessage('請輸入傳票單號或Cap QRCode', 'error')
    return
  }
  
  loading.value = true
  searchAttempted.value = true
  items.value = []
  
  // 識別QRCode類型
  identifyQRCodeType(query)
  
  try {
    // 模擬API查詢
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 模擬不同類型的查詢結果
    if (isYarnQRcode.value) {
      items.value = [
        {
          biName: 'BI-001',
          texName: 'TEX-001',
          batchName: 'LOT20241201',
          dryTime: 24,
          cakeWeight: 2500
        }
      ]
      showMessage('Yarn QRCode查詢完成', 'success')
    } else if (isCakeTrackSheetNO.value) {
      items.value = [
        {
          tracksheetNO: query,
          furnaceName: 'F001',
          productName: 'Cake產品A',
          tracksheetQty: 50,
          tracksheetNetWeight: 125.5
        }
      ]
      showMessage('Cake傳票查詢完成', 'success')
    } else if (isYarnTrackSheetNO.value) {
      items.value = [
        {
          tracksheetNO: query,
          furnaceName: 'F002',
          productName: 'Yarn產品B',
          tracksheetQty: 80
        }
      ]
      showMessage('Yarn傳票查詢完成', 'success')
    } else {
      items.value = [
        {
          tracksheetNO: query,
          tracksheetTime: new Date().toISOString(),
          furnaceName: 'F003',
          productName: '一般產品',
          tracksheetQty: 100,
          tracksheetNetWeight: 250.0
        }
      ]
      showMessage('一般查詢完成', 'success')
    }
  } catch (error) {
    showMessage('查詢失敗，請重試', 'error')
  } finally {
    loading.value = false
  }
}

const clearData = () => {
  searchQuery.value = ''
  items.value = []
  searchAttempted.value = false
  isYarnQRcode.value = false
  isCakeTrackSheetNO.value = false
  isYarnTrackSheetNO.value = false
  showMessage('數據已清除', 'info')
  
  // 重新聚焦到搜尋框
  nextTick(() => {
    if (searchInput.value) {
      searchInput.value.focus()
    }
  })
}

const goBack = () => {
  router.push('/vue3-form-test-center')
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-TW')
}

const showMessage = (message: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}

// 生命週期
onMounted(() => {
  showMessage('EM即時生產查詢已準備就緒', 'info')
  
  // 聚焦到搜尋框
  nextTick(() => {
    if (searchInput.value) {
      searchInput.value.focus()
    }
  })
})
</script>

<style scoped>
.v-card {
  margin-bottom: 16px;
}

pre {
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
