{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": false, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "experimentalDecorators": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "baseUrl": ".", "types": ["webpack-env", "mocha", "chai", "vuetify"], "paths": {"@/*": ["./src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "vueCompilerOptions": {"target": 2, "extensions": [".vue"]}, "include": ["shims-tsx.d.ts", "shims-vue.d.ts", "src/*.ts", "src/*.vue", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx"], "exclude": ["node_modules"]}