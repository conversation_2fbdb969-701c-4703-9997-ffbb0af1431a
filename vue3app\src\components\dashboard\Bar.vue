<template>
  <canvas ref="chart"></canvas>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-facing-decorator'
import { Bar } from 'vue-chartjs'
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js'

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend)

@Component({
  name: '<PERSON><PERSON><PERSON>'
})
export default class BarChart extends Vue {
  mounted() {
    const chart = new ChartJS(this.$refs.chart as HTMLCanvasElement, {
      type: 'bar',
      data: {
        labels: [
          "January",
          "February",
          "March",
          "April",
          "May",
          "June",
          "July"
        ],
        datasets: [
          {
            label: "Data One",
            backgroundColor: "#f87979",
            data: [40, 20, 12, 39, 10, 40, 39]
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }
}
</script>
