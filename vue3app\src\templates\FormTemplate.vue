<template>
  <v-container fluid>
    <v-card>
      <v-card-title>
        <span class="text-h5">{{ title }}</span>
      </v-card-title>
      
      <v-card-text>
        <v-form ref="formRef" v-model="formValid" lazy-validation>
          <v-row>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.id"
                label="單號"
                variant="outlined"
                readonly
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.classDate"
                label="日期"
                type="date"
                variant="outlined"
                required
                :rules="dateRules"
              ></v-text-field>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12" md="6">
              <v-select
                v-model="formData.employId"
                :items="employees"
                item-title="employName"
                item-value="id"
                label="人員"
                variant="outlined"
                required
                :rules="employeeRules"
              ></v-select>
            </v-col>
            <v-col cols="12" md="6">
              <v-select
                v-model="formData.shiftName"
                :items="shifts"
                label="勤別"
                variant="outlined"
                required
                :rules="shiftRules"
              ></v-select>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12" md="6">
              <v-select
                v-model="formData.groupName"
                :items="groups"
                label="組別"
                variant="outlined"
                required
                :rules="groupRules"
              ></v-select>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn
          color="primary"
          @click="save"
          :loading="loading"
          :disabled="!formValid"
        >
          儲存
        </v-btn>
        <v-btn
          color="secondary"
          @click="cancel"
          :disabled="loading"
        >
          取消
        </v-btn>
      </v-card-actions>
    </v-card>

    <!-- 明細檔表格 -->
    <v-card v-if="formData.id" class="mt-4">
      <v-card-title>
        <span class="text-h6">明細資料</span>
        <v-spacer></v-spacer>
        <v-btn
          color="primary"
          @click="addDetail"
          :disabled="loading"
        >
          新增明細
        </v-btn>
      </v-card-title>
      
      <Table
        :headers="detailHeaders"
        :items="detailItems"
        :loading="loading"
        :pagination="pagination"
        :setEdit="true"
        :setRemove="true"
        @edit="editDetail"
        @remove="removeDetail"
      />
    </v-card>

    <!-- 明細編輯對話框 -->
    <v-dialog v-model="detailDialog" width="700" persistent>
      <v-card>
        <v-card-title>
          {{ detailTitle }}
        </v-card-title>
        
        <v-card-text>
          <v-form ref="detailFormRef" v-model="detailFormValid" lazy-validation>
            <v-text-field
              v-model="detailData.code"
              label="代碼"
              variant="outlined"
              required
              :rules="codeRules"
            ></v-text-field>
            
            <v-text-field
              v-model="detailData.quantity"
              label="數量"
              type="number"
              variant="outlined"
              required
              :rules="quantityRules"
            ></v-text-field>
          </v-form>
        </v-card-text>
        
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="primary"
            @click="saveDetail"
            :loading="loading"
            :disabled="!detailFormValid"
          >
            確認
          </v-btn>
          <v-btn
            color="secondary"
            @click="cancelDetail"
            :disabled="loading"
          >
            取消
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 確認刪除對話框 -->
    <ConfirmDialog
      :dialog="confirmDialog"
      :dialogTitle="confirmTitle"
      :dialogText="confirmText"
      @onConfirm="onConfirmDelete"
      @onCancel="onCancelDelete"
      @update:dialog="confirmDialog = $event"
    />

    <!-- 通知訊息 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          關閉
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Table from '@/components/Table.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import { getISOClassDate } from '@/utils/app-util'

// Props定義
interface Props {
  apiEndpoint: string
  title: string
  newRoute: string
  listRoute: string
}

const props = defineProps<Props>()

// 路由
const router = useRouter()
const route = useRoute()

// 表單引用
const formRef = ref()
const detailFormRef = ref()

// 響應式數據
const formValid = ref(false)
const detailFormValid = ref(false)
const loading = ref(false)
const title = ref(props.title)

// 主檔數據
const formData = ref({
  id: null,
  classDate: getISOClassDate().slice(0, 10),
  employId: null,
  shiftName: '',
  groupName: ''
})

// 明細檔數據
const detailItems = ref([])
const detailData = ref({
  code: '',
  quantity: 0
})
const detailDialog = ref(false)
const detailTitle = ref('新增明細')
const editingDetailId = ref(null)

// 確認對話框
const confirmDialog = ref(false)
const confirmTitle = ref('確認刪除')
const confirmText = ref('確定要刪除此項目嗎？')
const deleteItemId = ref(null)

// 通知訊息
const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

// 選項數據
const employees = ref([])
const shifts = ref(['早班', '中班', '晚班'])
const groups = ref(['A組', 'B組', 'C組'])

// 表格標題
const detailHeaders = ref([
  { title: '代碼', key: 'code', align: 'start' },
  { title: '數量', key: 'quantity', align: 'start' },
  { title: '操作', key: 'actions', sortable: false }
])

// 分頁
const pagination = ref({
  page: 1,
  rowsPerPage: 10,
  pages: 1,
  totalItems: 0
})

// 驗證規則
const dateRules = [
  (v: string) => !!v || '請選擇日期'
]

const employeeRules = [
  (v: any) => !!v || '請選擇人員'
]

const shiftRules = [
  (v: string) => !!v || '請選擇勤別'
]

const groupRules = [
  (v: string) => !!v || '請選擇組別'
]

const codeRules = [
  (v: string) => !!v || '請輸入代碼'
]

const quantityRules = [
  (v: number) => !!v || '請輸入數量',
  (v: number) => v > 0 || '數量必須大於0'
]

// 計算屬性
const isEditMode = computed(() => !!route.params.id)

// 方法
const showMessage = (message: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}

const save = async () => {
  if (!formRef.value?.validate()) return
  
  loading.value = true
  try {
    // 這裡需要根據具體的API調用進行實現
    // await saveData(formData.value)
    showMessage('儲存成功')
    if (!isEditMode.value) {
      router.push(props.listRoute)
    }
  } catch (error) {
    console.error('Save error:', error)
    showMessage('儲存失敗，請稍後再試', 'error')
  } finally {
    loading.value = false
  }
}

const cancel = () => {
  router.push(props.listRoute)
}

const addDetail = () => {
  detailData.value = { code: '', quantity: 0 }
  detailTitle.value = '新增明細'
  editingDetailId.value = null
  detailDialog.value = true
}

const editDetail = (item: any) => {
  detailData.value = { ...item }
  detailTitle.value = '編輯明細'
  editingDetailId.value = item.id
  detailDialog.value = true
}

const saveDetail = async () => {
  if (!detailFormRef.value?.validate()) return
  
  loading.value = true
  try {
    // 這裡需要根據具體的API調用進行實現
    // if (editingDetailId.value) {
    //   await updateDetailData(detailData.value)
    // } else {
    //   await addDetailData(detailData.value)
    // }
    showMessage('明細儲存成功')
    detailDialog.value = false
    // await loadDetailData()
  } catch (error) {
    console.error('Save detail error:', error)
    showMessage('明細儲存失敗，請稍後再試', 'error')
  } finally {
    loading.value = false
  }
}

const cancelDetail = () => {
  detailDialog.value = false
  detailData.value = { code: '', quantity: 0 }
  editingDetailId.value = null
}

const removeDetail = (item: any) => {
  deleteItemId.value = item.id
  confirmDialog.value = true
}

const onConfirmDelete = async () => {
  loading.value = true
  try {
    // 這裡需要根據具體的API調用進行實現
    // await deleteDetailData(deleteItemId.value)
    showMessage('刪除成功')
    confirmDialog.value = false
    // await loadDetailData()
  } catch (error) {
    console.error('Delete error:', error)
    showMessage('刪除失敗，請稍後再試', 'error')
  } finally {
    loading.value = false
    deleteItemId.value = null
  }
}

const onCancelDelete = () => {
  confirmDialog.value = false
  deleteItemId.value = null
}

// 生命週期
onMounted(async () => {
  if (isEditMode.value) {
    // 載入編輯數據
    // await loadData(route.params.id)
    // await loadDetailData()
  }
  // await loadEmployees()
})
</script>
