const router = require("express").Router();
const moment = require('moment');

const pinventorylinesDB = require("../models/pinventorylines-model.js");

// GET PINVENTORYLINE BY ID
router.get("/:id", async (req, res) => {
  const pinventoryId = req.params.id;
  try {
    const pinventoryline = await pinventorylinesDB.findById(pinventoryId);
    if (!pinventoryline) {
      res.status(404).json({ err: "The pinventoryline with the specified id does not exist" });
    } else {  
    pinventoryline.forEach(mem => {
      mem.workDate = moment(mem.workDate).format("YYYY-M-D HH:mm");
    });
      res.status(200).json(pinventoryline);
    }
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET PINVENTORYLINE BY CODE
router.get("/duplicate/:id", async (req, res) => {
  const tracksheetNO = req.params.id;
  try {
    const exist = await pinventorylinesDB.findByCode(tracksheetNO);
    if (Array.isArray(exist) && exist.length > 0) {
      res.status(200).json(exist);
    } else {
      res.status(404).json({ error: "Data not found" });
    }
  } catch (err) {
    console.error(err);
    res.status(500).json({ err: err.message });
  }
});

// INSRT PINVENTORYLINE INTO DB
router.post("/", async (req, res) => {
  const newPInventoryline = req.body;
  if (!newPInventoryline.pinventoryId) {
    res.status(404).json({ err: "Please provide the name" });
  } else {
    try {
      const pinventoryline = await pinventorylinesDB.addPInventoryline(newPInventoryline);
      res.status(201).json(pinventoryline);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

// UPDATE PINVENTORYLINE IN DB
router.delete("/:id", async (req, res) => {
  const pinventorylineId = req.params.id;
  try {
    const deleting = await pinventorylinesDB.removePInventoryline(pinventorylineId);
    res.status(204).json(deleting);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;