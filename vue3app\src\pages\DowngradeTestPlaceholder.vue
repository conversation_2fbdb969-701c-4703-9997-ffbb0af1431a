<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <span class="text-h4">
              <v-icon class="mr-2">mdi-arrow-down-bold</v-icon>
              降級表單測試頁面
            </span>
          </v-card-title>
          
          <v-card-text>
            <v-alert type="success" class="mb-4">
              <strong>✅ 降級表單路由測試成功！</strong>
              這個頁面證明降級表單的路由導航是正常工作的。
            </v-alert>
            
            <v-row>
              <v-col cols="12">
                <h3>路由信息</h3>
                <v-list>
                  <v-list-item>
                    <template v-slot:prepend>
                      <v-icon color="info">mdi-information</v-icon>
                    </template>
                    <v-list-item-title>當前路由</v-list-item-title>
                    <v-list-item-subtitle>{{ $route.path }}</v-list-item-subtitle>
                  </v-list-item>
                  
                  <v-list-item>
                    <template v-slot:prepend>
                      <v-icon color="info">mdi-information</v-icon>
                    </template>
                    <v-list-item-title>路由名稱</v-list-item-title>
                    <v-list-item-subtitle>{{ $route.name }}</v-list-item-subtitle>
                  </v-list-item>
                  
                  <v-list-item v-if="$route.params.id">
                    <template v-slot:prepend>
                      <v-icon color="warning">mdi-pencil</v-icon>
                    </template>
                    <v-list-item-title>編輯模式</v-list-item-title>
                    <v-list-item-subtitle>ID: {{ $route.params.id }}</v-list-item-subtitle>
                  </v-list-item>
                  
                  <v-list-item v-else>
                    <template v-slot:prepend>
                      <v-icon color="success">mdi-plus</v-icon>
                    </template>
                    <v-list-item-title>新增模式</v-list-item-title>
                    <v-list-item-subtitle>準備新增降級記錄</v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>
            
            <v-divider class="my-4" />
            
            <v-row>
              <v-col cols="12">
                <h3>測試狀態</h3>
                <v-list>
                  <v-list-item>
                    <template v-slot:prepend>
                      <v-icon color="success">mdi-check-circle</v-icon>
                    </template>
                    <v-list-item-title>Vue3 組件載入</v-list-item-title>
                    <v-list-item-subtitle>組件成功載入並渲染</v-list-item-subtitle>
                  </v-list-item>
                  
                  <v-list-item>
                    <template v-slot:prepend>
                      <v-icon color="success">mdi-check-circle</v-icon>
                    </template>
                    <v-list-item-title>路由導航</v-list-item-title>
                    <v-list-item-subtitle>路由導航正常工作</v-list-item-subtitle>
                  </v-list-item>
                  
                  <v-list-item>
                    <template v-slot:prepend>
                      <v-icon color="success">mdi-check-circle</v-icon>
                    </template>
                    <v-list-item-title>Composition API</v-list-item-title>
                    <v-list-item-subtitle>Vue3 Composition API 正常運作</v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>
            
            <v-divider class="my-4" />
            
            <v-row>
              <v-col cols="12">
                <h3>下一步</h3>
                <v-alert type="info">
                  <strong>路由測試成功！</strong><br>
                  現在可以開始實現完整的降級表單功能，包括：
                  <ul class="mt-2">
                    <li>表單數據綁定</li>
                    <li>明細檔管理</li>
                    <li>數據驗證</li>
                    <li>API 整合</li>
                  </ul>
                </v-alert>
              </v-col>
            </v-row>
          </v-card-text>
          
          <v-card-actions>
            <v-btn
              color="primary"
              @click="goToFullForm"
              prepend-icon="mdi-arrow-right"
            >
              前往完整表單
            </v-btn>
            
            <v-btn
              color="secondary"
              @click="goBack"
              prepend-icon="mdi-arrow-left"
            >
              返回測試頁面
            </v-btn>
            
            <v-spacer />
            
            <v-chip color="success">
              路由測試: 成功
            </v-chip>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>
    
    <!-- 成功訊息 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          關閉
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

const goToFullForm = () => {
  // 這裡可以導航到完整的降級表單
  showMessage('完整表單功能開發中...', 'info')
}

const goBack = () => {
  router.push('/vue3-test')
}

const showMessage = (message: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}

onMounted(() => {
  const mode = route.params.id ? '編輯' : '新增'
  showMessage(`降級表單${mode}模式載入成功！`, 'success')
})
</script>

<style scoped>
.v-card {
  margin-bottom: 16px;
}

ul {
  padding-left: 20px;
}
</style>
