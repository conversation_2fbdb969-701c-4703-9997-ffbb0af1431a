const router = require("express").Router();
const moment = require('moment');

const relabellinesDB = require("../models/relabellines-model.js");
const categoriesDB = require("../models/categories-model.js");
const remarksDB = require("../models/remarks-model.js");

// GET RELABELLINES BY ID
router.get("/:id", async (req, res) => {
  const relabelId = req.params.id;
  try {
    const relabeline = await relabellinesDB.findById(relabelId);
    if (!relabeline) {
      res.status(404).json({ err: "The relabeline with the specified id does not exist" });
    } else {
      const categories = await categoriesDB.find();
      const remarks = await remarksDB.find();
    relabeline.forEach(mem => {
      mem.workDate = moment(mem.workDate).format("YYYY-M-D HH:mm");
      return mem;
    });
    // Map categories to relabeline
    relabeline.forEach(mem => {
      const category = categories.find(info => info.categoryId === mem.categoryId);
      if (category) {
        mem.categoryName = category.categoryName;
    }
    });
    // Map remarks to relabeline
    relabeline.forEach(mem => {
      const remark = remarks.find(info => info.remarkId === mem.remarkId);
      if (remark) {
        mem.remarkName = remark.remarkName;
      }
    });
      res.status(200).json(relabeline);
    }
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET RELABELLINES BY CODE
router.get("/duplicate/:id", async (req, res) => {
  const codeName = req.params.id;
  try {
    const exist = await relabellinesDB.findByCode(codeName);
    if (Array.isArray(exist) && exist.length > 0) {
      res.status(200).json(exist);
    } else {
      res.status(404).json({ error: "Data not found" });
    }
  } catch (err) {
    console.error(err);
    res.status(500).json({ err: err.message });
  }
});

// INSRT RELABELLINES INTO DB
router.post("/", async (req, res) => {
  const newRelabeline = req.body;
  if (!newRelabeline.relabelId) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const relabeline = await relabellinesDB.addRelabelline(newRelabeline);      
      res.status(201).json(relabeline);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

// REMOVE RELABELLINES INTO DB
router.delete("/:id", async (req, res) => {
  const relabellineId = req.params.id;
  try {
    const deleting = await relabellinesDB.removeRelabelline(relabellineId);
    res.status(204).json(deleting);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;
