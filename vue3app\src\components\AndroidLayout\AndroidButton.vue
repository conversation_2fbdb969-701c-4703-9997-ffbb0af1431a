<template>
  <v-btn
    :color="color"
    :variant="variant"
    :size="responsiveSize"
    :block="responsiveBlock"
    :disabled="disabled"
    :loading="loading"
    :prepend-icon="prependIcon"
    :append-icon="appendIcon"
    :class="buttonClass"
    :style="buttonStyle"
    @click="handleClick"
  >
    <slot />
  </v-btn>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAndroidLayout } from '@/composables/useAndroidLayout'

interface Props {
  // 基本按鈕屬性
  color?: string
  variant?: 'flat' | 'text' | 'elevated' | 'tonal' | 'outlined' | 'plain'
  disabled?: boolean
  loading?: boolean
  prependIcon?: string
  appendIcon?: string
  
  // 響應式屬性覆蓋
  size?: 'x-small' | 'small' | 'default' | 'large' | 'x-large'
  block?: boolean
  
  // Android特定屬性
  forceDesktopStyle?: boolean
  customMinHeight?: number
}

interface Emits {
  (e: 'click', event: MouseEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  color: 'primary',
  variant: 'flat',
  disabled: false,
  loading: false,
  forceDesktopStyle: false
})

const emit = defineEmits<Emits>()

// 使用Android Layout composable
const {
  isMobile,
  buttonConfig,
  buttonClass: androidButtonClass
} = useAndroidLayout()

// 計算響應式尺寸
const responsiveSize = computed(() => {
  if (props.forceDesktopStyle) {
    return props.size || 'default'
  }
  
  return props.size || buttonConfig.value.size
})

// 計算響應式block屬性
const responsiveBlock = computed(() => {
  if (props.forceDesktopStyle) {
    return props.block || false
  }
  
  return props.block !== undefined ? props.block : buttonConfig.value.block
})

// 計算按鈕類別
const buttonClass = computed(() => {
  const classes = [androidButtonClass.value]
  
  // 添加觸摸優化類別
  if (isMobile.value) {
    classes.push('android-touch-button')
  }
  
  return classes.filter(Boolean).join(' ')
})

// 計算按鈕樣式
const buttonStyle = computed(() => {
  const style: Record<string, string> = {}
  
  // 設定最小高度
  const minHeight = props.customMinHeight || buttonConfig.value.minHeight
  if (minHeight) {
    style.minHeight = `${minHeight}px`
  }
  
  // 觸摸優化
  if (isMobile.value) {
    style.touchAction = 'manipulation'
  }
  
  return style
})

// 點擊處理
const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped>
/* Android觸摸按鈕優化 */
.android-touch-button {
  min-width: 44px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  letter-spacing: 0.5px !important;
  text-transform: none !important;
}

/* 觸摸反饋優化 */
.android-touch-button:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* 載入狀態優化 */
.android-touch-button.v-btn--loading {
  pointer-events: none;
}

/* 禁用狀態優化 */
.android-touch-button.v-btn--disabled {
  opacity: 0.6 !important;
}

/* 高對比度模式支援 */
@media (prefers-contrast: high) {
  .android-touch-button {
    border: 2px solid currentColor !important;
  }
}

/* 減少動畫模式支援 */
@media (prefers-reduced-motion: reduce) {
  .android-touch-button {
    transition: none !important;
  }
  
  .android-touch-button:active {
    transform: none !important;
  }
}
</style>
