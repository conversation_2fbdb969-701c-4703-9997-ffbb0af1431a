const router = require("express").Router();
const moment = require('moment');

const worklinesDB = require("../models/worklines-model.js");
const employeesDB = require("../models/employees-model.js");

// GET ALL WORKLINES
router.get("/", async (req, res) => {
  try {
    const worklines = await worklinesDB.find();
    const employees = await employeesDB.findOfTW();
    worklines.map(mem => {
      mem.worklineTime = moment(mem.worklineTime).format("YYYY-M-D HH:mm");
      mem.trackTime = moment(mem.trackTime).format("YYYY-M-D HH:mm");
      return mem;
    });
    worklines.map(mem => {
      return employees.map(info => {
        if (info.Id === mem.employeeId) {
          mem.employeeName = info.firstname + '  ' + info.lastname;
          return mem;
        }
      })
    });
    res.status(200).json(worklines);
  } catch (err) {
    res.status(500).json({ err: err });
  }
});

// GET WORKLINE BY ID
router.get("/:id", async (req, res) => {
  const worklineId = req.params.id;
  try {
    const workline = await worklinesDB.findById(worklineId);
    if (!workline) {
      res.status(404).json({ err: "The specified id does not exist" });
    } else {
    //
    workline.map(mem => {
      mem.worklineTime = moment(mem.worklineTime).format("YYYY-M-D HH:mm");
      mem.trackTime = moment(mem.trackTime).format("YYYY-M-D HH:mm");
      return mem;
    });   
    //
      res.status(200).json(workline);
    }
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// INSERT WORKLINE INTO DB
router.post("/", async (req, res) => {
  const newWorkline = req.body;
  
  const wDate= newWorkline.worklineDate;
  const wTime = newWorkline.worklineTime;
  newWorkline.worklineTime = moment(wDate).format("YYYY-MM-DD") + ' T' + wTime + ':00.000Z';
  if (!newWorkline.workId) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const workline = await worklinesDB.addWorkline(newWorkline);
      res.status(201).json(workline);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

router.put("/:id", async (req, res) => {
  const worklineId = req.params.id;
  const newChanges = req.body;
  if (!newChanges.firstname) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const addChanges = await worklinesDB.updateWorkline(worklineId, newChanges);
      res.status(200).json(addChanges);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

router.delete("/:id", async (req, res) => {
  const worklineId = req.params.id;
  try {
    const deleting = await worklinesDB.removeWorkline(worklineId);
    res.status(204).json(deleting);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;
