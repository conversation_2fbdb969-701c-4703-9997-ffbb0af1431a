const db = require("../config/dbConfig.js");
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET ALL EMPLOYEES
const find = () => {
  return db("ad_user_roles_v")
    .select({
    employId: "ad_user.ad_user_id",
    employNO: "ad_user_roles_v.name",
    userName: "ad_user.description",
    userRole: "ad_user_roles_v.rolename",
    userPassword: "ad_user.password",
    userEmail: "ad_user.email"
    
  })
  .join('ad_user', 'ad_user_roles_v.name', '=', 'ad_user.name')
  .andWhere('ad_user.isactive', '=', 'Y')
  .orderBy('ad_user_roles_v.name','desc')
};

// GET TW EMPLOYEES
const findOfTW = () => {
  return db("ad_user_roles_v")
    .select({
    employId: "ad_user.ad_user_id",
    employNO: "ad_user_roles_v.name",
    userName: "ad_user.description",
    userRole: "ad_user_roles_v.rolename",
    userPassword: "ad_user.password",
    userEmail: "ad_user.email"
    
  })
  .join('ad_user', 'ad_user_roles_v.name', '=', 'ad_user.name')
  .whereIn('ad_user_roles_v.rolename', ['品證課 經辦人員','捻線課 經辦人員']) 
  .andWhere('ad_user.isactive', '=', 'Y')
  .orderBy('ad_user_roles_v.name','desc')
};

// GET QI EMPLOYEES
const findOfQI = () => {
  return db("ad_user_roles_v")
    .select({
    employId: "ad_user.ad_user_id",
    employNO: "ad_user_roles_v.name",
    userName: "ad_user.description",
    userRole: "ad_user_roles_v.rolename",
    userPassword: "ad_user.password",
    userEmail: "ad_user.email"
    
  })
  .join('ad_user', 'ad_user_roles_v.name', '=', 'ad_user.name')
  .where( 'ad_user_roles_v.rolename','like', `%品證%`)
  .andWhere('ad_user.isactive', '=', 'Y')
  .orderBy('ad_user_roles_v.name','desc')
};

// GET QI EMPLOYEES
const findOfPK = () => {
  return db("ad_user_roles_v")
    .select({
    employId: "ad_user.ad_user_id",
    employNO: "ad_user_roles_v.name",
    userName: "ad_user.description",
    userRole: "ad_user_roles_v.rolename",
    userPassword: "ad_user.password",
    userEmail: "ad_user.email"
    
  })
  .join('ad_user', 'ad_user_roles_v.name', '=', 'ad_user.name')
  .where( 'ad_user_roles_v.rolename','like', `%生管課 包裝組%`)
  .andWhere('ad_user.isactive', '=', 'Y')
  .orderBy('ad_user_roles_v.name','desc')
};
// GET SPECIFIC EMPLOYEE BY ID
const findById = id => {
  return db("ad_user_roles_v")
    .select({
    employId: "ad_user.ad_user_id",
    employNO: "ad_user_roles_v.name",
    userName: "ad_user.description",
    userRole: "ad_user_roles_v.rolename",
    userPassword: "ad_user.password",
    userEmail: "ad_user.email"
    
  })
  .join('ad_user', 'ad_user_roles_v.name', '=', 'ad_user.name')
  .where('ad_user.ad_user_id', id )
  .andWhere('ad_user.isactive', '=', 'Y')

  //SQL RAW METHOD
  // return db.raw(`SELECT * FROM employees
  //                  WHERE id = ${id}`);
};

module.exports = {
  find,
  findById,
  findOfTW,
  findOfQI,
  findOfPK
};
