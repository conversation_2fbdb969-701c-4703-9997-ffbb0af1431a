<template>
  <v-list>
    <v-list-item
      :prepend-avatar="userAvatar"
      :title="userDisplayName"
      :subtitle="userRole"
      class="user-info-item"
    >
      <template v-slot:append>
        <!-- 用戶菜單 -->
        <v-menu location="bottom end" v-if="!mini">
          <template v-slot:activator="{ props }">
            <v-btn 
              icon="mdi-dots-vertical" 
              size="small" 
              variant="text"
              v-bind="props"
            />
          </template>
          <v-list>
            <v-list-item
              v-for="item in userMenus"
              :key="item.title"
              :value="item.title"
              @click="$emit('user-action', item)"
            >
              <template v-slot:prepend>
                <v-icon :icon="item.icon" size="small" />
              </template>
              <v-list-item-title>{{ item.title }}</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>

        <!-- 摺疊/展開按鈕 -->
        <v-btn
          :icon="mini ? 'mdi-chevron-right' : 'mdi-chevron-left'"
          size="small"
          variant="text"
          @click="$emit('toggle-mini')"
          v-if="!isMobile"
          class="ml-1"
        />
      </template>

      <!-- Rail 模式下的用戶頭像 -->
      <template v-if="mini && !isMobile">
        <v-tooltip activator="parent" location="end">
          {{ userDisplayName }}
        </v-tooltip>
      </template>
    </v-list-item>

    <!-- 在線狀態指示器 -->
    <v-list-item v-if="!mini" class="py-1">
      <template v-slot:prepend>
        <v-icon 
          :icon="isOnline ? 'mdi-circle' : 'mdi-circle-outline'" 
          :color="isOnline ? 'success' : 'error'"
          size="small"
        />
      </template>
      <v-list-item-title class="text-caption">
        {{ isOnline ? '在線' : '離線' }}
      </v-list-item-title>
      <template v-slot:append>
        <span class="text-caption text-medium-emphasis">
          {{ lastActiveTime }}
        </span>
      </template>
    </v-list-item>
  </v-list>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'

interface AppMenu {
  icon: string
  title: string
  vertical?: string
  link: string
}

interface Props {
  user: any
  userMenus: AppMenu[]
  isMobile: boolean
  mini: boolean
}

interface Emits {
  (e: 'toggle-mini'): void
  (e: 'user-action', item: AppMenu): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 響應式數據
const isOnline = ref(navigator.onLine)
const lastActiveTime = ref(new Date().toLocaleTimeString('zh-TW', { 
  hour: '2-digit', 
  minute: '2-digit' 
}))

// 計算屬性
const userDisplayName = computed(() => {
  if (!props.user) return '訪客'
  const firstName = props.user.firstname || ''
  const lastName = props.user.lastname || ''
  return `${firstName} ${lastName}`.trim() || '用戶'
})

const userRole = computed(() => {
  return props.user?.role || '一般用戶'
})

const userAvatar = computed(() => {
  return props.user?.avatar || '/assets/avatar0.png'
})

// 方法
const updateOnlineStatus = () => {
  isOnline.value = navigator.onLine
}

const updateLastActiveTime = () => {
  lastActiveTime.value = new Date().toLocaleTimeString('zh-TW', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

// 生命週期
onMounted(() => {
  // 監聽網絡狀態變化
  window.addEventListener('online', updateOnlineStatus)
  window.addEventListener('offline', updateOnlineStatus)
  
  // 監聽用戶活動
  const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
  events.forEach(event => {
    document.addEventListener(event, updateLastActiveTime, { passive: true })
  })
  
  // 定期更新時間
  const timeInterval = setInterval(updateLastActiveTime, 60000) // 每分鐘更新
  
  // 清理函數
  onUnmounted(() => {
    window.removeEventListener('online', updateOnlineStatus)
    window.removeEventListener('offline', updateOnlineStatus)
    
    events.forEach(event => {
      document.removeEventListener(event, updateLastActiveTime)
    })
    
    clearInterval(timeInterval)
  })
})

onUnmounted(() => {
  // 清理已在 onMounted 中處理
})
</script>

<style scoped>
/* 用戶信息項目樣式 */
.user-info-item {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  margin-bottom: 8px;
}

.user-info-item :deep(.v-list-item__prepend) {
  margin-right: 12px;
}

.user-info-item :deep(.v-avatar) {
  border: 2px solid rgba(25, 118, 210, 0.2);
  transition: border-color 0.3s ease;
}

.user-info-item:hover :deep(.v-avatar) {
  border-color: rgba(25, 118, 210, 0.5);
}

/* 在線狀態樣式 */
.v-list-item .v-icon {
  transition: color 0.3s ease;
}

/* 深色模式適配 */
@media (prefers-color-scheme: dark) {
  .user-info-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  }
  
  .user-info-item :deep(.v-avatar) {
    border: 2px solid rgba(33, 150, 243, 0.2);
  }
  
  .user-info-item:hover :deep(.v-avatar) {
    border-color: rgba(33, 150, 243, 0.5);
  }
}

/* 動畫效果 */
.user-info-item {
  transition: background-color 0.2s ease;
}

.user-info-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

@media (prefers-color-scheme: dark) {
  .user-info-item:hover {
    background-color: rgba(255, 255, 255, 0.04);
  }
}

/* 響應式調整 */
@media (max-width: 599px) {
  .user-info-item :deep(.v-list-item-title) {
    font-size: 14px;
  }
  
  .user-info-item :deep(.v-list-item-subtitle) {
    font-size: 12px;
  }
}
</style>
