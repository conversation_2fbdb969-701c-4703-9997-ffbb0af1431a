import { getData, putData, postData, deleteData } from '@/utils/backend-api';
import { Employee, Entity } from '@/types';
import { appModule } from './app';
import { getDefaultPagination, getPagination } from '@/utils/store-util';
import { get } from 'lodash';
import { VuexModule, Module, Mutation, Action, getModule } from 'vuex-module-decorators';
import store from '@/store';

export interface EmployeeState {
  items: Entity[];
  pagination: Pagination;
  loading: boolean;
  employee: Employee;
  employees: Employee[];
}

@Module({ store, dynamic: true, name: 'employeeModule' })
class EmployeeModule extends VuexModule implements EmployeeState {
  public items: Entity[] = [];
  public pagination = getDefaultPagination();
  public loading = true;
  public employee = {} as Employee;
  public employees: Employee[] = [];

  get getEmployees() {
    return this.employees;
  }

  @Action
  getEmployeeById(id: string): void {
    this.setLoading(true);
    if (id) {
      getData('employees/' + id).then(
        res => {
          const employee = { ...res.data};
          employee.avatar = `..${employee.avatar}`;
          this.setEmployee(employee);
          this.setLoading(false);
        },
        (err: TODO) => {
          console.log(err);
        }
      );
    } else {
      this.setEmployee({} as Employee);
      this.setLoading(false);
    }
  }

  @Action
  getAllEmployees(): void {
    this.setLoading(true);
    getData('employees').then(res => {
      const employees = res.data;
      this.setDataTable(employees);
      this.setLoading(false);
    });
  }

  @Action
  earchEmployees(searchQuery: string): void {
    this.setLoading(true);
    getData('employees&' + searchQuery).then(res => {
      const employees = res.data;
      employees.forEach((p: TODO) => {
        p.orderAmount = p.orders?.length;
      });
      this.setDataTable(employees);
      this.setLoading(false);
    });
  }

  @Action
  quickSearch(headers: TableHeader[], qsFilter: SeachQuery): void {
    this.setLoading(true);
    getData('employees').then(res => {
      const employees = res.data.filter((r: TODO) =>
        headers.some((header: TODO) => {
          const val = get(r, [header.value]);
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter)) ||
            false
          );
        })
      );
      this.setDataTable(employees);
      this.setLoading(false);
    });
  }

  @Action
  deleteEmployee(id: number): void {
    deleteData(`employees/${id.toString()}`)
      .then(_res => {
        this.getAllEmployees();
        appModule.sendSuccessNotice('Operation is done.');
        appModule.closeNoticeWithDelay(3000);
      })
      .catch((err: TODO) => {
        appModule.sendErrorNotice('Operation failed! Please try again later. ');
        appModule.closeNoticeWithDelay(5000);
      });
  }

  @Action
  saveEmployee(employee: Employee): void {
    if (!employee.id) {
      postData('employees/', employee)
        .then(res => {
          const employee = res.data;
          this.setEmployee(employee);
          appModule.sendSuccessNotice('New record has been added.');
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice('Operation failed! Please try again later. ');
          appModule.closeNoticeWithDelay(5000);
        });
    } else {
      putData('employees/' + employee.id.toString(), employee)
        .then(res => {
          const employee = res.data;
          this.setEmployee(employee);
          appModule.sendSuccessNotice('The record has been updated.');
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice('Operation failed! Please try again later. ');
          appModule.closeNoticeWithDelay(5000);
        });
    }
  }

  @Action
  setDataTable(items: Employee[]) {
    const pagination = getPagination(items);
    this.setPagination(pagination);
    this.setItems(items);
  }

  @Mutation
  setItems(employees: Employee[]): void {
    this.items = employees;
  }

  @Mutation
  setPagination(pagination: TODO): void {
    this.pagination = pagination;

  }
  @Mutation
  setLoading(loading: boolean): void {
    this.loading = loading;
  }

  @Mutation setEmployee(employee: Employee): void {
    this.employee = employee;
  }
}

export const employeeModule = getModule(EmployeeModule);
