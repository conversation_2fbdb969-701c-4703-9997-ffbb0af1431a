const { defineConfig } = require('@vue/cli-service')
const webpack = require('webpack')

module.exports = defineConfig({
  transpileDependencies: true,
  chainWebpack: config => {
    config.module
      .rule('images')
      .test(/\.(png|jpe?g|gif|webp)(\?.*)?$/)
      .use('url-loader')
      .loader('url-loader')
      .options({
        limit: 8000,
        fallback: {
          loader: 'file-loader',
          options: { name: 'img/[name].[hash:8].[ext]' }
        }
      })
      .end()

    config.plugin('fork-ts-checker').tap((args) => {
      args[0] = {
        typescript: {
          memoryLimit: 512
        }
      }
      return args
    })
  },
  configureWebpack: {
    plugins: [
      new webpack.ProvidePlugin({
        process: 'process/browser'
      })
    ],
    resolve: {
      fallback: {
        url: require.resolve('url/'),
        querystring: require.resolve('querystring-es3'),
        process: require.resolve('process/browser')
      }
    }
  }
})
