<template>
  <v-container fluid>
    <v-flex xs12>
      <v-card>
        <v-card-title>
          <span class="title"
            >{{title}} {{ pagination ? "(" + pagination.totalItems + ")" : "" }}
          </span>
          <v-spacer></v-spacer>
          <table-header-buttons :reloadData="clearData"></table-header-buttons>
        </v-card-title>
        <v-card-text>
          <v-text-field
            ref="searchInput"
            v-model="searchFilter.contain.trackNO"
            append-icon="mdi-magnify"
            label="傳票QRCode 或 Cap QRCode"
            @change="getTrack"
            counter="27"
            variant="outlined"
            single-line
            hide-details
          ></v-text-field>
        </v-card-text>
        <Table
          v-if="loading === false"
          :headers="selectedHeaders"
          :items="items"
          :pagination="pagination"
          :setSearch="false"
          :setEdit="false"
          :setRemove="false"
          :disableSort="true"
        ></Table>
      </v-card>
    </v-flex>
    <search-panel
      :rightDrawer="rightDrawer"
      @cancelSearch="cancelSearch"
      @searchData="searchTracksheets"
    >
    </search-panel>
    <v-snackbar
      v-if="loading === false"
      location="top end"
      :timeout="5000"
      :color="mode"
      :model-value="snackbar"
      @update:model-value="updateSnackbar"
    >
      {{ notice }}
      <template v-slot:actions>
        <v-btn variant="text" @click="closeSnackbar">Close</v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script lang="ts">
import { Component, Vue, Ref } from 'vue-facing-decorator'
import Table from '@/components/Table.vue'
import TableHeaderButtons from '@/components/TableHeaderButtons.vue'
import SearchPanel from '@/components/SearchPanel.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import { debounce } from 'lodash'
import {
  buildSearchFilters,
  buildJsonServerQuery,
  clearSearchFilters
} from '@/utils/app-util'
import { productModule } from '@/store/modules/products'
import { tracksheetModule } from '@/store/modules/tracksheets'
import { useAppStore } from '@/stores/app'

interface Header {
  text: string
  left?: boolean
  value: string
  sortable?: boolean
}

@Component({
  name: 'TrackSheetList',
  components: {
    Table,
    TableHeaderButtons,
    SearchPanel,
    ConfirmDialog
  }
})
export default class TrackSheetList extends Vue {
  @Ref('searchInput') searchInput!: HTMLElement

  private appStore = useAppStore()

  private dialog = false
  private dialogTitle = "追踪傳票刪除確認"
  private dialogText = "確定要刪除此記錄?"
  private showSearchPanel = false
  private right = true
  private search = ""

  private headers: Header[] = [
    { text: "追踨傳票單號", left: true, value: "tracksheetNO" },
    { text: "日期時間", left: true, value: "tracksheetTime" },
    { text: "爐別", left: true, value: "furnaceName" },
    { text: "品種", left: true, value: "productName" },
    { text: "個數", left: true, value: "tracksheetQty" },
    { text: "淨重(kg)", left: true, value: "tracksheetNetWeight" },
    { text: "", value: "actions", sortable: false }
  ]

  private yarnQRcodeHeaders: Header[] = [
    { text: "BI檢測", value: "biName" },
    { text: "TEX檢測", value: "texName" },
    { text: "Lot NO", value: "batchName" },
    { text: "乾燥時間(hrs)", value: "dryTime" },
    { text: "過磅日期", left: true, value: "productDate" },
    { text: "品種", left: true, value: "productName" },
    { text: "等級", value: "gradeName" },
    { text: "爐別", left: true, value: "furnaceName" },
    { text: "Bushing NO", value: "bushingNO" },
    { text: "Cake位置", value: "positionName" },
    { text: "Cake重量(g)", value: "cakeWeight" },
    { text: "開機日期", left: true, value: "workDate" },
    { text: "Twister NO", value: "twisterNO" },
    { text: "Spindle NO", value: "spindleNO" },
    { text: "Is MFD", value: "isMFD" },
    { text: "", value: "actions", sortable: false }
  ]

  private cakeTrackSheetHeaders: Header[] = [
    { text: "追踪傳票單號", left: true, value: "tracksheetNO" },
    { text: "日期時間", left: true, value: "tracksheetTime" },
    { text: "爐別", left: true, value: "furnaceName" },
    { text: "品種", left: true, value: "productName" },
    { text: "等級", left: true, value: "gradeName" },
    { text: "個數", left: true, value: "tracksheetQty" },
    { text: "淨重(kg)", left: true, value: "tracksheetNetWeight" },
    { text: "", value: "actions", sortable: false }
  ]

  private yarnTrackSheetHeaders: Header[] = [
    { text: "追踪傳票單號", left: true, value: "documentNO" },
    { text: "日期時間", left: true, value: "tracksheetTime" },
    { text: "品種", left: true, value: "productName" },
    { text: "Twister NO", value: "twisterNO" },
    { text: "開機T1個數", left: true, value: "trackT1Qty" },
    { text: "開機T2個數", left: true, value: "trackT2Qty" },
    { text: "品檢T1小計", left: true, value: "icountT1Sum" },
    { text: "品檢T2小計", left: true, value: "icountT2Sum" },
    { text: "追踪傳票序號", left: true, value: "tracksheetNO" },
    { text: "爐別", left: true, value: "furnaceName" },
    { text: "品檢T1個數", left: true, value: "icountT1Qty" },
    { text: "品檢T2個數", left: true, value: "icountT2Qty" },
    { text: "", value: "actions", sortable: false }
  ]

  private searchFilter = { contain: { trackNO: "" } }

  private title = ""
  private type = ""
  private trackId = ""
  private tracks: string[] = []
  private isYarnQRcode: boolean | null = null
  private isCakeTrackSheetNO: boolean | null = null
  private isYarnTrackSheetNO: boolean | null = null
  private query = ""
  private timeout = 5000
  private color = ""
  private quickSearchFilter = ""
  private itemId = -1

  get selectedHeaders() {
    if (this.isCakeTrackSheetNO) {
      return this.cakeTrackSheetHeaders
    } else if (this.isYarnTrackSheetNO) {
      return this.yarnTrackSheetHeaders
    } else if (this.isYarnQRcode) {
      return this.yarnQRcodeHeaders
    }
    return this.headers
  }

  async getTrack() {
    buildSearchFilters(this.searchFilter)

    const trackNO = this.searchFilter.contain.trackNO
    const trimmedTrackNO = trackNO.trim()

    if (!trimmedTrackNO) {
      this.appStore.sendErrorNotice("請輸入傳票單號or Cap QRCode")
      this.appStore.closeNoticeWithDelay(3000)
      return ""
    }

    this.isYarnQRcode = /^\d{14}[a-zA-Z0-9\s.]*$/.test(trimmedTrackNO)
    this.isCakeTrackSheetNO = !!(
      /^F\d{11}\s*,[A-Za-z0-9]{10}$/.test(trimmedTrackNO) ||
      /^D\d{11}\s*$/.test(trimmedTrackNO)
    )
    this.isYarnTrackSheetNO = /^G\d{12}-\d{1}$/.test(trimmedTrackNO) || /^G\d{12}-\d{1}-S\d{2}$/.test(trimmedTrackNO)

    if (!this.isYarnQRcode && !this.isCakeTrackSheetNO && !this.isYarnTrackSheetNO) {
      this.appStore.sendErrorNotice("無效的傳票單號or Cap QRCode!")
      this.appStore.closeNoticeWithDelay(3000)
      productModule.clearProducts()
      tracksheetModule.clearTracksheets()
      return ""
    }

    if (this.isYarnQRcode) {
      this.query = trimmedTrackNO.slice(0, 14)
      await productModule.getProductById(this.query)
      return productModule.product
    }

    if (this.isCakeTrackSheetNO) {
      this.type = "CAKE"
      this.trackId = trimmedTrackNO.slice(0, 12)
      this.query = trimmedTrackNO.slice(1, 12)
      this.tracks = [this.type, this.query, this.trackId]
      await tracksheetModule.getTracksheetByCode(this.tracks)
      return tracksheetModule.tracksheet
    }

    if (this.isYarnTrackSheetNO) {
      this.type = "QI"
      this.trackId = trimmedTrackNO.slice(0, 15)
      this.query = trimmedTrackNO.slice(1, 13)
      this.tracks = [this.type, this.query, this.trackId]
      await tracksheetModule.getTracksheetByCode(this.tracks)
      return tracksheetModule.tracksheet
    }
  }

  searchTracksheets() {
    this.showSearchPanel = !this.showSearchPanel
    buildSearchFilters(this.searchFilter)
    this.query = buildJsonServerQuery(this.searchFilter)
    this.quickSearch = ""
    tracksheetModule.searchTracksheets(this.query)
  }

  clearSearchFilters() {
    this.showSearchPanel = !this.showSearchPanel
    clearSearchFilters(this.searchFilter)
  }

  clearData() {
    this.query = ""
    this.searchFilter.contain.trackNO = ""
    productModule.clearProducts()
    tracksheetModule.clearTracksheets()
    this.$nextTick(() => {
      if (this.searchInput) {
        this.searchInput.focus()
      }
    })
  }

  updateSearchPanel() {
    this.rightDrawer = !this.rightDrawer
  }

  cancelSearch() {
    this.showSearchPanel = false
  }

  closeSnackbar() {
    this.appStore.closeNotice()
  }

  updateSnackbar(value: boolean) {
    if (!value) {
      this.appStore.closeNotice()
    }
  }

  private quickSearchTrackSheets = debounce(function(this: TrackSheetList) {
    tracksheetModule.quickSearch(this.headers, this.quickSearchFilter)
  }, 500)

  get items() {
    if (this.isYarnQRcode) {
      return productModule.items
    }
    return tracksheetModule.items
  }

  get pagination() {
    if (this.isYarnQRcode) {
      return productModule.pagination
    }
    return tracksheetModule.pagination
  }

  get loading() {
    return this.appStore.loading
  }

  get mode() {
    return this.appStore.mode
  }

  get snackbar() {
    return this.appStore.snackbar
  }

  get notice() {
    return this.appStore.notice
  }

  get rightDrawer() {
    return this.showSearchPanel
  }

  set rightDrawer(value: boolean) {
    this.showSearchPanel = value
  }

  get quickSearch() {
    return this.quickSearchFilter
  }

  set quickSearch(val: string) {
    this.quickSearchFilter = val
    if (this.quickSearchFilter) {
      this.quickSearchTrackSheets()
    }
  }

  created() {
    productModule.clearProducts()
    tracksheetModule.clearTracksheets()
  }

  mounted() {
    window.scrollTo(0, 0)
    this.$nextTick(() => {
      this.title = "EM即時生產查詢"
      this.$nextTick(() => {
        if (this.searchInput) {
          this.searchInput.focus()
        }
      })
    })
  }
}
</script>
