const db = require("../config/dbConfig.js");
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET SPECIFIC INSPECTLINE BY ID
const findById = id => {
  return db("aits_inspectlines")
    .select({
    id: "aits_inspectlines.id",
    inspectId: "aits_inspectlines.inspectid",
    furnaceName: "aits_inspectlines.furnacename",
    productName: "aits_inspectlines.productname",
    productDate: "aits_inspectlines.productdate",
    gradeName: "aits_inspectlines.gradename",
    categoryId: "aits_inspectlines.categoryid",
    remarkId: "aits_inspectlines.remarkid",
    bushingNO: "aits_inspectlines.bushingno",
    positionName: "aits_inspectlines.positionname",
    cakeWeight: "aits_inspectlines.cakeweight",
    workDate: "aits_inspectlines.workdate",
    twisterNO: "aits_inspectlines.twisterno",
    spindleNO: "aits_inspectlines.spindleno",
    texName: "aits_inspectlines.texname",
    biName: "aits_inspectlines.biname",
    batchName: "aits_inspectlines.batchname",
    codeName: "aits_inspectlines.codename",
    m_product_id: "aits_inspectlines.m_product_id",
    m_twqrcodeline_id: "aits_inspectlines.m_twqrcodeline_id"
    
    }) 
    .where("aits_inspectlines.inspectid", id)
    .orderBy("id", "desc") 
    .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

// GET SPECIFIC INSPECTLINE BY CODE
const findByCode = code => {
  return db("aits_inspectlines")
    .select({
    codeName: "aits_inspectlines.codename"    
    }) 
    .where(db.raw("TRIM(aits_inspectlines.codename)"), code)
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
  });
};

// ADD A INSPECTLINE
const addInspectline = inspectline => {
  return db("aits_inspectlines").
    insert({     
      inspectid: inspectline.inspectId,
      furnacename: inspectline.furnaceName,
      productname: inspectline.productName,
      productdate: inspectline.productDate,
      gradename: inspectline.gradeName,
      categoryid: inspectline.categoryId,
      remarkid: inspectline.remarkId,
      bushingno: inspectline.bushingNO,
      positionname: inspectline.positionName,
      cakeweight: inspectline.cakeWeight,
      workdate: inspectline.workDate,
      twisterno: inspectline.twisterNO,
      spindleno: inspectline.spindleNO,
      texname: inspectline.texName,
      biname: inspectline.biName,
      batchname: inspectline.batchName,
      codename: inspectline.codeName,      
      m_product_id: inspectline.m_product_id,
      m_twqrcodeline_id: inspectline.m_twqrcodeline_id,
      created: db.fn.now()
    }, "id")
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

// REMOVE INSPECTLINE
const removeInspectline = id => {
  let result;
  return db.transaction(trx => {
    return db("aits_inspectlines")
      .where("id", id)
      .then(inspectline => {
        result = inspectline;     
        if (inspectline) { 
          return trx("aits_inspectlines") 
            .where("aits_inspectlines.id", id) 
            .del();
        } 
      }) 
      .then(trx.commit)
      .catch(trx.rollback);
    })
    .then(() => { 
      infoLogger.info(`remove inspectline content: ${JSON.stringify(result)}`)
    })
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

module.exports = {
  findById,
  findByCode,
  addInspectline,
  removeInspectline
};
