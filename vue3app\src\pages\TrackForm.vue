<template>
  <!-- Previous template content remains unchanged -->
</template>

<script lang="ts">
import { Component, Vue } from 'vue-facing-decorator'
import { useRouter, useRoute } from 'vue-router'
import { trackModule } from '@/store/modules/tracks'
import { appModule } from '@/stores/app'
import { Track } from '@/types'

@Component({
  name: 'TrackForm'
})
export default class TrackForm extends Vue {
  private router = useRouter()
  private route = useRoute()

  private dateMenu = false
  private errors: string[] = []
  private title = 'Track（新增）'

  private track: Track = {
    id: null,
    value: null,
    trackDate: new Date().toISOString().substr(0, 10),
    shiftName: '',
    qrCode: '',
    furnaceName: '',
    productName: '',
    twistName: '',
    trackTime: '',
    twisterNO: '',
    trackT1Qty: 0,
    trackT2Qty: 0,
    abnormalityId: 0,
    abnormalityName: '',
    dispositionId: 0,
    dispositionName: '',
    remark: '',
    furnaceProduct: ''
  }

  private rules = {
    required: [(v: any) => !!v || '此欄位為必填']
  }

  get loading() {
    return trackModule.loading
  }

  get mode() {
    return appModule.mode
  }

  get snackbar() {
    return appModule.snackbar
  }

  get notice() {
    return appModule.notice
  }

  get abnormalities() {
    return trackModule.abnormalities
  }

  get dispositions() {
    return trackModule.dispositions
  }

  get isValid() {
    return !!(
      this.track.trackDate &&
      this.track.shiftName &&
      this.track.qrCode &&
      this.track.abnormalityId &&
      this.track.dispositionId
    )
  }

  updateSnackbar(value: boolean) {
    if (!value) {
      appModule.closeNotice()
    }
  }

  async save() {
    try {
      await trackModule.saveTrack(this.track)
      this.router.push({ name: 'tracks' })
    } catch (err: any) {
      console.error('Error:', err.message)
    }
  }

  cancel() {
    this.router.push({ name: 'tracks' })
  }

  async scanQRCode() {
    if (this.track.qrCode) {
      try {
        const result = await trackModule.getTrackInfoByQRCode(this.track.qrCode)
        if (result && result.data) {
          this.track.furnaceProduct = result.data.furnaceProduct
        }
      } catch (err: any) {
        appModule.sendErrorNotice('無效的QR Code')
        this.track.furnaceProduct = ''
      }
    }
  }

  closeSnackbar() {
    appModule.closeNotice()
  }

  async created() {
    await Promise.all([
      trackModule.getAbnormalities(),
      trackModule.getDispositions()
    ])

    const id = this.route.params.id
    if (id) {
      this.title = 'Track（編輯）'
      trackModule.getTrackById(id as string)
    }
  }
}
</script>
