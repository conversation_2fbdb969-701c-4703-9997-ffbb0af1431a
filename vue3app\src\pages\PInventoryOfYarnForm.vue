<template>
  <v-container fluid>
    <v-card class="grey lighten-4 elevation-0">
      <v-form ref="validForm" v-model="formValid" lazy-validation>
        <v-card-title class="title">
          {{ title }}
          <v-spacer></v-spacer>
          <v-btn
            :elevation="4"
            icon
            size="small"
            color="grey"
            class="mr-2"
            @click="cancel()"
          >
            <v-icon>mdi-close-circle-outline</v-icon>
          </v-btn>
          <v-btn
            :elevation="4"
            icon
            size="small"
            color="purple"
            class="mr-2"
            @click="save()"
            :disabled="isSaving || !formValid"
            :loading="isSaving"
          >
            <v-icon>mdi-content-save-all</v-icon>
          </v-btn>
          <v-btn
            :elevation="4"
            icon
            size="small"
            color="blue"
            @click="addTracksheet()"
          >
            <v-icon>mdi-plus</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-container fluid grid-list-md>
            <v-row>
              <v-col md="4" cols="12">
                <v-text-field
                  name="id"
                  label="單號"
                  type="number"
                  hint="PInventoryID is required"
                  v-model="pinventory.id"
                  class="input-group--focused"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col md="4" cols="12">
                <v-menu
                  :close-on-content-click="false"
                  v-model="classDateMenu"
                  transition="v-scale-transition"
                  offset-y
                  :nudge-left="40"
                  max-width="290px"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      v-bind="props"
                      label="日期"
                      v-model="pinventory.classDate"
                      prepend-icon="mdi-calendar"
                      readonly
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="pinventory.classDate"
                    no-title
                    scrollable
                  >
                  </v-date-picker>
                </v-menu>
              </v-col>
              <v-col md="4" cols="12">
                <v-radio-group
                  name="shiftName"
                  label="勤別"
                  v-model="pinventory.shiftName"
                  :rules="[value => !!value || '必要!!請選擇']"
                  required
                  row
                >
                  <v-radio label="I" value="1"></v-radio>
                  <v-radio label="II" value="2"></v-radio>
                  <v-radio label="III" value="3"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col md="6" cols="12">
                <v-autocomplete
                  :items="employees"
                  label="人員"
                  item-title="employName"
                  item-value="employId"
                  v-model="pinventory.employId"
                  :rules="[value => !!value || '必要!!請選擇']"
                  required
                ></v-autocomplete>
              </v-col>
              <v-col md="4" cols="12">
                <v-radio-group
                  name="groupName"
                  label="組別"
                  v-model="pinventory.groupName"
                  :rules="[value => !!value || '必要!!請選擇']"
                  required
                  row
                >
                  <v-radio label="A" value="A"></v-radio>
                  <v-radio label="B" value="B"></v-radio>
                  <v-radio label="C" value="C"></v-radio>
                  <v-radio label="D" value="D"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col md="4" cols="12">
                <v-text-field
                  name="quantity"
                  label="小計"
                  type="number"
                  v-model="pinventory.quantity"
                  class="input-group--focused"
                  readonly
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-card>
                  <v-data-table
                    v-if="!loading"
                    :headers="headers"
                    :items="pinventory.pinventorylines || []"
                    :items-per-page="10"
                    class="elevation-1"
                  >
                    <template v-slot:item.actions="{ item }">
                      <v-btn
                        :elevation="4"
                        icon
                        size="x-small"
                        color="red"
                        @click="remove(item)"
                      >
                        <v-icon>mdi-delete</v-icon>
                      </v-btn>
                    </template>
                  </v-data-table>
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
      </v-form>
    </v-card>
    <!-- 新增明細檔對話框 -->
    <v-dialog v-model="addTracksheetModal" width="700" persistent>
      <v-form ref="validDetail" v-model="detailValid" lazy-validation>
        <v-card>
          <v-card-title>
            {{ modalTitle }}
            <v-spacer></v-spacer>
            <v-card-actions>
              <v-btn
                color="success"
                variant="text"
                :disabled="!detailValid"
                @click="savePInventoryline"
              >
                Confirm
              </v-btn>
              <v-btn
                color="warning"
                variant="text"
                @click="cancelAddTracksheet"
              >
                Cancel
              </v-btn>
            </v-card-actions>
          </v-card-title>
          <v-card-text>
            1.請掃Yarn傳票QRCode 2.輸入個數
            <v-text-field
              ref="qrCodeInput"
              v-model="searchFilter.contain.tracksheetNO"
              append-icon="mdi-magnify"
              label="Yarn傳票QRCode"
              @keydown.enter.prevent="getTracksheet"
              @input="handleQRCodeInput"
              counter="19"
              :rules="[value => !!value || '必要!!請選擇']"
              required
            ></v-text-field>
            <v-container fluid grid-list-md>
              <v-row>
                <v-col
                  md="6"
                  cols="12"
                  v-for="(item, index) in tracksheet"
                  :key="index"
                >
                  <v-text-field
                    v-model="item.tracksheetNO"
                    label="單號"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.furnaceName"
                    label="爐別"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.productName"
                    label="品種"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    label="盤點T1個數"
                    v-model="item.pinventoryT1Qty"
                  ></v-text-field>
                  <v-text-field
                    label="盤點T2個數"
                    v-model="item.pinventoryT2Qty"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-container>
          </v-card-text>
        </v-card>
      </v-form>
    </v-dialog>
    <!-- 刪除確認對話框 -->
    <v-dialog v-model="dialog" max-width="400">
      <v-card>
        <v-card-title>{{ dialogTitle }}</v-card-title>
        <v-card-text>{{ dialogText }}</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey" variant="text" @click="onCancel">取消</v-btn>
          <v-btn color="error" variant="text" @click="onConfirm">確認</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Snackbar for notifications -->
    <v-snackbar
      v-model="snackbar.show"
      location="top end"
      :timeout="5000"
      :color="snackbar.color"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn variant="text" @click="snackbar.show = false">Close</v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { usePInventoriesStore } from '@/stores/pinventories'
import { useAppStore } from '@/stores/app'
import { useTracksheetsStore } from '@/stores/tracksheets'
import { buildSearchFilters, getISOClassDate } from '@/utils/app-util'
import type { PInventory, PInventoryline, Tracksheet } from '@/types'

interface Header {
  title: string
  key: string
  align?: string
  sortable?: boolean
}

interface ValidForm {
  validate: () => boolean
  resetValidation: () => void
}

// Stores
const router = useRouter()
const route = useRoute()
const pinventoriesStore = usePInventoriesStore()
const appStore = useAppStore()
const tracksheetsStore = useTracksheetsStore()

// Template refs
const validForm = ref<ValidForm>()
const validDetail = ref<ValidForm>()
const qrCodeInput = ref<HTMLElement>()

// Reactive data
const modalTitle = ref("新增Yarn盤點(明細)")
const addTracksheetModal = ref(false)
const dialog = ref(false)
const dialogTitle = ref("Yarn盤點(明細)刪除確認")
const dialogText = ref("刪除該筆記錄?")
const classDateMenu = ref(false)
const formValid = ref(false)
const detailValid = ref(false)
const title = ref("")
const type = ref("YARN")
const trackId = ref("")
const tracks = ref<string[]>([])
const isYarnTrackSheetNO = ref<boolean | null>(null)
const pinventoryId = ref<number | null>(null)
const selectedPInventoryline = ref<any>(null)
const query = ref("")
const funo = ref("")
const isSaving = ref(false)

const snackbar = ref({
  show: false,
  message: "",
  color: "error"
})

const searchFilter = ref({ contain: { tracksheetNO: "" } })

// Headers for the data table
const headers: Header[] = [
  { title: "序號", key: "countdown", align: "start" },
  { title: "傳票單號", key: "tracksheetNO", align: "start" },
  { title: "爐別", key: "furnaceName", align: "start" },
  { title: "品種", key: "productName", align: "start" },
  { title: "盤點T1個數", key: "pinventoryT1Qty", align: "start" },
  { title: "盤點T2個數", key: "pinventoryT2Qty", align: "start" },
  { title: "操作", key: "actions", sortable: false }
]

// 計算屬性 - 從 store 獲取數據
const employees = computed(() => pinventoriesStore.employees)
const tracksheet = computed(() => tracksheetsStore.tracksheets || [])
const loading = computed(() => pinventoriesStore.isLoading)

// 使用本地響應式數據來避免直接修改 store 的計算屬性
const pinventory = ref({} as any)

// 監聽 store 的變化並同步到本地
const storePInventory = computed(() => pinventoriesStore.currentPInventory)
const isUpdatingFromStore = ref(false)

// 監聽 store 中的 pinventory 變化
watch(storePInventory, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0 && !isUpdatingFromStore.value) {
    // 創建深拷貝以避免響應性問題
    pinventory.value = JSON.parse(JSON.stringify(newValue))
    // 計算個數（明細檔數量）
    if (pinventory.value.pinventorylines) {
      pinventory.value.quantity = pinventory.value.pinventorylines.length
    }
  }
}, { immediate: true, deep: true })

// 監聽路由變化
watch(() => route.params.id, async (newId, oldId) => {
  console.log('路由 ID 變化:', { oldId, newId })
  if (newId && newId !== oldId) {
    // 路由從新增頁面跳轉到編輯頁面
    title.value = "Yarn盤點(明細)"

    // 檢查是否已經有相同 ID 的數據
    const currentId = pinventoriesStore.pinventoryId
    const routeId = Number(newId)

    if (currentId !== routeId) {
      console.log('路由變化：載入編輯頁面數據，ID:', routeId)
      await pinventoriesStore.getPInventoryById(newId as string)
    } else {
      console.log('路由變化：使用現有數據，ID:', currentId)
    }
  } else if (!newId) {
    // 跳轉到新增頁面
    title.value = "Yarn盤點(新增)"
    initializeNewForm()
  }
}, { immediate: false })

// Methods
const showError = (message: string) => {
  snackbar.value = {
    show: true,
    message,
    color: "error"
  }
}

const showSuccess = (message: string) => {
  snackbar.value = {
    show: true,
    message,
    color: "success"
  }
}

const save = async () => {
  if (isSaving.value) return

  console.log('開始儲存，設置 isSaving = true')
  isSaving.value = true
  isUpdatingFromStore.value = true

  try {
    // 準備要保存的數據
    const pinventoryData = {
      ...pinventory.value,
      typeName: type.value
    }

    if (!pinventoryData.id || pinventoryData.id <= 0) {
      // 新增模式
      console.log('執行新增模式儲存')
      await pinventoriesStore.savePInventory(pinventoryData)

      // 儲存成功後，更新本地的 pinventory 資料，包含新的 ID
      const savedPInventory = pinventoriesStore.currentPInventory
      if (savedPInventory && savedPInventory.id) {
        pinventory.value = { ...pinventory.value, id: savedPInventory.id }
        console.log('新增成功，更新本地 ID:', savedPInventory.id)
      }

      saveRoute()
    } else {
      // 編輯模式
      console.log('執行編輯模式儲存，ID:', pinventoryData.id)
      await pinventoriesStore.savePInventory(pinventoryData)
      appStore.sendSuccessNotice("保存成功!")
    }
  } catch (error: any) {
    console.error("Error:", error.message)
    appStore.sendErrorNotice("保存失敗: " + error.message)
  } finally {
    // 重置標誌
    console.log('儲存完成，設置 isSaving = false')
    isSaving.value = false
    setTimeout(() => {
      isUpdatingFromStore.value = false
    }, 100)
  }
}

const saveRoute = () => {
  const id = pinventoriesStore.pinventoryId
  if (id !== null) {
    router.push(`/pinventoryofyarn/${id}`)
  }
}

const cancel = () => {
  router.push({ name: "pinventoriesofyarn" })
}

const getPInventoryById = async () => {
  const id = route.params.id
  if (id) {
    await pinventoriesStore.getPInventoryById(id as string)
  }
}

const addTracksheet = () => {
  addTracksheetModal.value = true
  isYarnTrackSheetNO.value = null
  query.value = ""
  searchFilter.value.contain.tracksheetNO = ""
  pinventoryId.value = pinventory.value.id
  tracksheetsStore.clearTracksheets()

  nextTick(() => {
    if (qrCodeInput.value) {
      qrCodeInput.value.focus()
    }
  })
}

const handleQRCodeInput = (value: string) => {
  if (value && value.length === 19) {
    getTracksheet()
  }
}

const getTracksheet = async () => {
  const tracksheetNO = searchFilter.value.contain.tracksheetNO
  const trimmedTracksheetNO = tracksheetNO.trim()

  if (!trimmedTracksheetNO) {
    showError("請輸入追蹤單號!")
    return ""
  }

  // 驗證 Yarn 傳票 QR Code 格式
  isYarnTrackSheetNO.value = /^G\d{12}-\d{1}$/.test(trimmedTracksheetNO) || /^G\d{12}-\d{1}-S\d{2}$/.test(trimmedTracksheetNO)

  if (!isYarnTrackSheetNO.value) {
    showError("無效的追蹤單號!")
    return ""
  }

  try {
    if (isYarnTrackSheetNO.value) {
      // 按照 vue2app 的邏輯解析 QR Code
      trackId.value = trimmedTracksheetNO.slice(0, 19).trim()
      query.value = trimmedTracksheetNO.slice(1, 13)
      funo.value = trimmedTracksheetNO.slice(16, 19)
      tracks.value = [type.value, query.value, trackId.value]

      // 檢查重複
      const isDuplicate = await pinventoriesStore.getDuplicatePInventorylineByCode(tracks.value)
      if (isDuplicate) {
        showError("重複的追蹤單號!")
        return ""
      }

      // 獲取 tracksheet 數據
      await tracksheetsStore.getTracksheetByCode(tracks.value)
      const tracksheetArray = tracksheetsStore.tracksheets

      if (!tracksheetArray || tracksheetArray.length === 0 || !tracksheetArray[0]) {
        showError("查無資料!")
        return ""
      } else {
        // 如果有 funo，設置爐別名稱
        if (funo.value) {
          tracksheetArray[0].furnaceName = funo.value
        }
        showSuccess("成功載入傳票資料!")
        return tracksheetArray
      }
    }
  } catch (error: any) {
    console.error('Get tracksheet error:', error)
    showError("載入傳票資料失敗，請稍後再試")
  }
}

const remove = (item: any) => {
  selectedPInventoryline.value = item
  dialog.value = true
}

const onConfirm = async () => {
  try {
    if (selectedPInventoryline.value && selectedPInventoryline.value.id) {
      await pinventoriesStore.deletePInventoryline(selectedPInventoryline.value.id)
      selectedPInventoryline.value = null
      await getPInventoryById()
      dialog.value = false
      showSuccess("刪除成功")
    }
  } catch (error: any) {
    console.error('Delete error:', error)
    showError("刪除失敗，請稍後再試")
    appStore.sendErrorNotice("刪除失敗，請稍後再試")
  }
}

const onCancel = () => {
  selectedPInventoryline.value = null
  dialog.value = false
}

const cancelAddTracksheet = () => {
  addTracksheetModal.value = false
  searchFilter.value.contain.tracksheetNO = ""
  tracksheetsStore.clearTracksheets()
}

const resetForm = () => {
  isYarnTrackSheetNO.value = null
  query.value = ""
  searchFilter.value.contain.tracksheetNO = ""
  funo.value = ""
  tracksheetsStore.clearTracksheets()

  if (validDetail.value) {
    validDetail.value.resetValidation()
  }
}

const savePInventoryline = async () => {
  const tracksheetArray = tracksheetsStore.tracksheets

  if (!tracksheetArray || tracksheetArray.length === 0 || !tracksheetArray[0]) {
    showError("請先載入傳票資料!")
    return
  }

  try {
    // 如果主檔還沒有保存（新增模式），先保存主檔
    if (!pinventory.value.id || pinventory.value.id <= 0) {
      console.log('主檔尚未保存，先保存主檔')

      // 驗證主檔必填欄位
      if (!pinventory.value.shiftName || !pinventory.value.employId || !pinventory.value.groupName) {
        showError("請先填寫完整的主檔資料（勤別、人員、組別）!")
        return
      }

      // 保存主檔
      const pinventoryData = {
        ...pinventory.value,
        typeName: type.value
      }

      await pinventoriesStore.savePInventory(pinventoryData)

      // 更新本地的 pinventory 資料，包含新的 ID
      const savedPInventory = pinventoriesStore.currentPInventory
      if (savedPInventory && savedPInventory.id) {
        pinventory.value = { ...pinventory.value, id: savedPInventory.id }
        console.log('主檔保存成功，ID:', savedPInventory.id)

        // 更新標題為編輯模式
        title.value = "Yarn盤點(明細)"

        // 更新路由到編輯模式（不觸發頁面重新載入）
        router.replace(`/pinventoryofyarn/${savedPInventory.id}`)
      } else {
        showError("主檔保存失敗!")
        return
      }
    }

    // 現在保存明細檔
    const PInventoryId = { pinventoryId: pinventory.value.id }
    const addTracksheetNO = { tracksheetNO: trackId.value }
    const addTracksheet = tracksheetArray[0]

    console.log('準備保存明細檔:', {
      PInventoryId,
      addTracksheetNO,
      addTracksheet,
      pinventoryId: pinventory.value.id
    })

    const newPInventoryline = {
      ...PInventoryId,
      ...addTracksheet,
      ...addTracksheetNO,
      pinventoryT1Qty: addTracksheet.pinventoryT1Qty || 0,
      pinventoryT2Qty: addTracksheet.pinventoryT2Qty || 0,
      countdown: 0
    }

    console.log('最終明細檔數據:', newPInventoryline)

    await pinventoriesStore.addPInventorylineToPInventory(newPInventoryline)

    await getPInventoryById()
    resetForm()
    addTracksheetModal.value = false
    appStore.sendSuccessNotice("新增成功!")
  } catch (error: any) {
    console.error('Add PInventoryline error:', error)
    showError("新增失敗，請稍後再試")
    appStore.sendErrorNotice("新增失敗，請稍後再試")
  }
}

const clearForm = () => {
  pinventoriesStore.clearPInventorylines()
  tracksheetsStore.clearTracksheets()
}

// Lifecycle hooks
onMounted(async () => {
  await pinventoriesStore.getEmployees()

  window.scrollTo(0, 0)
  if (route.params.id) {
    title.value = "Yarn盤點(明細)"
    // 檢查是否已經有相同 ID 的數據，避免不必要的重新載入
    const currentId = pinventoriesStore.pinventoryId
    const routeId = Number(route.params.id)

    if (currentId !== routeId) {
      // 只有當 ID 不同時才重新載入數據
      console.log('載入編輯頁面數據，ID:', routeId)
      await pinventoriesStore.getPInventoryById(route.params.id as string)
    } else {
      console.log('使用現有數據，ID:', currentId)
    }
  } else {
    title.value = "Yarn盤點(新增)"
    // 新增模式：確保完全重置狀態
    pinventoriesStore.resetState()

    // 設置新的空白資料，不設定預設值
    const newPInventory = {
      id: null,
      typeName: type.value,
      classDate: new Date().toISOString().slice(0, 10),
      shiftName: null,  // 不設定預設值
      employId: null,
      groupName: null,  // 不設定預設值
      quantity: 0,
      pinventorylines: []
    }

    // 重置本地狀態
    pinventory.value = { ...newPInventory }
    pinventoriesStore.setPInventory(newPInventory)
    pinventoriesStore.setPInventorylines([])

    nextTick(() => {
      if (validForm.value) {
        // 先重置驗證，然後觸發驗證以顯示必填提示
        validForm.value.resetValidation()
        setTimeout(() => {
          validForm.value.validate()
        }, 100)
      }
    })
  }
})
</script>
