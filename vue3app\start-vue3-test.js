#!/usr/bin/env node

/**
 * Vue3編輯功能測試啟動腳本
 * 
 * 此腳本用於快速啟動開發服務器並打開測試頁面
 */

const { spawn } = require('child_process')
const { platform } = require('os')

console.log('🚀 啟動Vue3編輯功能測試...\n')

// 檢查是否已安裝依賴
const fs = require('fs')
const path = require('path')

if (!fs.existsSync(path.join(__dirname, 'node_modules'))) {
  console.log('❌ 未找到 node_modules，請先執行 npm install')
  process.exit(1)
}

console.log('📋 測試準備清單:')
console.log('✅ 檢查依賴安裝')
console.log('✅ 檢查Vue3組件文件')
console.log('✅ 檢查路由配置')
console.log('')

// 顯示測試URL
console.log('🌐 測試URL列表:')
console.log('─'.repeat(60))
console.log('主測試頁面:     http://localhost:3000/vue3-test')
console.log('新增品檢記錄:   http://localhost:3000/test-vue3/inspects/new')
console.log('編輯品檢記錄:   http://localhost:3000/test-vue3/inspect/1')
console.log('功能測試頁面:   http://localhost:3000/test-vue3/functions')
console.log('原始測試頁面:   http://localhost:3000/test')
console.log('')

// 顯示測試步驟
console.log('📝 測試步驟建議:')
console.log('─'.repeat(60))
console.log('1. 等待開發服務器啟動完成')
console.log('2. 在瀏覽器中訪問主測試頁面')
console.log('3. 點擊"開啟品檢表單測試"按鈕')
console.log('4. 測試表單驗證功能:')
console.log('   - 嘗試提交空表單')
console.log('   - 檢查驗證訊息是否正確顯示')
console.log('5. 測試數據綁定:')
console.log('   - 輸入各種數據')
console.log('   - 檢查響應式更新')
console.log('6. 測試鍵盤快捷鍵:')
console.log('   - Ctrl+S (儲存)')
console.log('   - Esc (取消)')
console.log('7. 測試主檔明細關聯:')
console.log('   - 儲存主檔後新增明細')
console.log('   - 編輯和刪除明細')
console.log('')

// 顯示除錯資訊
console.log('🔧 除錯資訊:')
console.log('─'.repeat(60))
console.log('如果遇到問題，請檢查:')
console.log('- 瀏覽器開發者工具的Console')
console.log('- Network標籤中的API請求')
console.log('- Vue DevTools擴展')
console.log('')

// 啟動開發服務器
console.log('🎯 正在啟動開發服務器...')
console.log('請稍候，服務器啟動後會自動打開瀏覽器')
console.log('')

const isWindows = platform() === 'win32'
const npmCommand = isWindows ? 'npm.cmd' : 'npm'

// 啟動Vite開發服務器
const devServer = spawn(npmCommand, ['run', 'dev'], {
  stdio: 'inherit',
  shell: true,
  cwd: __dirname
})

// 等待服務器啟動後打開瀏覽器
setTimeout(() => {
  console.log('\n🌐 正在打開瀏覽器...')
  
  const urls = [
    'http://localhost:3000/vue3-test',
    'http://localhost:5173/vue3-test', // Vite默認端口
    'http://localhost:8080/vue3-test'  // 備用端口
  ]
  
  // 嘗試打開瀏覽器
  const openBrowser = (url) => {
    const start = isWindows ? 'start' : platform() === 'darwin' ? 'open' : 'xdg-open'
    const command = isWindows ? `${start} ${url}` : `${start} "${url}"`
    
    require('child_process').exec(command, (error) => {
      if (error) {
        console.log(`❌ 無法自動打開瀏覽器: ${error.message}`)
        console.log(`請手動訪問: ${url}`)
      } else {
        console.log(`✅ 已打開瀏覽器: ${url}`)
      }
    })
  }
  
  // 先嘗試主要URL
  openBrowser(urls[0])
  
  // 顯示所有可能的URL
  console.log('\n如果上述URL無法訪問，請嘗試以下URL:')
  urls.forEach((url, index) => {
    console.log(`${index + 1}. ${url}`)
  })
  
}, 5000) // 等待5秒讓服務器啟動

// 處理進程退出
process.on('SIGINT', () => {
  console.log('\n\n👋 正在關閉開發服務器...')
  devServer.kill('SIGINT')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n\n👋 正在關閉開發服務器...')
  devServer.kill('SIGTERM')
  process.exit(0)
})

devServer.on('close', (code) => {
  console.log(`\n開發服務器已關閉，退出代碼: ${code}`)
  process.exit(code)
})

devServer.on('error', (error) => {
  console.error(`❌ 啟動開發服務器失敗: ${error.message}`)
  console.log('\n請嘗試手動執行:')
  console.log('npm run dev')
  console.log('\n然後在瀏覽器中訪問: http://localhost:3000/vue3-test')
  process.exit(1)
})
