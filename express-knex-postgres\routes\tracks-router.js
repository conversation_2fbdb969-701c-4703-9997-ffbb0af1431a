const router = require("express").Router();
const moment = require('moment');

const tracksDB = require("../models/tracks-model.js");

// GET ALL TRACKS
router.get("/", async (req, res) => {
  try {
    const tracks = await tracksDB.find();
    //
    tracks.map(mem => {
      mem.trackTime = moment(mem.trackTime).format("YYYY-M-D HH:mm");
      return mem;
    });
      //
     track.map(mem => { 
        mem.furnaceName = 'S01';
        return mem;      
    });   
    //
    res.status(200).json(tracks);
  } catch (err) {
    res.status(500).json({ err: err });
  }
});

// GET TRACK BY ID
router.get("/:id", async (req, res) => {
  const trackId = req.params.id;
  if (trackId.length == 3) {
    try {

      const track = await tracksDB.findT1ByCode(trackId);
      const trackT2 = await tracksDB.findT2ByCode(trackId);
      track.map(mem => {
        return trackT2.map(info => {
          if (info.trackTime.toDateString() === mem.trackTime.toDateString()) {
            mem.trackT2Qty = info.trackT2Qty;
            return mem;
          }
        })
      });
      //
      track.map(mem => {
        mem.trackTime = moment(mem.trackTime).format("YYYY-M-D HH:mm");
        return mem;
      });
      //
      //        
      track.map(mem => {
        if (mem.workT1Qty == null && mem.workT2Qty == null) {
          mem.workT1Qty = mem.trackT1Qty;
          mem.workT2Qty = mem.trackT2Qty;
          return mem;
        }
      });
      //
      track.map(mem => {
        mem.furnaceName = 'S01';
        return mem;
      });
      if (!track) {
        res
          .status(404)
          .json({ err: "The specified id does not exist" });
      } else {
        res.status(200).json(track);
      }
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
  else if (trackId.length == 6) {
    try {
      const track = await tracksDB.findT1ByTWCode(trackId);
      const trackT2 = await tracksDB.findT2ByTWCode(trackId);
      track.map(mem => {
        return trackT2.map(info => {
          if (info.trackTime.toDateString() === mem.trackTime.toDateString()) {
            mem.trackT2Qty = info.trackT2Qty;
            return mem;
          }
        })
      });
      //
      track.map(mem => {
        mem.trackTime = moment(mem.trackTime).format("YYYY-M-D HH:mm");
        return mem;
      });
      //
    
      track.map(mem => {
        mem.furnaceName = 'S01';
        return mem;
      });
      if (!track) {
        res
          .status(404)
          .json({ err: "The specified id does not exist" });
      } else {
        res.status(200).json(track);
      }
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
  else if (trackId.length >= 10) {
    // 處理長 QRCode (如: 20210903100003)
    try {
      const track = await tracksDB.findT1ByTWCode(trackId);
      const trackT2 = await tracksDB.findT2ByTWCode(trackId);

      track.map(mem => {
        return trackT2.map(info => {
          if (info.trackTime.toDateString() === mem.trackTime.toDateString()) {
            mem.trackT2Qty = info.trackT2Qty;
            return mem;
          }
        })
      });

      track.map(mem => {
        mem.trackTime = moment(mem.trackTime).format("YYYY-M-D HH:mm");
        return mem;
      });

      track.map(mem => {
        mem.furnaceName = 'S01';
        return mem;
      });

      if (!track || track.length === 0) {
        res.status(404).json({ err: "The specified QRCode does not exist" });
      } else {
        res.status(200).json(track);
      }
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
  else {
    res.status(200).json({ err: "The specified id does not exist" });
  }
});

// INSERT TRACK INTO DB
router.post("/", async (req, res) => {
  const newTrack = req.body;
  if (!newTrack.firstname) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const track = await tracksDB.addTrack(newTrack);
      res.status(201).json(track);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

router.put("/:id", async (req, res) => {
  const trackId = req.params.id;
  const newChanges = req.body;
  if (!newChanges.firstname) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const addChanges = await tracksDB.updateTrack(trackId, newChanges);
      res.status(200).json(addChanges);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

router.delete("/:id", async (req, res) => {
  const trackId = req.params.id;
  try {
    const deleting = await tracksDB.removeTrack(trackId);
    res.status(204).json(deleting);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;
