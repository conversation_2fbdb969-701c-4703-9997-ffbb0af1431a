<template>
  <div :style="styles">
    <canvas ref="chart" :height="height"></canvas>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-facing-decorator'
import { Line } from 'vue-chartjs'
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js'

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend)

@Component({
  name: 'OrderForecastChart'
})
export default class OrderForecastChart extends Vue {
  @Prop({ default: 150 }) height!: number

  private get styles() {
    return {
      "margin-left": '0px',
      "background": '#e8757857'
    }
  }

  mounted() {
    const gradient = (this.$refs.chart as HTMLCanvasElement).getContext('2d')?.createLinearGradient(0, 0, 0, 450)
    if (gradient) {
      gradient.addColorStop(0, 'rgba(255, 0,0, 0.5)')
      gradient.addColorStop(0.5, 'rgba(255, 0, 0, 0.25)')
      gradient.addColorStop(1, 'rgba(255, 0, 0, 0)')
    }

    const chart = new ChartJS(this.$refs.chart as HTMLCanvasElement, {
      type: 'line',
      data: {
        labels: [
          "January",
          "February",
          "March",
          "April",
          "May",
          "June",
          "July"
        ],
        datasets: [
          {
            label: "Orders",
            backgroundColor: gradient || 'rgba(255, 0, 0, 0.5)',
            borderColor: "#fc2525",
            data: [40, 39, 10, 40, 39, 80, 40]
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }
}
</script>
