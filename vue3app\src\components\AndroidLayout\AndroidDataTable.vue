<template>
  <!-- 移動端使用卡片列表 -->
  <template v-if="isMobile && useCardList">
    <v-list :class="tableClass">
      <v-list-item
        v-for="(item, index) in items"
        :key="getItemKey(item, index)"
        :class="cardItemClass"
      >
        <v-card variant="outlined" class="w-100">
          <v-card-text :class="cardContentClass">
            <!-- 卡片標題 -->
            <div class="d-flex justify-space-between align-center mb-2">
              <span class="text-subtitle2 font-weight-bold">
                {{ getCardTitle(item, index) }}
              </span>
              <v-chip 
                v-if="getCardChip(item)"
                :color="getCardChipColor(item)" 
                size="small"
                variant="flat"
              >
                {{ getCardChip(item) }}
              </v-chip>
            </div>
            
            <v-divider class="mb-2"></v-divider>
            
            <!-- 卡片內容 -->
            <div class="text-body-2">
              <div 
                v-for="header in displayHeaders" 
                :key="header.key"
                class="mb-1"
              >
                <strong>{{ header.title }}:</strong> 
                <span v-if="$slots[`item.${header.key}`]">
                  <slot 
                    :name="`item.${header.key}`" 
                    :item="item" 
                    :index="index"
                    :value="getItemValue(item, header.key)"
                  />
                </span>
                <span v-else>
                  {{ formatValue(getItemValue(item, header.key), header) }}
                </span>
              </div>
            </div>
            
            <!-- 操作按鈕 -->
            <div v-if="$slots.actions || showActions" class="mt-3">
              <slot name="actions" :item="item" :index="index">
                <v-btn-group v-if="showActions" density="compact">
                  <v-btn 
                    size="small" 
                    color="primary" 
                    variant="text"
                    @click="$emit('edit', item, index)"
                  >
                    編輯
                  </v-btn>
                  <v-btn 
                    size="small" 
                    color="error" 
                    variant="text"
                    @click="$emit('delete', item, index)"
                  >
                    刪除
                  </v-btn>
                </v-btn-group>
              </slot>
            </div>
          </v-card-text>
        </v-card>
      </v-list-item>
    </v-list>
  </template>
  
  <!-- 桌面端或強制使用表格 -->
  <template v-else>
    <v-data-table
      :headers="displayHeaders"
      :items="items"
      :items-per-page="responsiveItemsPerPage"
      :density="responsiveDensity"
      :class="tableClass"
      :loading="loading"
      :no-data-text="noDataText"
      :search="search"
      v-bind="$attrs"
    >
      <!-- 動態插槽轉發 -->
      <template 
        v-for="(_, slotName) in $slots" 
        :key="slotName"
        v-slot:[slotName]="slotProps"
      >
        <slot :name="slotName" v-bind="slotProps" />
      </template>
      
      <!-- 無資料插槽 -->
      <template v-slot:no-data>
        <slot name="no-data">
          <v-alert type="warning" class="ma-4">
            {{ noDataText }}
          </v-alert>
        </slot>
      </template>
    </v-data-table>
  </template>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAndroidLayout } from '@/composables/useAndroidLayout'

interface TableHeader {
  title: string
  key: string
  align?: 'start' | 'center' | 'end'
  sortable?: boolean
  width?: string | number
  minWidth?: string | number
  maxWidth?: string | number
}

interface Props {
  // 基本表格屬性
  headers: TableHeader[]
  items: any[]
  loading?: boolean
  search?: string
  noDataText?: string
  
  // 響應式屬性覆蓋
  itemsPerPage?: number
  density?: 'default' | 'comfortable' | 'compact'
  
  // Android特定屬性
  forceTable?: boolean
  showActions?: boolean
  cardTitleKey?: string
  cardChipKey?: string
  cardChipColorKey?: string
  maxMobileHeaders?: number
}

interface Emits {
  (e: 'edit', item: any, index: number): void
  (e: 'delete', item: any, index: number): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  noDataText: '查無資料',
  forceTable: false,
  showActions: false,
  cardTitleKey: 'name',
  cardChipKey: 'status',
  maxMobileHeaders: 3
})

const emit = defineEmits<Emits>()

// 使用Android Layout composable
const {
  isMobile,
  dataTableConfig,
  tableClass: androidTableClass,
  cardClass,
  simplifyHeaders
} = useAndroidLayout()

// 計算是否使用卡片列表
const useCardList = computed(() => {
  return !props.forceTable && dataTableConfig.value.useCardList
})

// 計算響應式每頁項目數
const responsiveItemsPerPage = computed(() => {
  return props.itemsPerPage || dataTableConfig.value.itemsPerPage
})

// 計算響應式密度
const responsiveDensity = computed(() => {
  return props.density || dataTableConfig.value.density
})

// 計算顯示的標題
const displayHeaders = computed(() => {
  if (isMobile.value && useCardList.value) {
    // 移動端卡片模式，限制顯示的欄位數量
    return props.headers.slice(0, props.maxMobileHeaders)
  }
  
  if (isMobile.value) {
    // 移動端表格模式，簡化標題
    return simplifyHeaders(props.headers)
  }
  
  return props.headers
})

// 計算表格類別
const tableClass = computed(() => {
  const classes = [androidTableClass.value]
  
  if (isMobile.value) {
    classes.push('android-mobile-table')
  }
  
  return classes.filter(Boolean).join(' ')
})

// 計算卡片項目類別
const cardItemClass = computed(() => {
  return isMobile.value ? 'mb-2' : 'mb-3'
})

// 計算卡片內容類別
const cardContentClass = computed(() => {
  return isMobile.value ? 'pa-3' : 'pa-4'
})

// 工具方法
const getItemKey = (item: any, index: number) => {
  return item.id || item.key || index
}

const getItemValue = (item: any, key: string) => {
  return key.split('.').reduce((obj, k) => obj?.[k], item)
}

const getCardTitle = (item: any, index: number) => {
  const titleValue = getItemValue(item, props.cardTitleKey)
  return titleValue || `項目 #${index + 1}`
}

const getCardChip = (item: any) => {
  return getItemValue(item, props.cardChipKey)
}

const getCardChipColor = (item: any) => {
  const colorKey = props.cardChipColorKey
  if (colorKey) {
    return getItemValue(item, colorKey)
  }
  
  // 預設顏色邏輯
  const chipValue = getCardChip(item)
  if (typeof chipValue === 'number') {
    return chipValue > 50 ? 'success' : chipValue > 25 ? 'warning' : 'error'
  }
  
  return 'primary'
}

const formatValue = (value: any, header: TableHeader) => {
  if (value === null || value === undefined) return ''
  
  // 根據欄位類型格式化值
  if (header.key.includes('date') || header.key.includes('time')) {
    return new Date(value).toLocaleDateString('zh-TW')
  }
  
  if (typeof value === 'number' && header.key.includes('weight')) {
    return `${value} kg`
  }
  
  return String(value)
}
</script>

<style scoped>
/* Android移動端表格優化 */
.android-mobile-table {
  font-size: 14px !important;
}

.android-mobile-table :deep(.v-data-table__td),
.android-mobile-table :deep(.v-data-table__th) {
  padding: 8px 4px !important;
  font-size: 14px !important;
}

/* 卡片列表優化 */
.w-100 {
  width: 100% !important;
}

.v-list-item {
  padding: 0 !important;
}

/* 卡片內容優化 */
.v-card-text {
  line-height: 1.4 !important;
}

/* 按鈕組優化 */
.v-btn-group {
  gap: 8px;
}

/* 載入狀態優化 */
.v-data-table--loading {
  opacity: 0.7;
}

/* 無資料狀態優化 */
.v-alert {
  border-radius: 8px !important;
}

/* 響應式優化 */
@media (max-width: 599px) {
  .android-mobile-table :deep(.v-data-table__wrapper) {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* 高對比度模式支援 */
@media (prefers-contrast: high) {
  .v-card {
    border: 2px solid currentColor !important;
  }
}

/* 減少動畫模式支援 */
@media (prefers-reduced-motion: reduce) {
  .v-card,
  .v-list-item {
    transition: none !important;
  }
}
</style>
