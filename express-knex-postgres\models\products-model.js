const db = require("../config/dbConfig.js");
const {infoLogger,errorLogger} = require("../config/winston.js");


// GET SPECIFIC PRODUCT BY ID
const findById = id => {
  return db("m_twqrcodeline")
  .select({
    furnaceName: "m_twqrcodeline.funo",
    productName: "rv_product_class.name1",
    productDate: "m_twqrcodeline.textime",
    gradeName: "rv_product_class.prod_class",
    bushingNO:  "m_twqrcodeline.bushingno",
    position: "m_twqrcodeline.position",
    cakeWeight: "m_twqrcodeline.movementqty",
    workDate: "m_twqrcodeline.created",
    twisterNO: "m_twqrcodeline.mach_tmis_no",
    spindleNO: "m_twqrcodeline.spindleno",
    texName:  "m_twqrcodeline.tex",
    biName:  "m_twqrcodeline.bi",
    batchName:  "m_twqrcodeline.lotno",
    lmccode: "m_twqrcodeline.qrcwisno",
    codeName: "m_twqrcodeline.qrbobbinno",
    isMFD: "m_twqrcodeline.ismfd",    
    m_product_id: "m_twqrcodeline.m_product_id",
    m_twqrcodeline_id: "m_twqrcodeline.m_twqrcodeline_id"
    
  })
  .join('rv_product_class', 'm_twqrcodeline.m_product_id', '=', 'rv_product_class.m_product_id')
  .where("m_twqrcodeline.qrbobbinno", id)
  .whereNot('m_twqrcodeline.qrcwisno', 'Error!!                                           ')
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
    });
};

const findProductIdOfGrade = (productname, gradename) => {
  return db("rv_product_class")
    .select("rv_product_class.m_product_id")    
    .where("rv_product_class.name1", "=", productname)
    .where("isactive", "=", "Y")
    .where("rv_product_class.prod_class", "=", gradename)
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

module.exports = {
  findById,
  findProductIdOfGrade
};
