import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createRouter, createWebHistory } from 'vue-router'
import { useGenericCRUD } from '@/composables/useGenericCRUD'

// Mock API functions
vi.mock('@/utils/backend-api', () => ({
  getData: vi.fn(),
  postData: vi.fn(),
  putData: vi.fn(),
  deleteData: vi.fn()
}))

import { getData, postData, putData, deleteData as deleteDataAPI } from '@/utils/backend-api'

// Mock router
const mockRouter = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/items', component: { template: '<div>List</div>' } },
    { path: '/item/:id', component: { template: '<div>Edit</div>' } },
    { path: '/items/new', component: { template: '<div>New</div>' } }
  ]
})

interface TestItem {
  id: number
  name: string
  description: string
}

describe('useGenericCRUD', () => {
  const defaultData: TestItem = {
    id: 0,
    name: '',
    description: ''
  }

  const options = {
    endpoint: 'test-items',
    listRoute: '/items',
    defaultData,
    onSaveSuccess: vi.fn(),
    onSaveError: vi.fn(),
    onLoadSuccess: vi.fn(),
    onLoadError: vi.fn(),
    onDeleteSuccess: vi.fn(),
    onDeleteError: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('初始化', () => {
    it('應該正確初始化狀態', async () => {
      await mockRouter.push('/items/new')

      const { state, isEditMode, formTitle, isDirty, canSave } = useGenericCRUD(options)

      expect(state.value.loading).toBe(false)
      expect(state.value.saving).toBe(false)
      expect(state.value.deleting).toBe(false)
      expect(state.value.formValid).toBe(false)
      expect(state.value.formData).toEqual(defaultData)
      expect(state.value.originalData).toEqual(defaultData)
      expect(isEditMode.value).toBe(false)
      expect(formTitle.value).toBe('新增')
      expect(isDirty.value).toBe(false)
      expect(canSave.value).toBe(false)
    })

    it('應該在編輯模式下正確設置狀態', async () => {
      await mockRouter.push('/item/123')

      const { isEditMode, formTitle } = useGenericCRUD(options)

      expect(isEditMode.value).toBe(true)
      expect(formTitle.value).toBe('編輯')
    })
  })

  describe('數據載入', () => {
    it('應該正確載入數據', async () => {
      await mockRouter.push('/item/123')

      const mockData = {
        id: 123,
        name: '測試項目',
        description: '測試描述'
      }

      vi.mocked(getData).mockResolvedValue({ data: mockData })

      const { loadData, state } = useGenericCRUD(options)

      await loadData()

      expect(getData).toHaveBeenCalledWith('test-items/123')
      expect(state.value.formData).toEqual(mockData)
      expect(state.value.originalData).toEqual(mockData)
      expect(options.onLoadSuccess).toHaveBeenCalledWith(mockData)
    })

    it('應該處理數組響應格式', async () => {
      await mockRouter.push('/item/123')

      const mockData = {
        id: 123,
        name: '測試項目',
        description: '測試描述'
      }

      vi.mocked(getData).mockResolvedValue({ data: [mockData] })

      const { loadData, state } = useGenericCRUD(options)

      await loadData()

      expect(state.value.formData).toEqual(mockData)
    })

    it('應該處理載入錯誤', async () => {
      await mockRouter.push('/item/123')

      const mockError = new Error('載入失敗')
      vi.mocked(getData).mockRejectedValue(mockError)

      const { loadData } = useGenericCRUD(options)

      await loadData()

      expect(options.onLoadError).toHaveBeenCalledWith(mockError)
    })
  })

  describe('數據保存', () => {
    it('應該在新增模式下正確保存數據', async () => {
      await mockRouter.push('/items/new')

      const mockSaveData = {
        id: 0,
        name: '新項目',
        description: '新描述'
      }

      const mockResponseData = {
        id: 456,
        name: '新項目',
        description: '新描述'
      }

      vi.mocked(postData).mockResolvedValue({ data: mockResponseData })

      const { formData, saveData, setFormValid } = useGenericCRUD(options)

      formData.value = mockSaveData
      setFormValid(true)

      await saveData()

      expect(postData).toHaveBeenCalledWith('test-items', mockSaveData)
      expect(options.onSaveSuccess).toHaveBeenCalledWith(mockResponseData)
    })

    it('應該在編輯模式下正確更新數據', async () => {
      await mockRouter.push('/item/123')

      const mockUpdateData = {
        id: 123,
        name: '更新項目',
        description: '更新描述'
      }

      vi.mocked(putData).mockResolvedValue({ data: mockUpdateData })

      const { formData, saveData, setFormValid } = useGenericCRUD(options)

      formData.value = mockUpdateData
      setFormValid(true)

      await saveData()

      expect(putData).toHaveBeenCalledWith('test-items/123', mockUpdateData)
      expect(options.onSaveSuccess).toHaveBeenCalledWith(mockUpdateData)
    })

    it('應該處理保存錯誤', async () => {
      await mockRouter.push('/items/new')

      const mockError = new Error('保存失敗')
      vi.mocked(postData).mockRejectedValue(mockError)

      const { saveData, setFormValid } = useGenericCRUD(options)

      setFormValid(true)

      await expect(saveData()).rejects.toThrow('保存失敗')
      expect(options.onSaveError).toHaveBeenCalledWith(mockError)
    })
  })

  describe('數據刪除', () => {
    it('應該正確刪除數據', async () => {
      await mockRouter.push('/item/123')

      vi.mocked(deleteDataAPI).mockResolvedValue({})

      const { deleteData: deleteFn, formData } = useGenericCRUD(options)

      formData.value = { id: 123, name: '測試', description: '測試' }

      await deleteFn()

      expect(deleteDataAPI).toHaveBeenCalledWith('test-items/123')
      expect(options.onDeleteSuccess).toHaveBeenCalledWith(123)
    })

    it('應該處理刪除錯誤', async () => {
      await mockRouter.push('/item/123')

      const mockError = new Error('刪除失敗')
      vi.mocked(deleteDataAPI).mockRejectedValue(mockError)

      const { deleteData: deleteFn, formData } = useGenericCRUD(options)

      formData.value = { id: 123, name: '測試', description: '測試' }

      await deleteFn()

      expect(options.onDeleteError).toHaveBeenCalledWith(mockError)
    })
  })

  describe('表單操作', () => {
    it('應該正確重設表單', () => {
      const { formData, resetForm, state } = useGenericCRUD(options)

      // 修改表單數據
      formData.value = { id: 123, name: '測試', description: '測試' }

      resetForm()

      expect(state.value.formData).toEqual(defaultData)
      expect(state.value.originalData).toEqual(defaultData)
    })

    it('應該正確更新表單數據', () => {
      const { updateFormData, formData } = useGenericCRUD(options)

      const updateData = { name: '更新名稱' }

      updateFormData(updateData)

      expect(formData.value.name).toBe('更新名稱')
    })

    it('應該正確設置表單驗證狀態', () => {
      const { setFormValid, state } = useGenericCRUD(options)

      setFormValid(true)

      expect(state.value.formValid).toBe(true)
    })
  })

  describe('計算屬性', () => {
    it('應該正確計算 isDirty', () => {
      const { isDirty, formData, state } = useGenericCRUD(options)

      expect(isDirty.value).toBe(false)

      formData.value = { ...defaultData, name: '修改後' }

      expect(isDirty.value).toBe(true)
    })

    it('應該正確計算 canSave', () => {
      const { canSave, setFormValid, updateFormData } = useGenericCRUD(options)

      expect(canSave.value).toBe(false)

      setFormValid(true)
      expect(canSave.value).toBe(false) // 還沒有修改

      updateFormData({ name: '修改後' })
      expect(canSave.value).toBe(true)
    })
  })

  describe('初始化流程', () => {
    it('應該在新增模式下正確初始化', async () => {
      await mockRouter.push('/items/new')

      const { initialize, state } = useGenericCRUD(options)

      await initialize()

      expect(state.value.formData).toEqual(defaultData)
    })

    it('應該在編輯模式下載入數據', async () => {
      await mockRouter.push('/item/123')

      const mockData = { id: 123, name: '測試', description: '測試' }
      vi.mocked(getData).mockResolvedValue({ data: mockData })

      const { initialize } = useGenericCRUD(options)

      await initialize()

      expect(getData).toHaveBeenCalledWith('test-items/123')
    })
  })

  describe('離開前檢查', () => {
    it('應該在沒有修改時允許離開', () => {
      const { beforeLeave } = useGenericCRUD(options)

      expect(beforeLeave()).toBe(true)
    })

    it('應該在有修改時提示確認', () => {
      global.confirm = vi.fn().mockReturnValue(true)

      const { beforeLeave, updateFormData } = useGenericCRUD(options)

      updateFormData({ name: '修改後' })

      expect(beforeLeave()).toBe(true)
      expect(global.confirm).toHaveBeenCalledWith('您有未保存的更改，確定要離開嗎？')
    })
  })

  describe('通知訊息', () => {
    it('應該正確顯示訊息', () => {
      const { showMessage, snackbar } = useGenericCRUD(options)

      showMessage('測試訊息', 'success')

      expect(snackbar.value).toEqual({
        show: true,
        message: '測試訊息',
        color: 'success'
      })
    })
  })

  describe('載入狀態管理', () => {
    it('應該正確管理載入狀態', () => {
      const { setLoading, state } = useGenericCRUD(options)

      setLoading(true)
      expect(state.value.loading).toBe(true)

      setLoading(false)
      expect(state.value.loading).toBe(false)
    })

    it('應該正確管理保存狀態', () => {
      const { setSaving, state } = useGenericCRUD(options)

      setSaving(true)
      expect(state.value.saving).toBe(true)

      setSaving(false)
      expect(state.value.saving).toBe(false)
    })

    it('應該正確管理刪除狀態', () => {
      const { setDeleting, state } = useGenericCRUD(options)

      setDeleting(true)
      expect(state.value.deleting).toBe(true)

      setDeleting(false)
      expect(state.value.deleting).toBe(false)
    })
  })
})
