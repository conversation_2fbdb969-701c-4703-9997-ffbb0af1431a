import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getData, putData, postData, deleteData } from '@/utils/backend-api'
import type { Employee, ICount, Entity, Tracksheet, ICountline, Pagination } from '@/types'

export const useICountsStore = defineStore('icounts', () => {
  // State
  const items = ref<Entity[]>([])
  const pagination = ref<Pagination>({
    page: 1,
    limit: 10,
    sortBy: [],
    descending: [],
    search: '',
    totalItems: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false
  })
  const loading = ref(false)
  const employee = ref('')
  const icountId = ref<number | null>(null)
  const icount = ref<ICount>({} as ICount)
  const icountlines = ref<ICountline[]>([])
  const tracksheet = ref<Tracksheet[]>([])
  const employees = ref<Employee[]>([])

  // Getters
  const isLoading = computed(() => loading.value)
  const currentICount = computed(() => icount.value)
  const currentICountlines = computed(() => icountlines.value)

  // Actions
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setICount = (value: ICount) => {
    // 如果是部分更新，合併現有資料
    if (icount.value && Object.keys(icount.value).length > 0) {
      icount.value = { ...icount.value, ...value }
    } else {
      icount.value = value
    }
  }

  const setICountlines = (value: ICountline[]) => {
    icountlines.value = value
  }

  const setTracksheet = (value: Tracksheet[]) => {
    tracksheet.value = value
  }

  const setEmployees = (value: Employee[]) => {
    employees.value = value
  }

  // API Actions
  const getEmployees = async () => {
    try {
      setLoading(true)
      const res = await getData("employees/tw")
      if (res.data) {
        // 先去重，再處理數據
        const uniqueData = res.data.filter((employee: any, index: number, self: any[]) =>
          index === self.findIndex((e: any) => e.employId === employee.employId)
        )

        const employeeList = uniqueData.map((c: any, index: number) => {
          return {
            // 確保每個項目都有唯一的 key，使用 employId 作為主鍵
            employId: c.employId,
            employNO: c.employNO,
            userName: c.userName,
            // 根據 Vue2 的邏輯，顯示格式為 employNO + userName
            employName: c.employNO + " " + c.userName,
            // 使用 employId 作為 value，與後端 API 一致
            value: c.employId,
            // 為 Vuetify 提供唯一的 key
            key: `employee_${c.employId}_${index}`,
            // 其他可能需要的欄位
            ...c
          }
        })

        console.log('Store: 員工數據處理完成:', employeeList)
        setEmployees(employeeList)
      }
    } catch (error) {
      console.error('獲取員工列表失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const getAllICounts = async (page: number = pagination.value.page, limit: number = pagination.value.limit || 10) => {
    setLoading(true)
    try {
      // 直接調用後端 API，使用原本的路徑
      const response = await getData(`icounts`)

      if (response.data && Array.isArray(response.data)) {
        // 後端返回的數據已經包含正確的 quantity 值
        const allItems = response.data.map((item: any) => ({
          ...item,
          // 使用後端計算的 quantity，不要重新計算
          quantity: parseInt(item.quantity) || 0
        }))

        // 手動處理分頁
        pagination.value.totalItems = allItems.length
        pagination.value.totalPages = Math.ceil(allItems.length / limit)
        pagination.value.hasNextPage = page < pagination.value.totalPages
        pagination.value.hasPrevPage = page > 1

        // 客戶端分頁
        const startIndex = (page - 1) * limit
        const endIndex = startIndex + limit
        items.value = allItems.slice(startIndex, endIndex)
      } else {
        items.value = []
        pagination.value.totalItems = 0
      }
    } catch (error) {
      console.error('API Error:', error)
      items.value = []
      pagination.value.totalItems = 0
      throw error
    } finally {
      setLoading(false)
    }
  }

  const getICountById = async (id: string | number) => {
    try {
      setLoading(true)
      console.log('Store: 開始獲取品檢計數記錄，ID:', id)
      const res = await getData(`icounts/${id}`)
      console.log('Store: API 響應:', res)

      if (res.data && Array.isArray(res.data) && res.data.length > 0) {
        // 後端返回的是數組，取第一個元素
        const icountData = res.data[0]
        console.log('Store: 設置主檔數據:', icountData)

        // 計算數量
        if (icountData.icountlines && Array.isArray(icountData.icountlines)) {
          icountData.quantity = icountData.icountlines.length
          setICountlines(icountData.icountlines)
        } else {
          icountData.quantity = 0
          setICountlines([])
        }

        setICount(icountData)
        icountId.value = Number(id)
      } else {
        console.log('Store: API 響應格式不正確或無數據')
        setICount({} as ICount)
        setICountlines([])
      }
      return res.data
    } catch (error) {
      console.error('獲取品檢計數記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const saveICount = async (data: ICount) => {
    try {
      setLoading(true)
      let res

      if (data.id && data.id > 0) {
        // 更新現有記錄 - 使用 PUT 方法
        console.log('Store: 更新現有記錄，ID:', data.id)
        res = await putData(`icounts/${data.id}`, data)
        if (res.data) {
          // PUT 請求通常返回更新後的單個對象
          const updatedICount = Array.isArray(res.data) ? res.data[0] : res.data
          setICount(updatedICount)
          icountId.value = updatedICount.id
        }
      } else {
        // 創建新記錄 - 使用 POST 方法
        console.log('Store: 創建新記錄')
        res = await postData('icounts/', data)
        if (res.data && Array.isArray(res.data) && res.data.length > 0) {
          // 後端返回數組，取第一個元素
          const newICount = res.data[0]
          setICount(newICount)
          icountId.value = newICount.id
        }
      }

      return res.data
    } catch (error) {
      console.error('保存品檢計數記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const deleteICount = async (id: number) => {
    try {
      setLoading(true)
      await deleteData(`icounts/${id}`)

      // 如果刪除的是當前記錄，清空狀態
      if (icountId.value === id) {
        icount.value = {} as ICount
        icountlines.value = []
        icountId.value = null
      }
    } catch (error) {
      console.error('刪除品檢計數記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 明細檔操作
  const addICountlineToICount = async (data: ICountline) => {
    try {
      const res = await postData('icountlines/', data)
      if (res.data) {
        // 重新載入明細檔
        if (icountId.value) {
          await getICountById(icountId.value)
        }
      }
      return res.data
    } catch (error) {
      console.error('新增明細記錄失敗:', error)
      throw error
    }
  }

  const updateICountline = async (data: ICountline) => {
    try {
      const res = await putData(`icountlines/${data.id}`, data)
      if (res.data) {
        // 重新載入明細檔
        if (icountId.value) {
          await getICountById(icountId.value)
        }
      }
      return res.data
    } catch (error) {
      console.error('更新明細記錄失敗:', error)
      throw error
    }
  }

  const deleteICountline = async (data: ICountline) => {
    try {
      await deleteData(`icountlines/${data.id}`)

      // 重新載入明細檔
      if (icountId.value) {
        await getICountById(icountId.value)
      }
    } catch (error) {
      console.error('刪除明細記錄失敗:', error)
      throw error
    }
  }

  // 獲取 Tracksheet 資料
  const getTracksheetByCode = async (tracks: string[]) => {
    try {
      setLoading(true)
      const [type, query, trackId] = tracks
      const res = await getData(`tracksheet/yarn/${query}`)
      if (res.data && Array.isArray(res.data)) {
        setTracksheet(res.data)
        return res.data
      } else {
        setTracksheet([])
        return []
      }
    } catch (error) {
      console.error('獲取 Tracksheet 失敗:', error)
      setTracksheet([])
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 檢查重複的 Tracksheet Code
  const getDuplicateICountlineByCode = async (code: string): Promise<boolean> => {
    try {
      setLoading(true)
      if (code) {
        const res = await getData(`icountlines/duplicate/${code}`)
        const data = res.data
        if (data !== undefined && data !== null && Array.isArray(data) && data.length > 0) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    } catch (error: any) {
      // 404 錯誤表示沒有重複，這是正常情況
      if (error?.response?.status === 404) {
        console.log('沒有重複的 Tracksheet Code，可以繼續')
        return false
      }
      console.error('檢查重複 Tracksheet Code 失敗:', error)
      return false
    } finally {
      setLoading(false)
    }
  }

  // 清空明細檔
  const clearICountline = () => {
    setLoading(true)
    setICountlines([])
    setLoading(false)
  }

  // 清空 Tracksheet
  const clearTracksheets = () => {
    setTracksheet([])
  }

  // 重置狀態
  const resetState = () => {
    items.value = []
    icount.value = {} as ICount
    icountlines.value = []
    tracksheet.value = []
    icountId.value = null
    employee.value = ''
    loading.value = false
  }

  return {
    // State
    items,
    pagination,
    loading,
    employee,
    icountId,
    icount,
    icountlines,
    tracksheet,
    employees,

    // Getters
    isLoading,
    currentICount,
    currentICountlines,

    // Actions
    setLoading,
    setICount,
    setICountlines,
    setTracksheet,
    setEmployees,
    getEmployees,
    getAllICounts,
    getICountById,
    saveICount,
    deleteICount,
    addICountlineToICount,
    updateICountline,
    deleteICountline,
    getTracksheetByCode,
    getDuplicateICountlineByCode,
    clearICountline,
    clearTracksheets,
    resetState
  }
})
