const db = require("../config/dbConfig.js");
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET ALL WORKS
const find = () => {
  return db("aits_works")
    .select({
      id: "aits_works.id",
      workDate: "aits_works.workdate",
      shiftName: "aits_works.shiftname",      
      groupName: "aits_works.groupname"
    }).orderBy("aits_works.id","desc");    
};

// SEARCH WORKS
const search = (searchParams) => {
  let query = db("aits_works")
    .select({
      id: "aits_works.id",
      workDate: "aits_works.workdate",
      shiftName: "aits_works.shiftname",      
      groupName: "aits_works.groupname"
    });

  // Handle ID search with exact match (trimming whitespace)
  if (searchParams.id) {
    query = query.where("aits_works.id", searchParams.id.trim());
  }

  // Handle other search params
  if (searchParams.workDate) {
    query = query.where("aits_works.workdate", searchParams.workDate);
  }
  if (searchParams.shiftName) {
    query = query.where("aits_works.shiftname", searchParams.shiftName);
  }
  if (searchParams.groupName) {
    query = query.where("aits_works.groupname", searchParams.groupName);
  }

  return query.orderBy("aits_works.id", "desc");
};

// GET SPECIFIC WORK BY ID
const findById = id => {
  return db("aits_works")
    .select({
      id: "aits_works.id",
      workDate: "aits_works.workdate",
      shiftName: "aits_works.shiftname",      
      groupName: "aits_works.groupname"
    }).where("id", id);

  //SQL RAW METHOD
  // return db.raw(`SELECT * FROM works
  //                  WHERE id = ${id}`);
};

// ADD A WORK
const addWork = work => {
  return db("aits_works").insert({
    workdate: work.workDate,
    shiftname: work.shiftName,   
    groupname: work.groupName
  }, "id");
};

// UPDATE WORK
const updateWork = (id, post) => {
  return db("aits_works")
    .where("id", id)
    .update(post);
};

// REMOVE WORK
const removeWork = id => {
  return db("aits_works")
    .where("id", id)
    .del();
};

module.exports = {
  find,
  findById,
  search,
  addWork,
  updateWork,
  removeWork
};
