<template>
  <v-container fluid>
    <v-flex xs12>
      <v-card>
        <v-card-title>
          <span class="title"
            >{{this.title}}
            {{ pagination ? "(" + pagination.totalItems + ")" : "" }}
          </span>
          <v-spacer></v-spacer>
          <table-header-buttons
            ref="reloadInput"
            :reloadData="clearData">
          </table-header-buttons>
        </v-card-title>
        <v-card-text>
          <v-text-field
            ref="searchInput"
            v-model="searchFilter.contain.codeName"
            append-icon="mdi-magnify"
            label="Search"
            @change="getProduct"
            counter="14"
            single-line
            hide-details
          ></v-text-field>
        </v-card-text>
        <Table
          v-if="loading === false"
          :headers="headers"
          :items="items"
          :pagination="pagination"
          :setSearch="false"
          :setEdit="false"
          :setRemove="false"
          :disableSort="true"

        ></Table>
      </v-card>
    </v-flex>
    <search-panel
      :rightDrawer="rightDrawer"
      @cancelSearch="cancelSearch"
      @searchData="searchProducts"
    >
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="productName"
            label="Product"
            light
            v-model="searchFilter.contain.productName"
          ></v-text-field>
        </v-flex>
      </v-layout>
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="minUnitPrice"
            type="number"
            label="Min Price"
            light
            v-model="searchFilter.greaterThanOrEqual.unitPrice"
          ></v-text-field>
        </v-flex>
      </v-layout>
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="maxUnitPrice"
            type="number"
            label="Max Price"
            light
            v-model="searchFilter.lessThanOrEqual.unitPrice"
          ></v-text-field>
        </v-flex>
      </v-layout>
    </search-panel>
    <confirm-dialog
      :dialog="dialog"
      :dialogTitle="dialogTitle"
      :dialogText="dialogText"
      @onConfirm="onConfirm"
      @onCancel="onCancel"
    ></confirm-dialog>
    <v-snackbar
      v-if="loading === false"
      :top="'top'"
      :right="true"
      :timeout="this.timeout"
      :color="mode"
      v-model="snackbar"
    >
    <div class="text-center">
      {{ notice }}
      <v-btn dark text @click.native="closeSnackbar">Close</v-btn>
    </div>
    </v-snackbar>
  </v-container>
</template>
<script lang="ts">
import Table from "@/components/Table.vue";
import TableHeaderButtons from "@/components/TableHeaderButtons.vue";
import SearchPanel from "@/components/SearchPanel.vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
import { debounce } from "lodash";
import {
  buildSearchFilters,
  buildJsonServerQuery,
  clearSearchFilters
} from "@/utils/app-util";
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { productModule } from "@/store/modules/products";
import { appModule } from "@/store/modules/app";

@Component({
  components: {
    Table,
    TableHeaderButtons,
    SearchPanel,
    ConfirmDialog
  }
})
export default class ProductList extends Vue {
  dialog = false;
  dialogTitle = "Product Delete Dialog";
  dialogText = "Do you want to delete this Record?";
  showSearchPanel = false;
  right = true;
  search = "";
  headers = [
    { text: "BI檢測", value: "biName" },
    { text: "TEX檢測", value: "texName" },
    { text: "Lot NO", value: "batchName" },
    { text: "乾燥時間(hrs)", value: "dryTime" },
    { text: "過磅日期", left: true, value: "productDate" },
    { text: "品種", left: true, value: "productName" },
    { text: "等級", value: "gradeName" },
    { text: "爐別", value: "furnaceName" },
    { text: "Bushing NO", value: "bushingNO" },
    { text: "Cake位置", value: "positionName" },
    { text: "Cake重量(g)", value: "cakeWeight" },
    { text: "開機日期", left: true, value: "workDate" },
    { text: "Twister NO", value: "twisterNO" },
    { text: "Spindle NO", value: "spindleNO" },
    { text: "Is MFD", value: "isMFD" },
    { text: "", value: "actions", sortable: false }
  ];
  searchFilter = {
    contain: {
      codeName: "",
      category: ""
    },
    greaterThanOrEqual: {
      unitPrice: 0
    },
    lessThanOrEqual: {
      unitPrice: 0
    }
  };
  private title = "";
  private productId = "";
  private query = "";
  private isYarnQRcode = null;
  private jsonQuery = "";
  private snackbarStatus = false;
  private timeout = 2000;
  private color = "";
  private quickSearchFilter = "";
  private itemId = -1;

  print() {
    window.print();
  }
  edit(item) {
    this.$router.push({
      name: "Product",
      params: { id: item.id }
    });
  }
  add() {
    this.$router.push("NewProduct");
  }
  remove(item) {
    this.itemId = item.id;
    this.dialog = true;
  }

  onConfirm() {
    productModule.deleteProduct(this.itemId);
    this.dialog = false;
  }
  onCancel() {
    this.itemId = -1;
    this.dialog = false;
  }
  getProduct() {
    buildSearchFilters(this.searchFilter);
    this.jsonQuery = buildJsonServerQuery(this.searchFilter);

    const codeName = this.searchFilter.contain.codeName;
    const trimmedCodeName = codeName.trim();

    if (!trimmedCodeName) {
      appModule.sendErrorNotice("查無資料!");
      appModule.closeNoticeWithDelay(3000);
      return "";
    }

    this.isYarnQRcode = /^\d{14}[a-zA-Z0-9\s.]*$/.test(trimmedCodeName);

    if (!this.isYarnQRcode) {
      appModule.sendErrorNotice("無效的QRCode!");
      appModule.closeNoticeWithDelay(3000);
      return "";
    }

    if (this.isYarnQRcode) {
      this.query = trimmedCodeName.slice(0, 14);
      productModule.getProductById(this.query);
      this.query = "";
      return productModule.product;
    }
  }

  searchProducts() {
    this.showSearchPanel = !this.showSearchPanel;
    buildSearchFilters(this.searchFilter);
    this.query = buildJsonServerQuery(this.searchFilter);
    this.quickSearch = "";
    productModule.searchProducts(this.query);
  }

  clearSearchFilters() {
    this.showSearchPanel = !this.showSearchPanel;
    clearSearchFilters(this.searchFilter);
  }

  reloadData() {
    this.query = "";
    this.$nextTick(() => {
        const searchInput = this.$refs.searchInput as HTMLElement;
          if (searchInput) {
          searchInput.focus();
          }
    });
  }

  clearData() {
    this.query = "";
    this.searchFilter.contain.codeName = "";
    productModule.clearProducts();
    this.$nextTick(() => {
        const searchInput = this.$refs.searchInput as HTMLElement;
          if (searchInput) {
          searchInput.focus();
          }
    });
  }

  updateSearchPanel() {
    this.rightDrawer = !this.rightDrawer;
  }

  cancelSearch() {
    this.showSearchPanel = false;
  }

  closeSnackbar() {
    appModule.closeNotice();
  }

  quickSearchCustomers = debounce(function() {
    productModule.quickSearch(this.headers, this.quickSearchFilter);
  }, 500);

  get items() {
    return productModule.items;
  }
  get pagination() {
    return productModule.pagination;
  }
  get loading() {
    return appModule.loading;
  }
  get mode() {
    return appModule.mode;
  }
  get snackbar() {
    return appModule.snackbar;
  }
  get notice() {
    return appModule.notice;
  }

  get rightDrawer() {
    return this.showSearchPanel;
  }

  set rightDrawer(rightDrawer: boolean) {
    this.showSearchPanel = rightDrawer;
  }

  get quickSearch() {
    return this.quickSearchFilter;
  }
  set quickSearch(val) {
    this.quickSearchFilter = val;
    this.quickSearchFilter && this.quickSearchCustomers();
  }

  created() {
    productModule.clearProducts();
  }

  mounted() {
    this.$nextTick(() => {
      this.title ="EM即時生產查詢";
      this.$nextTick(() => {
        const searchInput = this.$refs.searchInput as HTMLElement;
          if (searchInput) {
          searchInput.focus();
          }
      });
    });
  }
}
</script>
