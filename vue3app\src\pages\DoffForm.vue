<template>
  <!-- Previous template content remains unchanged -->
  <v-container fluid>
    <v-flex xs12>
      <v-card class="grey lighten-4 elevation-0">
        <!-- Rest of the template content -->
      </v-card>
    </v-flex>
  </v-container>
</template>

<script lang="ts">
import { Component, Vue, Ref } from 'vue-facing-decorator'
import { useRouter, useRoute } from 'vue-router'
import Table from '@/components/Table.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import { trackModule } from '@/store/modules/tracks'
import { workModule } from '@/store/modules/works'
import { appModule } from '@/store/modules/app'
import { buildSearchFilters, getISOClassDate } from '@/utils/app-util'

interface Header {
  text: string
  left?: boolean
  value: string
  sortable?: boolean
}

interface ValidForm {
  validate: () => boolean
}

@Component({
  name: 'WorkForm',
  components: {
    Table,
    ConfirmDialog
  }
})
export default class WorkForm extends Vue {
  @Ref('validForm') validForm!: ValidForm
  @Ref('validDetail') validDetail!: ValidForm

  private router = useRouter()
  private route = useRoute()

  private modalTitle = "Add Track"
  private modalText = "Select the category and track from the list"
  private addTrackModal = false
  private dialog = false
  private dialogTitle = "Track Delete Dialog"
  private dialogText = "Do you want to delete this track?"
  private workDateMenu = false
  private worklineDateMenu = false
  private worklineTimeMenu = false
  private errors: string[] = []
  private formValid = false
  private detailValid = false
  private title = ""
  private workId: number | null = null
  private worklineId: number | null = null
  private categoryId = 0
  private remarkId = 0
  private color = ""
  private selectedWorkline: any = null
  private query = ""
  private search = ""

  headers: Header[] = [
    { text: "下機日期時間", left: true, value: "doffTime" },
    { text: "下機人員", left: true, value: "doffEmployName" },
    { text: "捻線機號", left: true, value: "twisterNO" },
    { text: "爐別", left: true, value: "furnaceName" },
    { text: "品種", left: true, value: "productName" },
    { text: "開機T1個數", value: "workT1Qty" },
    { text: "開機T2個數", value: "workT2Qty" },
    { text: "下機T1個數", value: "doffT1Qty" },
    { text: "下機T2個數", value: "doffT2Qty" },
    { text: "", value: "actions", sortable: false }
  ]

  searchFilter = { contain: { twisterNO: "" } }

  get employees() {
    return workModule.employees
  }

  get work() {
    return workModule.work
  }

  get workline() {
    return workModule.workline
  }

  get track() {
    return trackModule.track
  }

  get loading() {
    return appModule.loading
  }

  get mode() {
    return appModule.mode
  }

  get snackbar() {
    return appModule.snackbar
  }

  get notice() {
    return appModule.notice
  }

  get pagination() {
    return workModule.pagination
  }

  save() {
    workModule.saveWork(this.work)
  }

  getWorkById() {
    const id = this.route.params.id
    if (id) {
      workModule.getWorkById(id as string)
    }
  }

  getTrack() {
    buildSearchFilters(this.searchFilter)
    this.query = this.searchFilter.contain.twisterNO
    if (this.query !== "") {
      trackModule.getTrackById(this.query)
      this.query = ""
      return trackModule.track
    }
    return ""
  }

  cancel() {
    this.router.push({ name: "works" })
  }

  remove(item: any) {
    this.selectedWorkline = item
    this.dialog = true
  }

  onConfirm() {
    workModule.deleteWorkline(this.selectedWorkline)
    this.selectedWorkline = null
    this.getWorkById()
    this.dialog = false
  }

  onCancel() {
    this.selectedWorkline = null
    this.dialog = false
  }

  addTrack() {
    this.addTrackModal = true
    this.query = ""
    this.searchFilter.contain.twisterNO = ""
    this.workId = this.work.id
    trackModule.clearTracks()
    if (this.validDetail) {
      this.validDetail.validate()
    }
  }

  saveWorkline() {
    const WorkId = { workId: this.work.id }
    const addTrack = this.track[0]
    const addWorkline = this.workline
    const newWorkline = { ...WorkId, ...addTrack, ...addWorkline }
    workModule.addWorklineToWork(newWorkline)
    this.worklineId = null
    this.getWorkById()
    this.addTrackModal = false
  }

  cancelAddTrack() {
    this.addTrackModal = false
    this.query = ""
    this.searchFilter.contain.twisterNO = ""
    workModule.clearWorkline()
    trackModule.clearTracks()
  }

  closeSnackbar() {
    appModule.closeNotice()
  }

  created() {
    this.getWorkById()
    workModule.getEmployees()
  }

  mounted() {
    if (this.route.params.id) {
      this.title = "捻線下機"
    } else {
      this.title = "捻線下機(新增)"
      const toDate = getISOClassDate()
      this.work.workDate = toDate.slice(0, 10)
      if (this.validForm) {
        this.validForm.validate()
      }
    }
  }
}
</script>
