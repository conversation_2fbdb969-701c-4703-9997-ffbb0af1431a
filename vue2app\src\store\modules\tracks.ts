import { getData, putData, postData, deleteData } from "@/utils/backend-api";
import { Entity, Track } from "@/types";
import { getDefaultPagination, getPagination } from "@/utils/store-util";
import { appModule } from "./app";
import { get } from "lodash";
import moment from "moment";
import {
  VuexModule,
  Module,
  Mutation,
  Action,
  getModule
} from "vuex-module-decorators";
import store from "@/store";

export interface TrackState {
  items: Entity[];
  pagination: Pagination;
  loading: boolean;
  track: Track;
}

@Module({ store, dynamic: true, name: "tracks" })
class TrackModule extends VuexModule implements TrackState {
  public items: Entity[] = [];
  public pagination = getDefaultPagination();
  public loading = false;
  public customer = "";
  public track = {} as Track;

  @Action
  getTrackById(id: string) {
    this.setLoading(true);
    if (id) {
      getData("tracks/" + id).then(
        res => {
          const track = res.data;
          track[0].trackDate = track[0].trackDate
            ? moment(track[0].trackDate).format("YYYY/M/D")
            : "";
          this.setDataTable(track);
          this.setTrack(track);
          this.setLoading(false);
        },
        (err: TODO) => {
          console.log(err);
          this.setLoading(false);
        }
      );
    } else {
      this.setTrack({} as Track);
      this.setLoading(false);
    }
  }

  @Action
  getAllTracks() {
    this.setLoading(true);
    getData("tracks").then(res => {
      const tracks = res.data;
      this.setDataTable(tracks);
      this.setLoading(false);
    });
  }

  @Action
  clearTracks() {
    this.setLoading(true);
    getData("tracks").then(() => {
      const tracks = [];
      const track = {} as Track;
      this.setDataTable(tracks);
      this.setTrack(track);
      this.setLoading(false);
    });
  }

  @Action
  searchTracks(searchQuery: string) {
    getData("tracks" + searchQuery).then(res => {
      const tracks = res.data;
      this.setDataTable(tracks);
      this.setLoading(false);
    });
  }

  @Action
  getTrackByCode(searchQuery: string) {
    getData("tracks/" + searchQuery).then(res => {
      const tracks = res.data;
      this.setDataTable(tracks);
      this.setTrack(tracks);
      this.setLoading(false);
    });
  }

  @Action
  quickSearch(headers: TableHeader[], qsFilter: SeachQuery): void {
    getData("tracks").then(res => {
      const tracks = res.data.filter((r: TODO) =>
        headers.some((header: TODO) => {
          const val = get(r, [header.value]);
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter)) ||
            false
          );
        })
      );
      this.setDataTable(tracks);
      this.setLoading(false);
    });
  }

  @Action
  deleteTrack(id: number) {
    deleteData(`tracks/${id.toString()}`)
      .then(() => {
        this.getAllTracks();
        appModule.sendSuccessNotice("Operation is done.");
      })
      .catch((err: TODO) => {
        console.log(err);
        appModule.sendErrorNotice("Operation failed! Please try again later.");
        appModule.closeNoticeWithDelay(5000);
      });
  }

  @Action
  saveTrack(track: Track) {
    if (!track.id) {
      postData("tracks/", track)
        .then(res => {
          const track = res.data;
          this.setTrack(track);
          appModule.sendSuccessNotice("New record has been added.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    } else {
      putData("tracks/" + track.id.toString(), track)
        .then(res => {
          const track = res.data;
          this.setTrack(track);
          appModule.sendSuccessNotice("The record has been updated.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    }
  }

  @Action
  setDataTable(items: Track[]) {
    const pagination = getPagination(items);
    this.setPagination(pagination);
    this.setItems(items);
  }

  @Mutation
  setItems(tracks: Track[]) {
    this.items = tracks;
  }

  @Mutation
  setPagination(pagination: Pagination) {
    this.pagination = pagination;
  }

  @Mutation
  setLoading(loading: boolean) {
    this.loading = loading;
  }

  @Mutation
  setTrack(track: Track) {
    this.track = track;
  }
}

export const trackModule = getModule(TrackModule);
