const router = require("express").Router();
const moment = require('moment');

const inspectlinesDB = require("../models/inspectlines-model.js");
const categoriesDB = require("../models/categories-model.js");
const remarksDB = require("../models/remarks-model.js");

// GET INSPECTLINES BY ID
router.get("/:id", async (req, res) => {
  const inspectId = req.params.id;
  try {
    const inspectline = await inspectlinesDB.findById(inspectId);
    if (!inspectline) {
      res
        .status(404)
        .json({ err: "The inspectline with the specified id does not exist" });
    } else {
      const categories = await categoriesDB.find();
      const remarks = await remarksDB.find();
      inspectline.forEach(mem => {
        const pDate = moment(mem.productDate);
        const wDate = moment(mem.workDate);
        mem.dryTime = wDate.diff(pDate,'hours');
        mem.productDate = moment(mem.productDate).format("YYYY-M-D HH:mm");
        mem.workDate = moment(mem.workDate).format("YYYY-M-D HH:mm");
      });
      // Map categories to inspectlines
      inspectline.forEach(mem => {
        const category = categories.find(info => info.categoryId === mem.categoryId);
        if (category) {
          mem.categoryName = category.categoryName;
        }
      });
      // Map remarks to inspectlines
      inspectline.forEach(mem => {
        const remark = remarks.find(info => info.remarkId === mem.remarkId);
        if (remark) {
          mem.remarkName = remark.remarkName;
        }
      });
      res.status(200).json(inspectline);
    }
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET INSPECTLINES BY CODE
router.get("/duplicate/:id", async (req, res) => {
  const codeName = req.params.id;
  try {
    const exist = await inspectlinesDB.findByCode(codeName);
    if (Array.isArray(exist) && exist.length > 0) {
      res.status(200).json(exist);
    } else {
      res.status(404).json({ error: "Data not found" });
    }
  } catch (err) {
    console.error(err);
    res.status(500).json({ err: err.message });
  }
});

// INSRT INSPECTLINES INTO DB
router.post("/", async (req, res) => {
  const newInspectline = req.body;
  if (!newInspectline.inspectId) {
    res.status(404).json({ err: "Please provide the id" });
  } else {
    try {
      const inspectline = await inspectlinesDB.addInspectline(newInspectline);
      res.status(201).json(inspectline);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

// REMOVE INSPECTLINES INTO DB
router.delete("/:id", async (req, res) => {
  const inspectlineId = req.params.id;
  try {
    const deleting = await inspectlinesDB.removeInspectline(inspectlineId);
    res.status(204).json(deleting);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;
