<template>
  <v-navigation-drawer
    v-model="drawerModel"
    :rail="mini && !isMobile"
    :temporary="isMobile"
    :permanent="!isMobile"
    class="bg-blue-lighten-5"
    :width="isMobile ? 280 : (mini ? 64 : 280)"
  >
    <!-- 用戶信息區域 -->
    <NavigationUserInfo
      :user="user"
      :user-menus="userMenus"
      :is-mobile="isMobile"
      :mini="mini"
      @toggle-mini="$emit('toggle-mini')"
      @user-action="$emit('user-action', $event)"
    />

    <v-divider></v-divider>

    <!-- 主導航菜單 -->
    <NavigationMenu
      :items="menuItems"
      :active-item="activeMenuItem"
      :is-mobile="isMobile"
      @navigate="$emit('navigate', $event)"
    />

    <!-- Vue3 功能區域 -->
    <v-divider class="my-2"></v-divider>

    <NavigationSection
      title="Vue3 功能"
      :items="vue3Items"
      :active-item="activeMenuItem"
      :is-mobile="isMobile"
      :mini="mini"
      @navigate="$emit('navigate', $event)"
    />

    <!-- Android Layout 功能區域 -->
    <v-divider class="my-2"></v-divider>

    <NavigationSection
      title="Android Layout"
      :items="androidItems"
      :active-item="activeMenuItem"
      :is-mobile="isMobile"
      :mini="mini"
      @navigate="$emit('navigate', $event)"
    />

    <!-- 版本信息 -->
    <template v-slot:append>
      <div class="pa-2 text-center" v-if="!mini">
        <v-chip size="small" color="primary" variant="tonal">
          Vue 3.4 + Vuetify 3.7
        </v-chip>
      </div>
    </template>
  </v-navigation-drawer>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import NavigationUserInfo from './NavigationUserInfo.vue'
import NavigationMenu from './NavigationMenu.vue'
import NavigationSection from './NavigationSection.vue'

interface AppMenu {
  icon: string
  title: string
  vertical?: string
  link: string
  badge?: string
  color?: string
}

interface Props {
  modelValue: boolean
  user: any
  userMenus: AppMenu[]
  activeMenuItem: string
  isMobile: boolean
  mini: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'toggle-mini'): void
  (e: 'navigate', item: AppMenu): void
  (e: 'user-action', item: AppMenu): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// v-model 處理
const drawerModel = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 主要功能菜單
const menuItems: AppMenu[] = [
  {
    icon: "mdi-vuejs",
    title: "Vue3 表單測試中心",
    vertical: "Vue3TestCenter",
    link: "vue3-form-test-center",
    badge: "NEW",
    color: "success"
  },
  {
    icon: "mdi-test-tube",
    title: "Vue3 測試頁面",
    vertical: "Vue3Test",
    link: "vue3-test"
  }
]

// Vue3 功能菜單
const vue3Items: AppMenu[] = [
  {
    icon: "mdi-factory",
    title: "EM即時生產查詢",
    vertical: "EMProductionQuery",
    link: "em-production-query",
    badge: "即時",
    color: "primary"
  },
  {
    icon: "mdi-counter",
    title: "QI品檢計數作業",
    vertical: "ICountVue3",
    link: "icounts",
    badge: "Vue3"
  },
  {
    icon: "mdi-arrow-down-bold",
    title: "Downgrade T2",
    vertical: "DowngradeVue3",
    link: "downgrades",
    badge: "Vue3"
  },
  {
    icon: "mdi-delete",
    title: "Disposal Yarn",
    vertical: "DisposalVue3",
    link: "disposals",
    badge: "Vue3"
  },
  {
    icon: "mdi-label",
    title: "Relabel NO MFD",
    vertical: "RelabelVue3",
    link: "relabels",
    badge: "Vue3"
  },
  {
    icon: "mdi-package-variant",
    title: "Yarn盤點",
    vertical: "YarnInventoryVue3",
    link: "pinventoriesofyarn",
    badge: "Vue3"
  },
  {
    icon: "mdi-cake",
    title: "Cake盤點",
    vertical: "CakeInventoryVue3",
    link: "pinventoriesofcake",
    badge: "Vue3"
  },
  {
    icon: "mdi-package",
    title: "Pack盤點",
    vertical: "PackInventoryVue3",
    link: "pinventoriesofpack",
    badge: "Vue3"
  }
]

// Android Layout 功能菜單
const androidItems: AppMenu[] = [
  {
    icon: "mdi-note-multiple-outline",
    title: "EM查詢 (原版)",
    vertical: "EMOriginal",
    link: "test-vue3/em-production-query"
  },
  {
    icon: "mdi-android",
    title: "EM查詢 (Android版)",
    vertical: "EMAndroid",
    link: "test-vue3/em-production-query-android",
    badge: "Android",
    color: "green"
  },
  {
    icon: "mdi-cog",
    title: "EM查詢 (配置版)",
    vertical: "EMConfig",
    link: "test-vue3/em-production-query-config",
    badge: "Config",
    color: "purple"
  },
  {
    icon: "mdi-android",
    title: "Android Layout 測試",
    vertical: "AndroidTest",
    link: "android-layout-test",
    badge: "Test",
    color: "orange"
  }
]
</script>

<style scoped>
/* 導航抽屜樣式 */
.v-navigation-drawer {
  border-right: 1px solid rgba(0, 0, 0, 0.12) !important;
}

/* 深色模式適配 */
@media (prefers-color-scheme: dark) {
  .v-navigation-drawer {
    border-right: 1px solid rgba(255, 255, 255, 0.12) !important;
  }
}

/* 滾動條樣式 */
.v-navigation-drawer :deep(.v-navigation-drawer__content) {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.v-navigation-drawer :deep(.v-navigation-drawer__content)::-webkit-scrollbar {
  width: 6px;
}

.v-navigation-drawer :deep(.v-navigation-drawer__content)::-webkit-scrollbar-track {
  background: transparent;
}

.v-navigation-drawer :deep(.v-navigation-drawer__content)::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.v-navigation-drawer :deep(.v-navigation-drawer__content)::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}
</style>
