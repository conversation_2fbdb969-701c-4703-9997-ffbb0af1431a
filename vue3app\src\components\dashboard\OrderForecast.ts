import { Component, Vue, Ref, Prop } from 'vue-facing-decorator'
import { Line } from 'vue-chartjs'
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js'

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend)

@Component({
  template: '<div :style="styles"><canvas ref="chart" :height="height"></canvas></div>',
  name: 'OrderForecast<PERSON>hart'
})
export default class OrderForecast<PERSON>hart extends Vue {
  @Prop({ default: 150 }) readonly height!: number
  @Ref('chart') readonly chartRef!: HTMLCanvasElement

  private get styles() {
    return {
      "margin-left": '0px',
      "background": '#e8757857'
    }
  }

  mounted() {
    const ctx = this.chartRef.getContext('2d')
    const gradient = ctx?.createLinearGradient(0, 0, 0, 450)

    if (gradient) {
      gradient.addColorStop(0, 'rgba(255, 0,0, 0.5)')
      gradient.addColorStop(0.5, 'rgba(255, 0, 0, 0.25)')
      gradient.addColorStop(1, 'rgba(255, 0, 0, 0)')
    }

    const chart = new ChartJS(this.chartRef, {
      type: 'line',
      data: {
        labels: [
          "January",
          "February",
          "March",
          "April",
          "May",
          "June",
          "July"
        ],
        datasets: [
          {
            label: "Orders",
            backgroundColor: gradient || 'rgba(255, 0, 0, 0.5)',
            borderColor: "#fc2525",
            data: [40, 39, 10, 40, 39, 80, 40]
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }
}
