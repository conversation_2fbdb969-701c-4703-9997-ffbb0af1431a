import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getData, putData, postData, deleteData } from '@/utils/backend-api'
import type { Employee, Downgrade, Entity, Product, Category, Remark, Downgradeline, Pagination } from '@/types'

export const useDowngradesStore = defineStore('downgrades', () => {
  // State
  const items = ref<Entity[]>([])
  const pagination = ref<Pagination>({
    page: 1,
    rowsPerPage: 10,
    sortBy: [],
    descending: [],
    search: '',
    totalItems: 0,
    pages: 0
  })
  const loading = ref(false)
  const employee = ref('')
  const downgradeId = ref<number | null>(null)
  const downgrade = ref<Downgrade>({} as Downgrade)
  const downgradelines = ref<Downgradeline[]>([])
  const product = ref<Product[]>([])
  const employees = ref<Employee[]>([])
  const categories = ref<Category[]>([])
  const remarks = ref<Remark[]>([])

  // Getters
  const isLoading = computed(() => loading.value)
  const currentDowngrade = computed(() => downgrade.value)
  const currentDowngradelines = computed(() => downgradelines.value)

  // Actions
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setDowngrade = (value: Downgrade) => {
    // 如果是部分更新，合併現有資料
    if (downgrade.value && Object.keys(downgrade.value).length > 0) {
      downgrade.value = { ...downgrade.value, ...value }
    } else {
      downgrade.value = value
    }
  }

  const setDowngradelines = (value: Downgradeline[]) => {
    downgradelines.value = value
  }

  const setEmployees = (value: Employee[]) => {
    employees.value = value
  }

  const setCategories = (value: Category[]) => {
    categories.value = value
  }

  const setRemarks = (value: Remark[]) => {
    remarks.value = value
  }

  // API Actions
  const getEmployees = async () => {
    try {
      setLoading(true)
      const res = await getData("employees/qi")
      if (res.data) {
        // 先去重，再處理數據
        const uniqueData = res.data.filter((employee: any, index: number, self: any[]) =>
          index === self.findIndex((e: any) => e.employId === employee.employId)
        )

        const employeeList = uniqueData.map((c: any, index: number) => {
          return {
            // 確保每個項目都有唯一的 key，使用 employId 作為主鍵
            employId: c.employId,
            employNO: c.employNO,
            userName: c.userName,
            // 根據 Vue2 的邏輯，顯示格式為 employNO + userName
            employName: c.employNO + " " + c.userName,
            // 使用 employId 作為 value，與後端 API 一致
            value: c.employId,
            // 為 Vuetify 提供唯一的 key
            key: `employee_${c.employId}_${index}`,
            // 其他可能需要的欄位
            ...c
          }
        })

        console.log('Store: 員工數據處理完成:', employeeList)
        setEmployees(employeeList)
      }
    } catch (error) {
      console.error('獲取員工列表失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const getCategories = async () => {
    try {
      const res = await getData("categories")
      if (res.data) {
        setCategories(res.data)
      }
    } catch (error) {
      console.error('獲取分類列表失敗:', error)
      throw error
    }
  }

  const getRemarks = async () => {
    try {
      const res = await getData("remarks")
      if (res.data) {
        setRemarks(res.data)
      }
    } catch (error) {
      console.error('獲取備註列表失敗:', error)
      throw error
    }
  }

  const getDowngradeById = async (id: string | number) => {
    try {
      setLoading(true)
      console.log('Store: 開始獲取降級記錄，ID:', id)
      const res = await getData(`downgrades/${id}`)
      console.log('Store: API 響應:', res)

      if (res.data && Array.isArray(res.data) && res.data.length > 0) {
        // 後端返回的是數組，取第一個元素
        const downgradeData = res.data[0]
        console.log('Store: 設置主檔數據:', downgradeData)
        setDowngrade(downgradeData)
        downgradeId.value = Number(id)

        // 同時獲取明細檔
        if (downgradeData.downgradelines && Array.isArray(downgradeData.downgradelines)) {
          console.log('Store: 設置明細檔數據:', downgradeData.downgradelines)
          setDowngradelines(downgradeData.downgradelines)
        } else {
          console.log('Store: 沒有明細檔數據')
          setDowngradelines([])
        }
      } else {
        console.log('Store: API 響應格式不正確或無數據')
        setDowngrade({} as Downgrade)
        setDowngradelines([])
      }
      return res.data
    } catch (error) {
      console.error('獲取降級記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const saveDowngrade = async (data: Downgrade) => {
    try {
      setLoading(true)
      let res

      if (data.id && data.id > 0) {
        // 更新現有記錄 - 使用 PUT 方法
        console.log('Store: 更新現有記錄，ID:', data.id)
        res = await putData(`downgrades/${data.id}`, data)
        if (res.data) {
          // PUT 請求通常返回更新後的單個對象
          const updatedDowngrade = Array.isArray(res.data) ? res.data[0] : res.data
          setDowngrade(updatedDowngrade)
          downgradeId.value = updatedDowngrade.id
        }
      } else {
        // 創建新記錄 - 使用 POST 方法
        console.log('Store: 創建新記錄')
        res = await postData('downgrades/', data)
        if (res.data && Array.isArray(res.data) && res.data.length > 0) {
          // 後端返回數組，取第一個元素
          const newDowngrade = res.data[0]
          setDowngrade(newDowngrade)
          downgradeId.value = newDowngrade.id
        }
      }

      return res.data
    } catch (error) {
      console.error('保存降級記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const updateDowngrade = async (data: Downgrade) => {
    return await saveDowngrade(data)
  }

  const deleteDowngrade = async (id: number) => {
    try {
      setLoading(true)
      await deleteData(`downgrades/${id}`)

      // 如果刪除的是當前記錄，清空狀態
      if (downgradeId.value === id) {
        downgrade.value = {} as Downgrade
        downgradelines.value = []
        downgradeId.value = null
      }
    } catch (error) {
      console.error('刪除降級記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 明細檔操作
  const addDowngradelineToDowngrade = async (data: Downgradeline) => {
    try {
      const res = await postData('downgradelines/', data)
      if (res.data) {
        // 重新載入明細檔
        if (downgradeId.value) {
          await getDowngradeById(downgradeId.value)
        }
      }
      return res.data
    } catch (error) {
      console.error('新增明細記錄失敗:', error)
      throw error
    }
  }

  const updateDowngradeline = async (data: Downgradeline) => {
    try {
      const res = await putData(`downgradelines/${data.id}`, data)
      if (res.data) {
        // 重新載入明細檔
        if (downgradeId.value) {
          await getDowngradeById(downgradeId.value)
        }
      }
      return res.data
    } catch (error) {
      console.error('更新明細記錄失敗:', error)
      throw error
    }
  }

  const deleteDowngradeline = async (data: Downgradeline) => {
    try {
      await deleteData(`downgradelines/${data.id}`)

      // 重新載入明細檔
      if (downgradeId.value) {
        await getDowngradeById(downgradeId.value)
      }
    } catch (error) {
      console.error('刪除明細記錄失敗:', error)
      throw error
    }
  }

  // 檢查重複的 QR Code
  const getDuplicateDowngradelineByCode = async (code: string): Promise<boolean> => {
    try {
      setLoading(true)
      if (code) {
        const res = await getData(`downgradelines/duplicate/${code}`)
        const data = res.data
        if (data !== undefined && data !== null && Array.isArray(data) && data.length > 0) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    } catch (error: any) {
      // 404 錯誤表示沒有重複，這是正常情況
      if (error?.response?.status === 404) {
        console.log('沒有重複的 QR Code，可以繼續')
        return false
      }
      console.error('檢查重複 QR Code 失敗:', error)
      return false
    } finally {
      setLoading(false)
    }
  }

  // 載入降級列表
  const loadDowngrades = async (page: number = 1, limit: number = 10) => {
    try {
      setLoading(true)
      const response = await getData('downgrades/t2')

      if (response.data && Array.isArray(response.data)) {
        items.value = response.data
        // 由於後端沒有分頁，我們在前端模擬分頁
        const totalItems = response.data.length
        const startIndex = (page - 1) * limit
        const endIndex = startIndex + limit
        const paginatedItems = response.data.slice(startIndex, endIndex)

        items.value = paginatedItems
        pagination.value = {
          ...pagination.value,
          page,
          limit,
          totalItems,
          totalPages: Math.ceil(totalItems / limit),
          hasNextPage: page < Math.ceil(totalItems / limit),
          hasPrevPage: page > 1
        }
      } else {
        items.value = []
        pagination.value = {
          ...pagination.value,
          page: 1,
          limit: 10,
          totalItems: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPrevPage: false
        }
      }
      return items.value
    } catch (error) {
      console.error('載入降級列表失敗:', error)
      items.value = []
      pagination.value = {
        ...pagination.value,
        page: 1,
        limit: 10,
        totalItems: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPrevPage: false
      }
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 清空明細檔
  const clearDowngradeline = () => {
    setLoading(true)
    setDowngradelines([])
    setLoading(false)
  }

  // 獲取產品資料 (與vue2app相同的API)
  const getProductById = async (id: string) => {
    try {
      setLoading(true)
      if (id) {
        const res = await getData(`products/${id}`)
        const productData = res.data
        // 設置到products store中，保持與現有邏輯一致
        if (productData && Array.isArray(productData)) {
          // 如果返回的是數組，直接使用
          product.value = productData
        } else if (productData) {
          // 如果返回的是單個對象，包裝成數組
          product.value = [productData]
        } else {
          product.value = []
        }
        return productData
      } else {
        product.value = []
        return null
      }
    } catch (error) {
      console.error('獲取產品資料失敗:', error)
      product.value = []
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 重置狀態
  const resetState = () => {
    items.value = []
    downgrade.value = {} as Downgrade
    downgradelines.value = []
    downgradeId.value = null
    employee.value = ''
    loading.value = false
  }

  return {
    // State
    items,
    pagination,
    loading,
    employee,
    downgradeId,
    downgrade,
    downgradelines,
    product,
    employees,
    categories,
    remarks,

    // Getters
    isLoading,
    currentDowngrade,
    currentDowngradelines,

    // Actions
    setLoading,
    setDowngrade,
    setDowngradelines,
    setEmployees,
    setCategories,
    setRemarks,
    getEmployees,
    getCategories,
    getRemarks,
    getDowngradeById,
    saveDowngrade,
    updateDowngrade,
    deleteDowngrade,
    addDowngradelineToDowngrade,
    updateDowngradeline,
    deleteDowngradeline,
    getDuplicateDowngradelineByCode,
    loadDowngrades,
    clearDowngradeline,
    getProductById,
    resetState
  }
})
