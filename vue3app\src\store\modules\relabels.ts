import { Component, Vue } from 'vue-facing-decorator'
import { createApp } from 'vue'
import { getData, putData, postData, deleteData } from '@/utils/backend-api'
import { Employee, Relabel, Entity, Product, Category, Relabelline, Pagination, TableHeader } from '@/types'
import { getDefaultPagination, getPagination } from '@/utils/store-util'
import { appModule } from '@/stores/app'
import { get } from 'lodash'

export interface RelabelState {
  items: Entity[]
  pagination: Pagination
  loading: boolean
  employee: string
  relabelId: number | null
  relabel: Relabel
  relabelline: Relabelline[]
  product: Product
  employees: Employee[]
  categories: Category[]
}

@Component({
  name: 'RelabelModule'
})
class RelabelModule extends Vue implements RelabelState {
  items: Entity[] = []
  pagination = getDefaultPagination()
  loading = false
  employee = ""
  relabelId: number | null = null
  relabel = {} as Relabel
  relabelline: Relabelline[] = []
  product = {} as Product
  employees: Employee[] = []
  categories: Category[] = []

  getEmployees = () => {
    getData("employees/tw").then(res => {
      if (res.data) {
        const employees = res.data.map((c: Employee) => {
          c.employName = c.employNO + " " + c.userName
          c.value = c.id
          return c
        })
        this.setEmployees(employees)
      }
    })
  }

  getAllRelabelsByType = async (type: string) => {
    this.setLoading(true)
    try {
      const res = await getData(`relabels/${type}`)
      const relabels = res.data
      this.setRelabel(relabels)
      this.setDataTable(relabels)
    } catch (error) {
      console.error(error)
    } finally {
      this.setLoading(false)
    }
  }

  getRelabelById = (id: string) => {
    if (id) {
      getData("relabels/" + id).then(
        res => {
          const _relabel = res.data
          const relabel = _relabel[0]
          relabel.relabellines = relabel.relabellines?.filter(
            (p: Relabel) => p !== null && p !== undefined
          ) || []
          relabel.quantity = relabel.relabellines?.length || 0
          this.setRelabel(relabel)
          this.setDataTable(relabel.relabellines)
        },
        err => {
          console.log(err)
        }
      )
    } else {
      const relabel = {} as Relabel
      relabel.relabellines = []
      this.setRelabel(relabel)
      this.setLoading(false)
    }
  }

  getProductById = async (id: string) => {
    try {
      this.setLoading(true)
      if (id) {
        const res = await getData("products/" + id)
        const product = res.data
        this.setProduct(product)
      } else {
        this.setProduct({} as Product)
      }
    } catch (err) {
      console.log(err)
    } finally {
      this.setLoading(false)
    }
  }

  getDuplicateRelabellineByCode = async (code: string): Promise<boolean> => {
    try {
      this.setLoading(true)
      if (code) {
        const res = await getData("relabellines/duplicate/" + code)
        const data = res.data
        if (data !== undefined && data !== null) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    } catch (err) {
      console.log(err)
      return false
    } finally {
      this.setLoading(false)
    }
  }

  searchRelabels = (searchQuery: string) => {
    getData("relabels" + searchQuery).then(res => {
      const relabels = res.data
      relabels.forEach((item: Relabel) => {
        item.quantity = item.relabellines?.length || 0
      })
      this.setDataTable(relabels)
      this.setLoading(false)
    })
  }

  quickSearch = (headers: TableHeader[], qsFilter: string): void => {
    getData("relabels").then(res => {
      const relabels = res.data.filter((r: any) =>
        headers.some((header: TableHeader) => {
          const val = get(r, [header.value])
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter)) ||
            false
          )
        })
      )
      relabels.forEach((item: Relabel) => {
        item.quantity = item.relabellines?.length || 0
      })
      this.setDataTable(relabels)
      this.setLoading(false)
    })
  }

  saveRelabel = (relabel: Relabel): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!relabel.id) {
        postData("relabels/", relabel)
          .then(res => {
            const relabel = res.data
            const RelabelId = { id: relabel[0].id }
            const addRelabel = { ...RelabelId, ...this.relabel }
            this.setRelabel(addRelabel)
            if (addRelabel.id !== undefined) {
              this.setRelabelId(addRelabel.id)
            }
            appModule.sendSuccessNotice("New record has been added.")
            appModule.closeNoticeWithDelay(3000)
            resolve()
          })
          .catch(err => {
            console.log(err)
            appModule.sendErrorNotice("Operation failed! Please try again later.")
            appModule.closeNoticeWithDelay(5000)
            reject(err)
          })
      } else {
        putData("relabels/" + relabel.id.toString(), relabel)
          .then(() => {
            appModule.sendSuccessNotice("The record has been updated.")
            appModule.closeNoticeWithDelay(3000)
            resolve()
          })
          .catch(err => {
            console.log(err)
            appModule.sendErrorNotice("Operation failed! Please try again later.")
            appModule.closeNoticeWithDelay(5000)
            reject(err)
          })
      }
    })
  }

  addRelabellineToRelabel = (relabelline: Relabelline) => {
    if (relabelline && this.relabel.id) {
      this.saveRelabelline(relabelline)
      this.getRelabelById(this.relabel.id.toString())
      const newRelabel = this.relabel
      this.setRelabel(newRelabel)
    }
  }

  saveRelabelline = (relabelline: Relabelline) => {
    if (!relabelline.id) {
      postData("relabellines/", relabelline)
        .then(res => {
          const relabelline = res.data
          this.setRelabelline([relabelline])
          appModule.sendSuccessNotice("New record has been added.")
          appModule.closeNoticeWithDelay(3000)
        })
        .catch(err => {
          console.log(err)
          appModule.sendErrorNotice("Operation failed! Please try again later.")
          appModule.closeNoticeWithDelay(3000)
        })
    } else {
      putData("relabellines/" + relabelline.id.toString(), relabelline)
        .then(res => {
          const relabel = res.data
          this.setRelabel(relabel)
          appModule.sendSuccessNotice("The record has been updated.")
          appModule.closeNoticeWithDelay(3000)
        })
        .catch(err => {
          console.log(err)
          appModule.sendErrorNotice("Operation failed! Please try again later.")
          appModule.closeNoticeWithDelay(5000)
        })
    }
  }

  deleteRelabel = async (id: number) => {
    try {
      await deleteData(`relabels/${id.toString()}`)
      appModule.sendSuccessNotice("Operation is done.")
      appModule.closeNoticeWithDelay(3000)
    } catch (error) {
      console.error(error)
      appModule.sendErrorNotice("Operation failed! Please try again later.")
      appModule.closeNoticeWithDelay(5000)
    } finally {
      this.setLoading(false)
    }
  }

  deleteRelabelline = (relabelline: Relabelline) => {
    if (relabelline && relabelline.id && this.relabel.id) {
      const relabelId = this.relabel.id
      const relabellineId = relabelline.id
      const { relabellines } = this.relabel
      if (relabellines) {
        relabellines.splice(
          relabellines.findIndex((p: Relabelline) => p.id === relabelline.id),
          1
        )
        this.setRelabelline(relabellines)
        deleteData(`relabellines/${relabellineId.toString()}`)
          .then(() => {
            this.getRelabelById(relabelId.toString())
            const newRelabel = this.relabel
            this.setRelabel(newRelabel)
            appModule.sendSuccessNotice("Operation is done.")
            appModule.closeNoticeWithDelay(3000)
          })
          .catch(err => {
            console.log(err)
            appModule.sendErrorNotice("Operation failed! Please try again later.")
            appModule.closeNoticeWithDelay(5000)
          })
      }
    }
  }

  clearRelabelline = () => {
    this.setLoading(true)
    const relabelline: Relabelline[] = []
    this.setRelabelline(relabelline)
    this.setLoading(false)
  }

  setDataTable = (items: Relabel[]) => {
    const pagination = getPagination(items)
    this.setPagination(pagination)
    this.setItems(items)
  }

  setEmployees = (employees: Employee[]) => {
    this.employees = employees
  }

  setCategories = (categories: Category[]) => {
    this.categories = categories
  }

  setRelabelId = (id: number | null) => {
    this.relabelId = id
  }

  setRelabel = (relabel: Relabel) => {
    this.relabel = relabel
  }

  setRelabelline = (relabelline: Relabelline[]) => {
    this.relabelline = relabelline
  }

  setProduct = (product: Product) => {
    this.product = product
  }

  setItems = (relabels: Relabel[]) => {
    this.items = relabels
  }

  setPagination = (pagination: Pagination) => {
    this.pagination = pagination
  }

  setLoading = (loading: boolean) => {
    this.loading = loading
  }
}

// Create and export a singleton instance
const app = createApp(RelabelModule)
const vm = app.mount(document.createElement('div'))
export const relabelModule = vm as InstanceType<typeof RelabelModule>
