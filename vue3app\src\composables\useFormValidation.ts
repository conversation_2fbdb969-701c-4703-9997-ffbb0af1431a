import { ref, computed, watch, nextTick } from 'vue'

// 驗證規則類型
export type ValidationRule = (value: any) => boolean | string

// 欄位驗證配置
export interface FieldValidation {
  rules: ValidationRule[]
  required?: boolean
  message?: string
}

// 表單驗證配置
export interface FormValidationConfig {
  [fieldName: string]: FieldValidation
}

// 驗證結果
export interface ValidationResult {
  isValid: boolean
  errors: { [fieldName: string]: string[] }
  firstError?: string
}

// 常用驗證規則
export const validationRules = {
  required: (message = '此欄位為必填') => (value: any) => {
    if (value === null || value === undefined || value === '') {
      return message
    }
    if (Array.isArray(value) && value.length === 0) {
      return message
    }
    return true
  },

  minLength: (min: number, message?: string) => (value: string) => {
    if (!value) return true // 讓 required 規則處理空值
    if (value.length < min) {
      return message || `最少需要 ${min} 個字元`
    }
    return true
  },

  maxLength: (max: number, message?: string) => (value: string) => {
    if (!value) return true
    if (value.length > max) {
      return message || `最多只能 ${max} 個字元`
    }
    return true
  },

  exactLength: (length: number, message?: string) => (value: string) => {
    if (!value) return true
    if (value.length !== length) {
      return message || `必須為 ${length} 個字元`
    }
    return true
  },

  min: (min: number, message?: string) => (value: number) => {
    if (value === null || value === undefined) return true
    if (value < min) {
      return message || `最小值為 ${min}`
    }
    return true
  },

  max: (max: number, message?: string) => (value: number) => {
    if (value === null || value === undefined) return true
    if (value > max) {
      return message || `最大值為 ${max}`
    }
    return true
  },

  positive: (message = '必須為正數') => (value: number) => {
    if (value === null || value === undefined) return true
    if (value <= 0) {
      return message
    }
    return true
  },

  email: (message = '請輸入有效的電子郵件地址') => (value: string) => {
    if (!value) return true
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(value)) {
      return message
    }
    return true
  },

  phone: (message = '請輸入有效的電話號碼') => (value: string) => {
    if (!value) return true
    const phoneRegex = /^[\d\-\+\(\)\s]+$/
    if (!phoneRegex.test(value)) {
      return message
    }
    return true
  },

  pattern: (regex: RegExp, message = '格式不正確') => (value: string) => {
    if (!value) return true
    if (!regex.test(value)) {
      return message
    }
    return true
  },

  custom: (validator: (value: any) => boolean, message = '驗證失敗') => (value: any) => {
    if (!validator(value)) {
      return message
    }
    return true
  }
}

// 表單驗證組合式函數
export function useFormValidation(formData: any, config: FormValidationConfig) {
  const formRef = ref()
  const isValid = ref(false)
  const errors = ref<{ [key: string]: string[] }>({})
  const touched = ref<{ [key: string]: boolean }>({})

  // 驗證單一欄位
  const validateField = (fieldName: string, value: any): string[] => {
    const fieldConfig = config[fieldName]
    if (!fieldConfig) return []

    const fieldErrors: string[] = []
    
    for (const rule of fieldConfig.rules) {
      const result = rule(value)
      if (result !== true) {
        fieldErrors.push(typeof result === 'string' ? result : '驗證失敗')
        break // 只顯示第一個錯誤
      }
    }

    return fieldErrors
  }

  // 驗證所有欄位
  const validateAll = (): ValidationResult => {
    const allErrors: { [key: string]: string[] } = {}
    let hasErrors = false

    for (const fieldName in config) {
      const fieldValue = getNestedValue(formData.value || formData, fieldName)
      const fieldErrors = validateField(fieldName, fieldValue)
      
      if (fieldErrors.length > 0) {
        allErrors[fieldName] = fieldErrors
        hasErrors = true
      }
    }

    errors.value = allErrors
    isValid.value = !hasErrors

    const firstError = hasErrors ? Object.values(allErrors)[0][0] : undefined

    return {
      isValid: !hasErrors,
      errors: allErrors,
      firstError
    }
  }

  // 驗證表單 (兼容 Vuetify 的 validate 方法)
  const validate = (): boolean => {
    const result = validateAll()
    
    // 標記所有欄位為已觸碰
    for (const fieldName in config) {
      touched.value[fieldName] = true
    }

    // 如果有 Vuetify 表單引用，也調用其驗證
    if (formRef.value && formRef.value.validate) {
      return formRef.value.validate() && result.isValid
    }

    return result.isValid
  }

  // 重置驗證
  const resetValidation = () => {
    errors.value = {}
    touched.value = {}
    isValid.value = false
    
    if (formRef.value && formRef.value.resetValidation) {
      formRef.value.resetValidation()
    }
  }

  // 重置表單
  const reset = () => {
    resetValidation()
    
    if (formRef.value && formRef.value.reset) {
      formRef.value.reset()
    }
  }

  // 標記欄位為已觸碰
  const touch = (fieldName: string) => {
    touched.value[fieldName] = true
  }

  // 獲取欄位錯誤
  const getFieldErrors = (fieldName: string): string[] => {
    return errors.value[fieldName] || []
  }

  // 獲取欄位的第一個錯誤
  const getFieldError = (fieldName: string): string | undefined => {
    const fieldErrors = getFieldErrors(fieldName)
    return fieldErrors.length > 0 ? fieldErrors[0] : undefined
  }

  // 檢查欄位是否有錯誤
  const hasFieldError = (fieldName: string): boolean => {
    return getFieldErrors(fieldName).length > 0
  }

  // 檢查欄位是否已觸碰
  const isFieldTouched = (fieldName: string): boolean => {
    return touched.value[fieldName] || false
  }

  // 獲取 Vuetify 驗證規則
  const getVuetifyRules = (fieldName: string): ValidationRule[] => {
    const fieldConfig = config[fieldName]
    if (!fieldConfig) return []
    return fieldConfig.rules
  }

  // 計算屬性：表單是否有任何錯誤
  const hasErrors = computed(() => {
    return Object.keys(errors.value).length > 0
  })

  // 計算屬性：第一個錯誤訊息
  const firstError = computed(() => {
    if (!hasErrors.value) return undefined
    const firstFieldErrors = Object.values(errors.value)[0]
    return firstFieldErrors.length > 0 ? firstFieldErrors[0] : undefined
  })

  // 監聽表單數據變化，進行即時驗證
  watch(
    () => formData.value || formData,
    (newData) => {
      if (!newData) return
      
      // 只驗證已觸碰的欄位
      for (const fieldName in config) {
        if (touched.value[fieldName]) {
          const fieldValue = getNestedValue(newData, fieldName)
          const fieldErrors = validateField(fieldName, fieldValue)
          
          if (fieldErrors.length > 0) {
            errors.value[fieldName] = fieldErrors
          } else {
            delete errors.value[fieldName]
          }
        }
      }
      
      // 更新整體驗證狀態
      isValid.value = Object.keys(errors.value).length === 0
    },
    { deep: true }
  )

  return {
    // 表單引用
    formRef,
    
    // 驗證狀態
    isValid,
    errors,
    touched,
    hasErrors,
    firstError,
    
    // 驗證方法
    validate,
    validateField,
    validateAll,
    resetValidation,
    reset,
    touch,
    
    // 錯誤獲取方法
    getFieldErrors,
    getFieldError,
    hasFieldError,
    isFieldTouched,
    
    // Vuetify 整合
    getVuetifyRules
  }
}

// 輔助函數：獲取嵌套物件的值
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined
  }, obj)
}

// 預設驗證配置生成器
export function createValidationConfig(): FormValidationConfig {
  return {}
}

// 常用欄位驗證配置
export const commonFieldValidations = {
  // 必填文字欄位
  requiredText: (message?: string): FieldValidation => ({
    rules: [validationRules.required(message)]
  }),

  // 必填選擇欄位
  requiredSelect: (message?: string): FieldValidation => ({
    rules: [validationRules.required(message)]
  }),

  // 必填數字欄位
  requiredNumber: (message?: string): FieldValidation => ({
    rules: [
      validationRules.required(message),
      validationRules.positive()
    ]
  }),

  // 電子郵件欄位
  email: (): FieldValidation => ({
    rules: [
      validationRules.required('請輸入電子郵件'),
      validationRules.email()
    ]
  }),

  // 電話號碼欄位
  phone: (): FieldValidation => ({
    rules: [
      validationRules.required('請輸入電話號碼'),
      validationRules.phone()
    ]
  }),

  // QR Code 欄位 (14位)
  qrCode: (): FieldValidation => ({
    rules: [
      validationRules.required('請輸入QR Code'),
      validationRules.exactLength(14, 'QR Code必須為14位')
    ]
  }),

  // 日期欄位
  date: (): FieldValidation => ({
    rules: [validationRules.required('請選擇日期')]
  }),

  // 數量欄位
  quantity: (): FieldValidation => ({
    rules: [
      validationRules.required('請輸入數量'),
      validationRules.positive('數量必須大於0')
    ]
  })
}
