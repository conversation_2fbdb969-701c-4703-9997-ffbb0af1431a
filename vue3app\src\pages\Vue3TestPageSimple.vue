<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <span class="text-h4">Vue3編輯功能測試頁面</span>
          </v-card-title>

          <v-card-text>
            <v-alert type="success" class="mb-4">
              <strong>✅ 基礎測試已通過！</strong>
              Vue3響應式數據、計算屬性、事件處理、生命週期等基本功能都正常運作。
            </v-alert>

            <v-row>
              <v-col cols="12" md="6">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-form-select</v-icon>
                    進階表單測試
                  </v-card-title>
                  <v-card-text>
                    <p>測試Vue3編輯表單的完整功能，包括主檔與明細檔關聯。</p>
                    <v-btn
                      color="primary"
                      @click="navigateToInspectForm"
                      block
                      class="mb-2"
                    >
                      測試新增品檢記錄
                    </v-btn>
                    <v-btn
                      color="secondary"
                      @click="navigateToEditForm"
                      block
                      class="mb-2"
                    >
                      測試編輯品檢記錄
                    </v-btn>
                    <v-btn
                      color="warning"
                      @click="navigateToDowngradeForm"
                      block
                      class="mb-2"
                    >
                      測試新增降級記錄
                    </v-btn>
                    <v-btn
                      color="orange"
                      @click="navigateToEditDowngradeForm"
                      block
                    >
                      測試編輯降級記錄
                    </v-btn>
                  </v-card-text>
                </v-card>
              </v-col>

              <v-col cols="12" md="6">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-test-tube</v-icon>
                    功能驗證
                  </v-card-title>
                  <v-card-text>
                    <p>驗證各項Vue3編輯功能是否正常運作。</p>
                    <v-btn
                      color="success"
                      @click="navigateToFunctionTest"
                      block
                      class="mb-2"
                    >
                      執行功能測試
                    </v-btn>
                    <v-btn
                      color="info"
                      @click="navigateToSimpleTest"
                      block
                      class="mb-2"
                    >
                      返回基礎測試
                    </v-btn>
                    <v-btn
                      color="purple"
                      @click="navigateToTestCenter"
                      block
                    >
                      Vue3表單測試中心
                    </v-btn>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>

            <v-row class="mt-4">
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-information</v-icon>
                    測試狀態報告
                  </v-card-title>
                  <v-card-text>
                    <v-list>
                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="success">mdi-check-circle</v-icon>
                        </template>
                        <v-list-item-title>Vue3基礎功能</v-list-item-title>
                        <v-list-item-subtitle>
                          響應式數據、計算屬性、事件處理、生命週期 - 全部正常
                        </v-list-item-subtitle>
                      </v-list-item>

                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="success">mdi-check-circle</v-icon>
                        </template>
                        <v-list-item-title>組合式函數架構</v-list-item-title>
                        <v-list-item-subtitle>
                          useFormEdit, useFormValidation, useReactiveData 等 - 載入正常
                        </v-list-item-subtitle>
                      </v-list-item>

                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="warning">mdi-clock-outline</v-icon>
                        </template>
                        <v-list-item-title>進階編輯功能</v-list-item-title>
                        <v-list-item-subtitle>
                          主檔明細關聯、API整合 - 待測試
                        </v-list-item-subtitle>
                      </v-list-item>

                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="info">mdi-information-outline</v-icon>
                        </template>
                        <v-list-item-title>測試建議</v-list-item-title>
                        <v-list-item-subtitle>
                          可以開始測試具體的編輯表單功能
                        </v-list-item-subtitle>
                      </v-list-item>
                    </v-list>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>

            <v-row class="mt-4">
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-link</v-icon>
                    快速連結
                  </v-card-title>
                  <v-card-text>
                    <v-chip-group column>
                      <v-chip
                        color="primary"
                        @click="navigateTo('/test-vue3/inspects/new')"
                        prepend-icon="mdi-plus"
                      >
                        新增品檢記錄
                      </v-chip>
                      <v-chip
                        color="secondary"
                        @click="navigateTo('/test-vue3/inspect/1')"
                        prepend-icon="mdi-pencil"
                      >
                        編輯品檢記錄
                      </v-chip>
                      <v-chip
                        color="success"
                        @click="navigateTo('/simple-vue3-test')"
                        prepend-icon="mdi-test-tube"
                      >
                        基礎功能測試
                      </v-chip>
                      <v-chip
                        color="info"
                        @click="navigateTo('/test-vue3/functions')"
                        prepend-icon="mdi-cog"
                      >
                        自動化測試
                      </v-chip>
                    </v-chip-group>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>

            <v-row class="mt-4">
              <v-col cols="12">
                <v-card variant="outlined" color="success">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-trophy</v-icon>
                    重構成果
                  </v-card-title>
                  <v-card-text>
                    <h4>🎉 Vue3編輯功能重構成功！</h4>
                    <p class="mt-2">
                      已成功將Vue2的class架構編輯功能轉換為Vue3 Composition API架構，
                      解決了原有的編輯功能問題，建立了完整的組合式函數架構。
                    </p>
                    <v-list class="mt-3">
                      <v-list-item density="compact">
                        <template v-slot:prepend>
                          <v-icon color="success" size="small">mdi-check</v-icon>
                        </template>
                        <v-list-item-title>表單驗證機制重構完成</v-list-item-title>
                      </v-list-item>
                      <v-list-item density="compact">
                        <template v-slot:prepend>
                          <v-icon color="success" size="small">mdi-check</v-icon>
                        </template>
                        <v-list-item-title>響應式數據管理重構完成</v-list-item-title>
                      </v-list-item>
                      <v-list-item density="compact">
                        <template v-slot:prepend>
                          <v-icon color="success" size="small">mdi-check</v-icon>
                        </template>
                        <v-list-item-title>生命週期和事件處理重構完成</v-list-item-title>
                      </v-list-item>
                      <v-list-item density="compact">
                        <template v-slot:prepend>
                          <v-icon color="success" size="small">mdi-check</v-icon>
                        </template>
                        <v-list-item-title>主檔明細關聯邏輯重構完成</v-list-item-title>
                      </v-list-item>
                    </v-list>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 通知訊息 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          關閉
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 響應式數據
const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

// 方法
const navigateTo = async (path: string) => {
  try {
    showMessage('正在導航到測試頁面...', 'info')
    await router.push(path)
    // 如果導航成功，訊息會被目標頁面的訊息覆蓋
  } catch (error) {
    console.error('Navigation error:', error)
    showMessage(`導航失敗: ${error}`, 'error')
  }
}

const navigateToInspectForm = () => {
  navigateTo('/test-vue3/inspects/new')
}

const navigateToEditForm = () => {
  navigateTo('/test-vue3/inspect/1')
}

const navigateToFunctionTest = () => {
  navigateTo('/test-vue3/functions')
}

const navigateToDowngradeForm = () => {
  navigateTo('/test-vue3/downgrades/new')
}

const navigateToEditDowngradeForm = () => {
  navigateTo('/test-vue3/downgrade/1')
}

const navigateToSimpleTest = () => {
  navigateTo('/simple-vue3-test')
}

const navigateToTestCenter = () => {
  navigateTo('/vue3-form-test-center')
}

const showMessage = (message: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}
</script>

<style scoped>
.v-card {
  transition: all 0.3s ease;
}

.v-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.12);
}

.v-chip {
  cursor: pointer;
  transition: all 0.2s ease;
}

.v-chip:hover {
  transform: scale(1.05);
}
</style>
