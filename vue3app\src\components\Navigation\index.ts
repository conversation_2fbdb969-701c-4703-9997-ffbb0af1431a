/**
 * Navigation 組件統一導出
 */

// 組件導入
import NavigationDrawer from './NavigationDrawer.vue'
import NavigationUserInfo from './NavigationUserInfo.vue'
import NavigationMenu from './NavigationMenu.vue'
import NavigationSection from './NavigationSection.vue'

// 組件導出
export {
  NavigationDrawer,
  NavigationUserInfo,
  NavigationMenu,
  NavigationSection
}

// 預設導出
export default {
  NavigationDrawer,
  NavigationUserInfo,
  NavigationMenu,
  NavigationSection
}

// 類型定義
export interface AppMenu {
  icon: string
  title: string
  vertical?: string
  link: string
  badge?: string
  color?: string
}

export interface NavigationConfig {
  // 主要功能菜單
  mainItems: AppMenu[]
  
  // Vue3 功能菜單
  vue3Items: AppMenu[]
  
  // Android Layout 功能菜單
  androidItems: AppMenu[]
  
  // 用戶菜單
  userMenus: AppMenu[]
}

// 預設導航配置
export const defaultNavigationConfig: NavigationConfig = {
  mainItems: [
    {
      icon: "mdi-vuejs",
      title: "Vue3 表單測試中心",
      vertical: "Vue3TestCenter",
      link: "vue3-form-test-center",
      badge: "NEW",
      color: "success"
    },
    {
      icon: "mdi-test-tube",
      title: "Vue3 測試頁面",
      vertical: "Vue3Test",
      link: "vue3-test"
    },
    {
      icon: "mdi-note-multiple-outline",
      title: "EM即時生產查詢",
      vertical: "Tracksheets",
      link: "tracksheets"
    }
  ],
  
  vue3Items: [
    {
      icon: "mdi-clipboard-check",
      title: "品質檢驗記錄",
      vertical: "InspectVue3",
      link: "test-vue3/inspects/new",
      badge: "Vue3"
    },
    {
      icon: "mdi-alert-circle",
      title: "QI品檢異常登錄",
      vertical: "QIAbnormalVue3",
      link: "test-vue3/qi-abnormals/new",
      badge: "Vue3"
    },
    {
      icon: "mdi-counter",
      title: "QI品檢計數作業",
      vertical: "ICountVue3",
      link: "test-vue3/icounts/new",
      badge: "Vue3"
    },
    {
      icon: "mdi-arrow-down-bold",
      title: "降級異常",
      vertical: "DowngradeVue3",
      link: "test-vue3/downgrades/new",
      badge: "Vue3"
    },
    {
      icon: "mdi-delete",
      title: "報廢異常",
      vertical: "DisposalVue3",
      link: "test-vue3/disposals/new",
      badge: "Vue3"
    },
    {
      icon: "mdi-label",
      title: "Relabel NO MFD",
      vertical: "RelabelVue3",
      link: "test-vue3/relabels/new",
      badge: "Vue3"
    },
    {
      icon: "mdi-package-variant",
      title: "Yarn盤點",
      vertical: "YarnInventoryVue3",
      link: "test-vue3/pinventory-yarn/new",
      badge: "Vue3"
    },
    {
      icon: "mdi-cake",
      title: "Cake盤點",
      vertical: "CakeInventoryVue3",
      link: "test-vue3/pinventory-cake/new",
      badge: "Vue3"
    },
    {
      icon: "mdi-package",
      title: "Pack盤點",
      vertical: "PackInventoryVue3",
      link: "test-vue3/pinventory-pack/new",
      badge: "Vue3"
    }
  ],
  
  androidItems: [
    {
      icon: "mdi-note-multiple-outline",
      title: "EM查詢 (原版)",
      vertical: "EMOriginal",
      link: "test-vue3/em-production-query"
    },
    {
      icon: "mdi-android",
      title: "EM查詢 (Android版)",
      vertical: "EMAndroid",
      link: "test-vue3/em-production-query-android",
      badge: "Android",
      color: "green"
    },
    {
      icon: "mdi-cog",
      title: "EM查詢 (配置版)",
      vertical: "EMConfig",
      link: "test-vue3/em-production-query-config",
      badge: "Config",
      color: "purple"
    },
    {
      icon: "mdi-android",
      title: "Android Layout 測試",
      vertical: "AndroidTest",
      link: "android-layout-test",
      badge: "Test",
      color: "orange"
    }
  ],
  
  userMenus: [
    {
      icon: "mdi-logout",
      title: "Logout",
      link: "login"
    },
    {
      icon: "mdi-key",
      title: "Change Password",
      link: "changepassword"
    }
  ]
}

// 工具函數
export const createNavigationConfig = (overrides: Partial<NavigationConfig> = {}): NavigationConfig => {
  return {
    mainItems: overrides.mainItems || defaultNavigationConfig.mainItems,
    vue3Items: overrides.vue3Items || defaultNavigationConfig.vue3Items,
    androidItems: overrides.androidItems || defaultNavigationConfig.androidItems,
    userMenus: overrides.userMenus || defaultNavigationConfig.userMenus
  }
}

// 導航項目過濾器
export const filterNavigationItems = (items: AppMenu[], filter: string): AppMenu[] => {
  if (!filter) return items
  
  const lowerFilter = filter.toLowerCase()
  return items.filter(item => 
    item.title.toLowerCase().includes(lowerFilter) ||
    item.vertical?.toLowerCase().includes(lowerFilter) ||
    item.badge?.toLowerCase().includes(lowerFilter)
  )
}

// 導航項目排序器
export const sortNavigationItems = (items: AppMenu[], sortBy: 'title' | 'badge' = 'title'): AppMenu[] => {
  return [...items].sort((a, b) => {
    if (sortBy === 'badge') {
      const aBadge = a.badge || 'zzz'
      const bBadge = b.badge || 'zzz'
      return aBadge.localeCompare(bBadge)
    }
    return a.title.localeCompare(b.title)
  })
}

// 導航項目分組器
export const groupNavigationItems = (items: AppMenu[]): Record<string, AppMenu[]> => {
  return items.reduce((groups, item) => {
    const group = item.badge || 'Other'
    if (!groups[group]) {
      groups[group] = []
    }
    groups[group].push(item)
    return groups
  }, {} as Record<string, AppMenu[]>)
}
