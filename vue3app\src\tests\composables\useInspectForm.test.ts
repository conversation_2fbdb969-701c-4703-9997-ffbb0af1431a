import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import { useInspectForm } from '@/composables/useInspectForm'
import { useInspectsStore } from '@/stores/inspects'
import { useProductsStore } from '@/stores/products'
import { useAppStore } from '@/stores/app'

// Mock stores
vi.mock('@/stores/inspects')
vi.mock('@/stores/products')
vi.mock('@/stores/app')

// Mock Vue Router
vi.mock('vue-router', () => ({
  useRoute: vi.fn(),
  useRouter: vi.fn(),
  createRouter: vi.fn(),
  createWebHistory: vi.fn()
}))

describe('useInspectForm', () => {
  let pinia: any
  let mockInspectsStore: any
  let mockProductsStore: any
  let mockAppStore: any
  let mockRoute: any
  let mockRouter: any

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)

    // Mock route and router
    mockRoute = {
      params: {},
      query: {},
      path: '/',
      name: 'home'
    }

    mockRouter = {
      push: vi.fn(),
      replace: vi.fn(),
      go: vi.fn(),
      back: vi.fn(),
      forward: vi.fn()
    }

    vi.mocked(useRoute).mockReturnValue(mockRoute)
    vi.mocked(useRouter).mockReturnValue(mockRouter)

    // 創建 mock stores
    mockInspectsStore = {
      currentInspect: {},
      employees: [],
      inspectId: null,
      updateInspect: vi.fn(),
      saveInspect: vi.fn(),
      getInspectById: vi.fn(),
      getEmployees: vi.fn(),
      setInspect: vi.fn()
    }

    mockProductsStore = {
      getCategories: vi.fn(),
      getRemarks: vi.fn()
    }

    mockAppStore = {
      loading: false
    }

    // Mock store 返回值
    vi.mocked(useInspectsStore).mockReturnValue(mockInspectsStore)
    vi.mocked(useProductsStore).mockReturnValue(mockProductsStore)
    vi.mocked(useAppStore).mockReturnValue(mockAppStore)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('初始化', () => {
    it('應該正確初始化表單數據', async () => {
      // 設置路由為新增模式
      mockRoute.params = {}
      mockRoute.path = '/inspects/new'

      const { formData, isEditMode, formTitle } = useInspectForm()

      expect(isEditMode.value).toBe(false)
      expect(formTitle.value).toBe('新增')
      expect(formData.value).toEqual({
        id: null,
        classDate: expect.any(String),
        shiftName: '',
        employId: 0,
        groupName: '',
        typeName: 'YARN',
        quantity: 0
      })
    })

    it('應該在編輯模式下正確設置狀態', async () => {
      // 設置路由為編輯模式
      mockRoute.params = { id: '123' }
      mockRoute.path = '/inspect/123'

      const { isEditMode, formTitle } = useInspectForm()

      expect(isEditMode.value).toBe(true)
      expect(formTitle.value).toBe('編輯')
    })
  })

  describe('表單驗證', () => {
    it('應該包含正確的驗證規則', () => {
      const { validationRules } = useInspectForm()

      expect(validationRules.required).toBeDefined()
      expect(validationRules.number).toBeDefined()
      expect(validationRules.required[0]('')).toBe('此欄位為必填')
      expect(validationRules.required[0]('test')).toBe(true)
      expect(validationRules.number[0]('abc')).toBe('請輸入有效數字')
      expect(validationRules.number[0]('123')).toBe(true)
    })
  })

  describe('數據載入', () => {
    it('應該在編輯模式下載入檢驗數據', async () => {
      await mockRouter.push('/inspect/123')

      const mockInspectData = {
        id: 123,
        classDate: '2023-12-01',
        shiftName: '1',
        employId: 1,
        groupName: 'A',
        typeName: 'YARN',
        quantity: 10
      }

      mockInspectsStore.currentInspect = mockInspectData
      mockInspectsStore.getInspectById.mockResolvedValue()

      const { loadInspectData, formData } = useInspectForm()

      await loadInspectData()

      expect(mockInspectsStore.getInspectById).toHaveBeenCalledWith('123')
      expect(formData.value).toEqual(expect.objectContaining(mockInspectData))
    })

    it('應該載入員工數據', async () => {
      const mockEmployees = [
        { employId: 1, employName: '員工1' },
        { employId: 2, employName: '員工2' }
      ]

      mockInspectsStore.employees = mockEmployees
      mockInspectsStore.getEmployees.mockResolvedValue()

      const { loadEmployees, employeeOptions } = useInspectForm()

      await loadEmployees()

      expect(mockInspectsStore.getEmployees).toHaveBeenCalled()
      expect(employeeOptions.value).toEqual(mockEmployees)
    })
  })

  describe('表單操作', () => {
    it('應該在新增模式下正確保存數據', async () => {
      await mockRouter.push('/inspects/new')

      const mockSaveData = {
        id: null,
        classDate: '2023-12-01',
        shiftName: '1',
        employId: 1,
        groupName: 'A',
        typeName: 'YARN',
        quantity: 10
      }

      mockInspectsStore.saveInspect.mockResolvedValue()
      mockInspectsStore.inspectId = 456

      const { formData, saveForm } = useInspectForm({
        onSaveSuccess: vi.fn()
      })

      formData.value = mockSaveData

      await saveForm()

      expect(mockInspectsStore.saveInspect).toHaveBeenCalledWith(mockSaveData)
    })

    it('應該在編輯模式下正確更新數據', async () => {
      await mockRouter.push('/inspect/123')

      const mockUpdateData = {
        id: 123,
        classDate: '2023-12-01',
        shiftName: '2',
        employId: 2,
        groupName: 'B',
        typeName: 'CAKE',
        quantity: 20
      }

      mockInspectsStore.updateInspect.mockResolvedValue()

      const { formData, saveForm } = useInspectForm({
        onSaveSuccess: vi.fn()
      })

      formData.value = mockUpdateData

      await saveForm()

      expect(mockInspectsStore.updateInspect).toHaveBeenCalledWith(mockUpdateData)
    })

    it('應該正確重設表單', () => {
      const { formData, resetForm } = useInspectForm()

      // 修改表單數據
      formData.value.shiftName = '2'
      formData.value.employId = 123

      resetForm()

      expect(formData.value).toEqual({
        id: null,
        classDate: expect.any(String),
        shiftName: '',
        employId: 0,
        groupName: '',
        typeName: 'YARN',
        quantity: 0
      })
    })
  })

  describe('錯誤處理', () => {
    it('應該處理保存錯誤', async () => {
      await mockRouter.push('/inspects/new')

      const mockError = new Error('保存失敗')
      mockInspectsStore.saveInspect.mockRejectedValue(mockError)

      const onSaveError = vi.fn()
      const { saveForm } = useInspectForm({ onSaveError })

      await saveForm()

      expect(onSaveError).toHaveBeenCalledWith(mockError)
    })

    it('應該處理載入錯誤', async () => {
      await mockRouter.push('/inspect/123')

      const mockError = new Error('載入失敗')
      mockInspectsStore.getInspectById.mockRejectedValue(mockError)

      const onLoadError = vi.fn()
      const { loadInspectData } = useInspectForm({ onLoadError })

      await loadInspectData()

      expect(onLoadError).toHaveBeenCalledWith(mockError)
    })
  })

  describe('日期格式化', () => {
    it('應該正確格式化日期', () => {
      const { formatDateForInput } = useInspectForm()

      expect(formatDateForInput('2023-12-01T10:30:00Z')).toBe('2023-12-01')
      expect(formatDateForInput('2023-12-01')).toBe('2023-12-01')
      expect(formatDateForInput(null)).toBe(expect.stringMatching(/^\d{4}-\d{2}-\d{2}$/))
      expect(formatDateForInput('invalid')).toBe(expect.stringMatching(/^\d{4}-\d{2}-\d{2}$/))
    })
  })

  describe('初始化流程', () => {
    it('應該正確執行初始化流程', async () => {
      await mockRouter.push('/inspects/new')

      mockInspectsStore.getEmployees.mockResolvedValue()
      mockProductsStore.getCategories.mockResolvedValue()
      mockProductsStore.getRemarks.mockResolvedValue()

      const { initialize } = useInspectForm()

      await initialize()

      expect(mockInspectsStore.getEmployees).toHaveBeenCalled()
      expect(mockProductsStore.getCategories).toHaveBeenCalled()
      expect(mockProductsStore.getRemarks).toHaveBeenCalled()
      expect(mockInspectsStore.setInspect).toHaveBeenCalledWith({
        typeName: 'YARN',
        classDate: expect.any(String)
      })
    })

    it('應該在編輯模式下載入現有數據', async () => {
      await mockRouter.push('/inspect/123')

      mockInspectsStore.getEmployees.mockResolvedValue()
      mockProductsStore.getCategories.mockResolvedValue()
      mockProductsStore.getRemarks.mockResolvedValue()
      mockInspectsStore.getInspectById.mockResolvedValue()

      const { initialize } = useInspectForm()

      await initialize()

      expect(mockInspectsStore.getInspectById).toHaveBeenCalledWith('123')
    })
  })

  describe('通知訊息', () => {
    it('應該正確顯示訊息', () => {
      const { showMessage, snackbar } = useInspectForm()

      showMessage('測試訊息', 'success')

      expect(snackbar.value).toEqual({
        show: true,
        message: '測試訊息',
        color: 'success'
      })
    })

    it('應該使用默認顏色', () => {
      const { showMessage, snackbar } = useInspectForm()

      showMessage('測試訊息')

      expect(snackbar.value.color).toBe('success')
    })
  })
})
