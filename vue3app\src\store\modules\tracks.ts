import { Component, Vue } from 'vue-facing-decorator'
import { createApp } from 'vue'
import { getData, putData, postData, deleteData } from '@/utils/backend-api'
import { Entity, Track, Pagination, TableHeader } from '@/types'
import { getDefaultPagination, getPagination } from '@/utils/store-util'
import { appModule } from '@/stores/app'
import { get } from 'lodash'
import moment from 'moment'

export interface TrackState {
  items: Entity[]
  pagination: Pagination
  loading: boolean
  track: Track
  customer: string
  abnormalities: any[]
  dispositions: any[]
}

@Component({
  name: 'TrackModule'
})
class TrackModule extends Vue implements TrackState {
  items: Entity[] = []
  pagination = getDefaultPagination()
  loading = false
  customer = ""
  track = {} as Track
  abnormalities: any[] = []
  dispositions: any[] = []

  getTrackById = (id: string) => {
    this.setLoading(true)
    if (id) {
      getData("tracks/" + id).then(
        res => {
          const tracks = res.data
          if (tracks && tracks.length > 0) {
            tracks[0].trackDate = tracks[0].trackDate
              ? moment(tracks[0].trackDate).format("YYYY/M/D")
              : ""
            this.setDataTable([tracks[0]])
            this.setTrack(tracks[0])
          }
          this.setLoading(false)
        },
        err => {
          console.log(err)
          this.setLoading(false)
        }
      )
    } else {
      this.setTrack({} as Track)
      this.setLoading(false)
    }
  }

  getAllTracks = () => {
    this.setLoading(true)
    getData("tracks").then(res => {
      const tracks: Track[] = res.data
      this.setDataTable(tracks)
      this.setLoading(false)
    })
  }

  clearTracks = () => {
    this.setLoading(true)
    getData("tracks").then(() => {
      const tracks: Track[] = []
      const track = {} as Track
      this.setDataTable(tracks)
      this.setTrack(track)
      this.setLoading(false)
    })
  }

  searchTracks = (searchQuery: string) => {
    getData("tracks" + searchQuery).then(res => {
      const tracks: Track[] = res.data
      this.setDataTable(tracks)
      this.setLoading(false)
    })
  }

  getTrackByCode = (searchQuery: string) => {
    getData("tracks/" + searchQuery).then(res => {
      const tracks: Track[] = res.data
      if (tracks && tracks.length > 0) {
        this.setDataTable([tracks[0]])
        this.setTrack(tracks[0])
      }
      this.setLoading(false)
    })
  }

  getTrackInfoByQRCode = (qrCode: string) => {
    return getData("tracks/info/" + qrCode)
  }

  getAbnormalities = () => {
    getData("abnormalities").then(res => {
      if (res.data) {
        this.abnormalities = res.data
      }
    })
  }

  getDispositions = () => {
    getData("dispositions").then(res => {
      if (res.data) {
        this.dispositions = res.data
      }
    })
  }

  quickSearch = (headers: TableHeader[], qsFilter: string): void => {
    getData("tracks").then(res => {
      const tracks = res.data.filter((r: any) =>
        headers.some((header: TableHeader) => {
          const val = get(r, [header.value])
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter)) ||
            false
          )
        })
      )
      this.setDataTable(tracks)
      this.setLoading(false)
    })
  }

  deleteTrack = (id: number) => {
    deleteData(`tracks/${id.toString()}`)
      .then(() => {
        this.getAllTracks()
        appModule.sendSuccessNotice("Operation is done.")
      })
      .catch(err => {
        console.log(err)
        appModule.sendErrorNotice("Operation failed! Please try again later.")
        appModule.closeNoticeWithDelay(5000)
      })
  }

  saveTrack = (track: Track) => {
    if (!track.id) {
      postData("tracks/", track)
        .then(res => {
          const track = res.data
          this.setTrack(track)
          appModule.sendSuccessNotice("New record has been added.")
          appModule.closeNoticeWithDelay(3000)
        })
        .catch(err => {
          console.log(err)
          appModule.sendErrorNotice("Operation failed! Please try again later.")
          appModule.closeNoticeWithDelay(5000)
        })
    } else {
      putData("tracks/" + track.id.toString(), track)
        .then(res => {
          const track = res.data
          this.setTrack(track)
          appModule.sendSuccessNotice("The record has been updated.")
          appModule.closeNoticeWithDelay(3000)
        })
        .catch(err => {
          console.log(err)
          appModule.sendErrorNotice("Operation failed! Please try again later.")
          appModule.closeNoticeWithDelay(5000)
        })
    }
  }

  setDataTable = (items: Track[]) => {
    const pagination = getPagination(items)
    this.setPagination(pagination)
    this.setItems(items)
  }

  setItems = (tracks: Track[]) => {
    this.items = tracks
  }

  setPagination = (pagination: Pagination) => {
    this.pagination = pagination
  }

  setLoading = (loading: boolean) => {
    this.loading = loading
  }

  setTrack = (track: Track) => {
    this.track = track
  }
}

// Create and export a singleton instance
const app = createApp(TrackModule)
const vm = app.mount(document.createElement('div'))
export const trackModule = vm as InstanceType<typeof TrackModule>
