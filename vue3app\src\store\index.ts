import { createPinia } from 'pinia'
import { appModule } from '@/stores/app'
import { userModule } from '@/stores/user'
import { productModule } from './modules/products'
import { trackModule } from './modules/tracks'
import { tracksheetModule } from './modules/tracksheets'
import { pinventoryModule } from './modules/pinventories'
import { icountModule } from './modules/icounts'
import { inspectModule } from './modules/inspects'
import { relabelModule } from './modules/relabels'
import { disposalModule } from './modules/disposals'
import { downgradeModule } from './modules/downgrades'
import { workModule } from './modules/works'

const pinia = createPinia()

export {
  pinia,
  appModule,
  userModule,
  productModule,
  trackModule,
  tracksheetModule,
  pinventoryModule,
  icountModule,
  inspectModule,
  relabelModule,
  disposalModule,
  downgradeModule,
  workModule
}
