<template>
  <canvas ref="chart"></canvas>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-facing-decorator'
import { Bar } from 'vue-chartjs'
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js'

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend)

@Component({
  name: 'ProductAnalysisChart'
})
export default class ProductAnalysisChart extends Vue {
  mounted() {
    const chart = new ChartJS(this.$refs.chart as HTMLCanvasElement, {
      type: 'bar',
      data: {
        labels: [
          "Jan",
          "Feb",
          "Mar",
          "Apr",
          "May",
          "Jun"
        ],
        datasets: [
          {
            label: "Product A",
            backgroundColor: "#f87979",
            data: [40, 20, 12, 39, 10, 40]
          },
          {
            label: "Product B",
            backgroundColor: "#36A2EB",
            data: [30, 25, 18, 32, 15, 35]
          }
        ]
      },
      options: {
        indexAxis: 'y', // This makes the bar chart horizontal
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top'
          }
        }
      }
    })
  }
}
</script>
