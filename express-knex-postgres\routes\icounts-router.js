const router = require("express").Router();
const moment = require('moment');

const icountsDB = require("../models/icounts-model.js");
const employeesDB = require("../models/employees-model.js");
const icountlinesDB = require("../models/icountlines-model.js");

// GET ALL ICOUNTS
router.get("/", async (req, res) => {
  try {
    const icounts = await icountsDB.find();
    const employees = await employeesDB.find();
    // Map employees to icount
    icounts.forEach(mem => {
      const employee = employees.find(info => info.employId === mem.employId);
      if (employee) {
        mem.employName = `${employee.employNO} ${employee.userName}`;
      }
    });
    // Format dates
    icounts.forEach(mem => {
      mem.classDate = moment(mem.classDate).format("YYYY-M-D");
    });
    res.status(200).json(icounts);
  } catch (err) {
    res.status(500).json({ err: err });
  }
});

// GET ICOUNT BY ID
router.get("/:id", async (req, res) => {
  const icountId = req.params.id;
  try {
    const icount = await icountsDB.findById(icountId);
    if (!icount) {
      res.status(404).json({ err: "The icount with the specified id does not exist" });
    } else {
    const icountlines = await icountlinesDB.findById(icount[0].id);
    const employee = await employeesDB.findById(icount[0].employId);
    // Map employees to icount
    icount.forEach(mem => {           
        mem.employName = `${employee[0].employNO} ${employee[0].userName}`;      
    });
    // Format dates
    icount.forEach(mem => {
      mem.classDate = moment(mem.classDate).format("YYYY-M-D");
    });
    icountlines.forEach(mem => {
      mem.tracksheetTime = moment(mem.tracksheetTime).format("YYYY-M-D HH:mm");
    });
    // Assign icountlines to respective icount
    icount.forEach(mem => {
      mem.icountlines = icountlines.filter(d => d.icountId === mem.id);
    });
    res.status(200).json(icount);
    }
  } catch (err) {
    res.status(500).json({ err: err.message });
    }
});

// INSERT ICOUNT INTO DB
router.post("/", async (req, res) => {
  const newICount = req.body;
  if (!newICount.shiftName) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const icount = await icountsDB.addICount(newICount);
      res.status(201).json(icount);
    } catch (err) {
      res.status(500).json({err: err.message });
    }
  }
});

// UPDATE ICOUNT INTO DB
router.put("/:id", async (req, res) => {
  const icountId = req.params.id;
  const newChanges = req.body;
  if (!newChanges.shiftName) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const addChanges = await icountsDB.updateICount(icountId, newChanges);
      res.status(200).json(addChanges);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

// REMOVE ICOUNT INTO DB
router.delete("/:id", async (req, res) => {
  const icountId = req.params.id;
  try {
    const deleting = await icountsDB.removeICount(icountId);
    res.status(204).json(deleting);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;
