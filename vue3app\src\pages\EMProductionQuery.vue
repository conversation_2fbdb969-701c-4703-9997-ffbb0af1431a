<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <span class="text-h4">
              <v-icon class="mr-2">mdi-factory</v-icon>
              EM即時生產查詢
            </span>
            <v-spacer />
            <v-chip
              v-if="totalItems > 0"
              color="primary"
              variant="tonal"
              class="mr-2"
            >
              {{ totalItems }} 筆資料
            </v-chip>
            <v-btn
              color="brown-lighten-1"
              size="small"
              icon
              @click="clearData"
              :loading="isLoading"
            >
              <v-icon>mdi-refresh</v-icon>
            </v-btn>
          </v-card-title>

          <v-card-text>
            <!-- 搜尋區域 -->
            <v-alert type="info" class="mb-4">
              <div class="d-flex align-center">
                <div>
                  <strong>🔍 EM即時生產查詢</strong><br>
                  請輸入傳票QRCode或Cap QRCode進行查詢
                </div>
              </div>
            </v-alert>

            <v-row class="mb-4">
              <v-col cols="12" md="8">
                <v-text-field
                  ref="searchInput"
                  v-model="searchQuery"
                  label="傳票QRCode 或 Cap QRCode"
                  prepend-inner-icon="mdi-magnify"
                  append-icon="mdi-qrcode"
                  @keyup.enter="performSearch"
                  @input="handleInput"
                  counter="27"
                  variant="outlined"
                  clearable
                  hide-details="auto"
                  :rules="[rules.required]"
                />
              </v-col>
              <v-col cols="12" md="4">
                <v-btn
                  color="primary"
                  @click="performSearch"
                  :loading="isLoading"
                  :disabled="!searchQuery"
                  block
                  size="large"
                  prepend-icon="mdi-magnify"
                >
                  查詢
                </v-btn>
              </v-col>
            </v-row>

            <!-- Loading indicator -->
            <v-row v-if="isLoading" class="mb-4">
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-text class="text-center">
                    <v-progress-circular indeterminate color="primary" size="64" class="mb-4" />
                    <div class="text-h6">查詢中...</div>
                    <div class="text-caption">正在搜尋相關資料</div>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>

            <!-- 查詢結果 -->
            <v-row v-if="searchAttempted && !isLoading" class="mb-4">
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-table</v-icon>
                    查詢結果
                    <v-spacer />
                    <v-chip color="info" size="small">
                      {{ items.length }} 筆資料
                    </v-chip>
                  </v-card-title>

                  <v-card-text>
                    <v-data-table
                      :headers="selectedHeaders"
                      :items="items"
                      :items-per-page="10"
                      class="elevation-1"
                      loading-text="載入資料中..."
                      no-data-text="查無資料"
                      :loading="isLoading"
                    >
                      <!-- 時間格式化 -->
                      <template v-slot:item.tracksheetTime="{ item }">
                        <span>{{ formatDateTime((item as any).tracksheetTime) }}</span>
                      </template>

                      <template v-slot:item.productDate="{ item }">
                        <span>{{ formatDateTime((item as any).productDate || (item as any).ProductDate) }}</span>
                      </template>

                      <template v-slot:item.workDate="{ item }">
                        <span>{{ formatDateTime((item as any).workDate) }}</span>
                      </template>

                      <!-- 數量顯示 -->
                      <template v-slot:item.tracksheetQty="{ item }">
                        <v-chip color="primary" size="small" v-if="(item as any).tracksheetQty">
                          {{ (item as any).tracksheetQty }}
                        </v-chip>
                      </template>

                      <template v-slot:item.cakeWeight="{ item }">
                        <v-chip color="success" size="small" v-if="(item as any).cakeWeight">
                          {{ (item as any).cakeWeight }} g
                        </v-chip>
                      </template>

                      <template v-slot:item.tracksheetNetWeight="{ item }">
                        <v-chip color="success" size="small" v-if="(item as any).tracksheetNetWeight">
                          {{ (item as any).tracksheetNetWeight }} kg
                        </v-chip>
                      </template>

                      <!-- 乾燥時間 -->
                      <template v-slot:item.dryTime="{ item }">
                        <v-chip color="warning" size="small" v-if="(item as any).dryTime">
                          {{ (item as any).dryTime }} hrs
                        </v-chip>
                      </template>

                      <!-- MFD狀態 -->
                      <template v-slot:item.isMFD="{ item }">
                        <v-chip
                          :color="(item as any).isMFD === 'Y' ? 'success' : 'error'"
                          size="small"
                          v-if="(item as any).isMFD !== undefined"
                        >
                          {{ (item as any).isMFD === 'Y' ? '是' : '否' }}
                        </v-chip>
                      </template>

                      <template v-slot:no-data>
                        <v-alert type="warning" class="ma-4">
                          <div class="text-center">
                            <v-icon size="large" class="mb-2">mdi-database-search</v-icon>
                            <div>查無相關資料</div>
                            <div class="text-caption">請檢查輸入的QRCode是否正確</div>
                          </div>
                        </v-alert>
                      </template>
                    </v-data-table>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>

            <!-- 查詢類型識別 -->
            <v-row class="mb-4" v-if="searchQuery">
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-information</v-icon>
                    查詢類型識別
                  </v-card-title>
                  <v-card-text>
                    <v-row>
                      <v-col cols="12" md="4">
                        <v-list-item>
                          <template v-slot:prepend>
                            <v-icon :color="isYarnQRcode ? 'success' : 'grey'">
                              {{ isYarnQRcode ? 'mdi-check-circle' : 'mdi-circle-outline' }}
                            </v-icon>
                          </template>
                          <v-list-item-title>Yarn QRCode (14位數)</v-list-item-title>
                          <v-list-item-subtitle>
                            {{ isYarnQRcode ? '已識別為Yarn QRCode查詢' : '非Yarn QRCode' }}
                          </v-list-item-subtitle>
                        </v-list-item>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-list-item>
                          <template v-slot:prepend>
                            <v-icon :color="isCakeTrackSheetNO ? 'success' : 'grey'">
                              {{ isCakeTrackSheetNO ? 'mdi-check-circle' : 'mdi-circle-outline' }}
                            </v-icon>
                          </template>
                          <v-list-item-title>Cake傳票 (F/D開頭)</v-list-item-title>
                          <v-list-item-subtitle>
                            {{ isCakeTrackSheetNO ? '已識別為Cake傳票查詢' : '非Cake傳票' }}
                          </v-list-item-subtitle>
                        </v-list-item>
                      </v-col>
                      <v-col cols="12" md="4">
                        <v-list-item>
                          <template v-slot:prepend>
                            <v-icon :color="isYarnTrackSheetNO ? 'success' : 'grey'">
                              {{ isYarnTrackSheetNO ? 'mdi-check-circle' : 'mdi-circle-outline' }}
                            </v-icon>
                          </template>
                          <v-list-item-title>Yarn傳票 (G開頭)</v-list-item-title>
                          <v-list-item-subtitle>
                            {{ isYarnTrackSheetNO ? '已識別為Yarn傳票查詢' : '非Yarn傳票' }}
                          </v-list-item-subtitle>
                        </v-list-item>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>

          <v-card-actions>
            <v-btn
              color="primary"
              @click="performSearch"
              :disabled="!searchQuery"
              :loading="isLoading"
              prepend-icon="mdi-magnify"
            >
              查詢
            </v-btn>

            <v-btn
              color="secondary"
              @click="clearData"
              prepend-icon="mdi-refresh"
            >
              清除
            </v-btn>

            <v-spacer />

            <v-chip color="info" variant="tonal">
              查詢狀態: {{ isLoading ? '查詢中' : '就緒' }}
            </v-chip>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <!-- 成功訊息 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          關閉
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useProductsStore } from '@/stores/products'
import { useTracksheetsStore } from '@/stores/tracksheets'

const productsStore = useProductsStore()
const tracksheetsStore = useTracksheetsStore()

// 響應式數據
const searchInput = ref()
const searchQuery = ref('')
const searchAttempted = ref(false)

// QRCode類型識別
const isYarnQRcode = ref(false)
const isCakeTrackSheetNO = ref(false)
const isYarnTrackSheetNO = ref(false)

const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

// 表單驗證規則
const rules = {
  required: (value: string) => !!value || '請輸入傳票QRCode或Cap QRCode'
}

// 計算屬性
const isLoading = computed(() =>
  productsStore.isLoading || tracksheetsStore.isLoading
)

const items = computed(() => {
  if (isYarnQRcode.value) {
    return productsStore.products
  } else {
    return tracksheetsStore.items
  }
})

const totalItems = computed(() => items.value.length)

// 表格標題定義
const defaultHeaders = [
  { title: '追踨傳票單號', key: 'tracksheetNO', align: 'start' as const },
  { title: '日期時間', key: 'tracksheetTime', align: 'start' as const },
  { title: '爐別', key: 'furnaceName', align: 'start' as const },
  { title: '品種', key: 'productName', align: 'start' as const },
  { title: '個數', key: 'tracksheetQty', align: 'center' as const },
  { title: '淨重(kg)', key: 'tracksheetNetWeight', align: 'center' as const }
]

const yarnQRcodeHeaders = [
  { title: 'BI檢測', key: 'biName', align: 'start' as const },
  { title: 'TEX檢測', key: 'texName', align: 'start' as const },
  { title: 'Lot NO', key: 'batchName', align: 'start' as const },
  { title: '乾燥時間(hrs)', key: 'dryTime', align: 'center' as const },
  { title: '過磅日期', key: 'productDate', align: 'start' as const },
  { title: '品種', key: 'productName', align: 'start' as const },
  { title: '等級', key: 'gradeName', align: 'start' as const },
  { title: '爐別', key: 'furnaceName', align: 'start' as const },
  { title: 'Bushing NO', key: 'bushingNO', align: 'start' as const },
  { title: 'Cake位置', key: 'positionName', align: 'start' as const },
  { title: 'Cake重量(g)', key: 'cakeWeight', align: 'center' as const },
  { title: '開機日期', key: 'workDate', align: 'start' as const },
  { title: 'Twister NO', key: 'twisterNO', align: 'start' as const },
  { title: 'Spindle NO', key: 'spindleNO', align: 'start' as const },
  { title: 'Is MFD', key: 'isMFD', align: 'center' as const }
]

const cakeTrackSheetHeaders = [
  { title: '追踪傳票單號', key: 'tracksheetNO', align: 'start' as const },
  { title: '日期時間', key: 'tracksheetTime', align: 'start' as const },
  { title: '爐別', key: 'furnaceName', align: 'start' as const },
  { title: '品種', key: 'productName', align: 'start' as const },
  { title: '等級', key: 'gradeName', align: 'start' as const },
  { title: '個數', key: 'tracksheetQty', align: 'center' as const },
  { title: '淨重(kg)', key: 'tracksheetNetWeight', align: 'center' as const }
]

const yarnTrackSheetHeaders = [
  { title: '追踪傳票單號', key: 'documentNO', align: 'start' as const },
  { title: '日期時間', key: 'tracksheetTime', align: 'start' as const },
  { title: '品種', key: 'productName', align: 'start' as const },
  { title: 'Twister NO', key: 'twisterNO', align: 'start' as const },
  { title: '開機T1個數', key: 'trackT1Qty', align: 'center' as const },
  { title: '開機T2個數', key: 'trackT2Qty', align: 'center' as const },
  { title: '品檢T1小計', key: 'icountT1Sum', align: 'center' as const },
  { title: '品檢T2小計', key: 'icountT2Sum', align: 'center' as const },
  { title: '追踪傳票序號', key: 'tracksheetNO', align: 'start' as const },
  { title: '爐別', key: 'furnaceName', align: 'start' as const },
  { title: '品檢T1個數', key: 'icountT1Qty', align: 'center' as const },
  { title: '品檢T2個數', key: 'icountT2Qty', align: 'center' as const }
]

const selectedHeaders = computed(() => {
  if (isCakeTrackSheetNO.value) {
    return cakeTrackSheetHeaders
  } else if (isYarnTrackSheetNO.value) {
    return yarnTrackSheetHeaders
  } else if (isYarnQRcode.value) {
    return yarnQRcodeHeaders
  }
  return defaultHeaders
})

// 方法
const identifyQRCodeType = (query: string) => {
  // 重置所有類型
  isYarnQRcode.value = false
  isCakeTrackSheetNO.value = false
  isYarnTrackSheetNO.value = false

  if (!query) return

  const trimmedQuery = query.trim()

  // Yarn QRCode: 14位數字
  isYarnQRcode.value = /^\d{14}[a-zA-Z0-9\s.]*$/.test(trimmedQuery)

  // Cake傳票: F或D開頭
  isCakeTrackSheetNO.value = !!(
    /^F\d{11}\s*,[A-Za-z0-9]{10}$/.test(trimmedQuery) ||
    /^D\d{11}\s*$/.test(trimmedQuery)
  )

  // Yarn傳票: G開頭
  isYarnTrackSheetNO.value = /^G\d{12}-\d{1}$/.test(trimmedQuery) || /^G\d{12}-\d{1}-S\d{2}$/.test(trimmedQuery)
}

const handleInput = () => {
  identifyQRCodeType(searchQuery.value)
}

const performSearch = async () => {
  const query = searchQuery.value?.trim()

  if (!query) {
    showMessage('請輸入傳票單號或Cap QRCode', 'error')
    return
  }

  searchAttempted.value = true
  identifyQRCodeType(query)

  if (!isYarnQRcode.value && !isCakeTrackSheetNO.value && !isYarnTrackSheetNO.value) {
    showMessage('無效的傳票單號或Cap QRCode!', 'error')
    productsStore.setProducts([])
    tracksheetsStore.clearTracksheets()
    return
  }

  try {
    if (isYarnQRcode.value) {
      // Yarn QRCode查詢
      const queryCode = query.slice(0, 14)
      await productsStore.getProductByQRCode(queryCode)
      showMessage('Yarn QRCode查詢完成', 'success')
    } else if (isCakeTrackSheetNO.value) {
      // Cake傳票查詢
      const type = "CAKE"
      const trackId = query.slice(0, 12)
      const queryCode = query.slice(1, 12)
      const tracks = [type, queryCode, trackId]
      await tracksheetsStore.getTracksheetByCode(tracks)
      showMessage('Cake傳票查詢完成', 'success')
    } else if (isYarnTrackSheetNO.value) {
      // Yarn傳票查詢
      const type = "QI"
      const trackId = query.slice(0, 15)
      const queryCode = query.slice(1, 13)
      const tracks = [type, queryCode, trackId]
      await tracksheetsStore.getTracksheetByCode(tracks)
      showMessage('Yarn傳票查詢完成', 'success')
    }
  } catch (error) {
    console.error('查詢失敗:', error)
    showMessage('查詢失敗，請重試', 'error')
  }
}

const clearData = () => {
  searchQuery.value = ''
  searchAttempted.value = false
  isYarnQRcode.value = false
  isCakeTrackSheetNO.value = false
  isYarnTrackSheetNO.value = false

  productsStore.setProducts([])
  tracksheetsStore.clearTracksheets()

  showMessage('數據已清除', 'info')

  // 重新聚焦到搜尋框
  nextTick(() => {
    if (searchInput.value) {
      searchInput.value.focus()
    }
  })
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-TW')
}

const showMessage = (message: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}

// 生命週期
onMounted(() => {
  // 清除之前的數據
  productsStore.setProducts([])
  tracksheetsStore.clearTracksheets()

  showMessage('EM即時生產查詢已準備就緒', 'info')

  // 聚焦到搜尋框
  nextTick(() => {
    if (searchInput.value) {
      searchInput.value.focus()
    }
  })
})
</script>

<style scoped>
.v-card {
  margin-bottom: 16px;
}

.v-data-table {
  border-radius: 8px;
}

.text-h6 {
  font-weight: 600;
}

.text-caption {
  opacity: 0.7;
}
</style>
