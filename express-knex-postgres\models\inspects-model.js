const db = require("../config/dbConfig.js");
require("dotenv").config();
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET ALL INSPECTS
const findByType = type => {
  return db("aits_inspects")
    .select({
      id: "aits_inspects.id",
      classDate: "aits_inspects.classdate",
      shiftName: "aits_inspects.shiftname",
      employId: "aits_inspects.employid",
      groupName:"aits_inspects.groupname",
      typeName: "aits_inspects.typename",
      created: "aits_inspects.created",
      updated: "aits_inspects.updated",
      quantity: db.raw("count(aits_inspectlines.inspectid)")      
    })
    .leftJoin("aits_inspectlines", "aits_inspects.id", "aits_inspectlines.inspectid")
    .where("aits_inspects.typename", type)
    .where("aits_inspects.classdate", ">=", process.env.DUE_TO_QUERY_DATE)
    .groupBy("aits_inspects.id", "aits_inspects.classdate", "aits_inspects.shiftname", "aits_inspects.employid", "aits_inspects.groupname")
    .orderBy("aits_inspects.id", "desc")
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

// GET SPECIFIC INSPECT BY ID
const findById = id => {
  return db("aits_inspects")
    .select({
      id: "aits_inspects.id",
      classDate: "aits_inspects.classdate",
      shiftName: "aits_inspects.shiftname",
      employId: "aits_inspects.employid",
      groupName: "aits_inspects.groupname",
      typeName: "aits_inspects.typename",
      created: "aits_inspects.created",
      updated: "aits_inspects.updated"
    })
    .where("aits_inspects.id", id)
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

// ADD A INSPECT
const addInspect = inspect => {
  return db.transaction(trx => {
    return db("aits_inspects")
      .insert({
        classdate: inspect.classDate,
        shiftname: inspect.shiftName,
        employid: inspect.employId,
        groupname: inspect.groupName,
        typename: inspect.typeName,
        created: db.fn.now()
      }, "id")
      .then(trx.commit)
      .catch(trx.rollback);
    })  
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

// UPDATE INSPECT
const updateInspect = (id, inspect) => {
  let origin,result;
  return db.transaction(trx => {
    return db("aits_inspects")
      .where("aits_inspects.id", id)
      .then(old_inspect => {
        origin = old_inspect;
        if (old_inspect) {
          return db("aits_inspects")
          .where("aits_inspects.id", id)
          .update({
            classdate: inspect.classDate,
            shiftname: inspect.shiftName,
            employid: inspect.employId,
            groupname: inspect.groupName,
            typename: inspect.typeName,
            updated: db.fn.now()
          })
          .then(trx.commit)
          .catch(trx.rollback);
        }
    })
  })
  .then(()=>{
    return db("aits_inspects")
    .where("aits_inspects.id", id)
    .then(new_inspect => { 
      result = new_inspect;
      infoLogger.info(`update inspect content: ${JSON.stringify({origin,result})}`)
    })
  }) 
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

// REMOVE INSPECT
const removeInspect = id => {
  let result;
  return db.transaction(trx => {
    return db("aits_inspects")
      .where("aits_inspects.id", id)
      .then(inspect => {   
        result = inspect;      
        if (inspect) { 
          return db("aits_inspects") 
            .where("aits_inspects.id", id)
            .del()
            .then(trx.commit)
            .catch(trx.rollback);
        }
      })
    })
  .then(() => { 
    infoLogger.info(`remove inspect content: ${JSON.stringify(result)}`)
  })
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

// GET COUNT BY TYPE FOR PAGINATION
const countByType = type => {
  return db("aits_inspects")
    .count("id as count")
    .where("aits_inspects.typename", type)
    .where("aits_inspects.classdate", ">=", process.env.DUE_TO_QUERY_DATE)
    .first()
    .then(result => parseInt(result.count))
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

// GET INSPECTS BY TYPE WITH PAGINATION
const findByTypeWithPagination = (type, limit, offset) => {
  return db("aits_inspects")
    .select({
      id: "aits_inspects.id",
      classDate: "aits_inspects.classdate",
      shiftName: "aits_inspects.shiftname",
      employId: "aits_inspects.employid",
      groupName:"aits_inspects.groupname",
      typeName: "aits_inspects.typename",
      created: "aits_inspects.created",
      updated: "aits_inspects.updated",
      quantity: db.raw("count(aits_inspectlines.inspectid)")      
    })
    .leftJoin("aits_inspectlines", "aits_inspects.id", "aits_inspectlines.inspectid")
    .where("aits_inspects.typename", type)
    .where("aits_inspects.classdate", ">=", process.env.DUE_TO_QUERY_DATE)
    .groupBy("aits_inspects.id", "aits_inspects.classdate", "aits_inspects.shiftname", "aits_inspects.employid", "aits_inspects.groupname", "aits_inspects.typename", "aits_inspects.created", "aits_inspects.updated")
    .orderBy("aits_inspects.id", "desc")
    .limit(limit)
    .offset(offset)
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

module.exports = {
  findByType,
  findById,
  addInspect,
  updateInspect,
  removeInspect,
  countByType,
  findByTypeWithPagination
};
