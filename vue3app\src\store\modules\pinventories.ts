import { Component, Vue } from 'vue-facing-decorator'
import { createApp } from 'vue'
import { getData, putData, postData, deleteData } from '@/utils/backend-api'
import { Employee, PInventory, Entity, Product, Tracksheet, PInventoryline, Pagination, TableHeader } from '@/types'
import { getDefaultPagination, getPagination } from '@/utils/store-util'
import { appModule } from '@/stores/app'
import { get } from 'lodash'

export interface PInventoryState {
  items: Entity[]
  pagination: Pagination
  loading: boolean
  pinventoryId: number | null
  pinventory: PInventory
  employee: string
  pinventoryline: PInventoryline[]
  tracksheet: Tracksheet
  employees: Employee[]
  product: Product
}

@Component({
  name: 'PInventoryModule'
})
class PInventoryModule extends Vue implements PInventoryState {
  items: Entity[] = []
  pagination = getDefaultPagination()
  loading = false
  employee = ""
  pinventoryId: number | null = null
  pinventory = {} as PInventory
  pinventoryline: PInventoryline[] = []
  employees: Employee[] = []
  product = {} as Product
  tracksheet = {} as Tracksheet

  getEmployees = () => {
    getData("employees/tw").then(res => {
      if (res.data) {
        const employees = res.data.map((c: Employee) => {
          c.employName = c.employNO + " " + c.userName
          c.value = c.id
          return c
        })
        this.setEmployees(employees)
      }
    })
  }

  getProductById = async (id: string) => {
    try {
      this.setLoading(true)
      if (id) {
        const res = await getData("products/" + id)
        const product = res.data
        this.setProduct(product)
      } else {
        this.setProduct({} as Product)
      }
    } catch (err) {
      console.log(err)
    } finally {
      this.setLoading(false)
    }
  }

  getAllPInventoriesByType = async (type: string) => {
    this.setLoading(true)
    try {
      const res = await getData(`pinventories/${type}`)
      const pinventories = res.data
      this.setPInventory(pinventories)
      this.setDataTable(pinventories)
    } catch (error) {
      console.error(error)
    } finally {
      this.setLoading(false)
    }
  }

  getPInventoryById = (id: string) => {
    if (id) {
      getData("pinventories/" + id).then(
        res => {
          const _pinventory = res.data
          const pinventory = _pinventory[0]
          pinventory.quantity = pinventory.pinventorylines?.length || 0
          this.setPInventory(pinventory)
          this.setDataTable(pinventory.pinventorylines)
        },
        err => {
          console.log(err)
        }
      )
    } else {
      const pinventory = {} as PInventory
      pinventory.pinventorylines = []
      this.setPInventory(pinventory)
      this.setLoading(false)
    }
  }

  getTracksheetById = async (id: string) => {
    try {
      this.setLoading(true)
      if (id) {
        const res = await getData("tracksheet/yarn/" + id)
        const tracksheet = res.data
        this.setTracksheet(tracksheet)
      } else {
        this.setTracksheet({} as Tracksheet)
      }
    } catch (err) {
      console.log(err)
    } finally {
      this.setLoading(false)
    }
  }

  getDuplicatePInventorylineByCode = async (tracks: string[]): Promise<boolean> => {
    const type = tracks[0].toString()
    const trackId = tracks[2].toString()
    try {
      this.setLoading(true)
      if (trackId) {
        const res = await getData("pinventorylines/duplicate/" + trackId)
        const data = res.data
        if (data !== undefined && data !== null) {
          for (const item of data) {
            if (item.typeName === type) {
              return true
            }
          }
        }
      }
      return false
    } catch (err) {
      console.log(err)
      return false
    } finally {
      this.setLoading(false)
    }
  }

  searchPInventories = (searchQuery: string) => {
    getData("pinventories" + searchQuery).then(res => {
      const pinventories = res.data
      pinventories.forEach((item: PInventory) => {
        item.quantity = item.pinventorylines?.length || 0
      })
      this.setDataTable(pinventories)
      this.setLoading(false)
    })
  }

  quickSearch = (headers: TableHeader[], qsFilter: string): void => {
    getData("pinventories").then(res => {
      const pinventories = res.data.filter((r: any) =>
        headers.some((header: TableHeader) => {
          const val = get(r, [header.value])
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter)) ||
            false
          )
        })
      )
      pinventories.forEach((item: PInventory) => {
        item.quantity = item.pinventorylines?.length || 0
      })
      this.setDataTable(pinventories)
      this.setLoading(false)
    })
  }

  savePInventory = (pinventory: PInventory): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!pinventory.id) {
        postData("pinventories/", pinventory)
          .then(res => {
            const pinventory = res.data
            const PInventoryId = { id: pinventory[0].id }
            const addPInventory = { ...PInventoryId, ...this.pinventory }
            this.setPInventory(addPInventory)
            if (addPInventory.id !== undefined) {
              this.setPInventoryId(addPInventory.id)
            }
            appModule.sendSuccessNotice("New record has been added.")
            appModule.closeNoticeWithDelay(3000)
            resolve()
          })
          .catch(err => {
            console.log(err)
            appModule.sendErrorNotice("Operation failed! Please try again later.")
            appModule.closeNoticeWithDelay(5000)
            reject(err)
          })
      } else {
        putData("pinventories/" + pinventory.id.toString(), pinventory)
          .then(() => {
            appModule.sendSuccessNotice("The record has been updated.")
            appModule.closeNoticeWithDelay(3000)
            resolve()
          })
          .catch(err => {
            console.log(err)
            appModule.sendErrorNotice("Operation failed! Please try again later.")
            appModule.closeNoticeWithDelay(5000)
            reject(err)
          })
      }
    })
  }

  addPInventorylineToPInventory = (pinventoryline: PInventoryline) => {
    if (pinventoryline) {
      const typeName = { typeName: this.pinventory.typeName }
      const classDate = { classDate: this.pinventory.classDate }
      const newPInventoryline = { ...pinventoryline, ...typeName, ...classDate }
      this.savePInventoryline(newPInventoryline)
      const pinventoryId = this.pinventory.id
      if (pinventoryId) {
        this.getPInventoryById(pinventoryId.toString())
        const newPInventory = this.pinventory
        this.setPInventory(newPInventory)
      }
    }
  }

  savePInventoryline = (pinventoryline: PInventoryline) => {
    if (!pinventoryline.id) {
      postData("pinventorylines/", pinventoryline)
        .then(res => {
          const pinventoryline = res.data
          this.setPInventoryline([pinventoryline])
          appModule.sendSuccessNotice("New record has been added.")
          appModule.closeNoticeWithDelay(3000)
        })
        .catch(err => {
          console.log(err)
          appModule.sendErrorNotice("Operation failed! Please try again later.")
          appModule.closeNoticeWithDelay(5000)
        })
    } else {
      putData("pinventories/" + pinventoryline.id.toString(), pinventoryline)
        .then(res => {
          const pinventory = res.data
          this.setPInventory(pinventory)
          appModule.sendSuccessNotice("The record has been updated.")
        })
        .catch(err => {
          console.log(err)
          appModule.sendErrorNotice("Operation failed! Please try again later.")
          appModule.closeNoticeWithDelay(5000)
        })
    }
  }

  deletePInventory = async (id: number) => {
    try {
      await deleteData(`pinventories/${id.toString()}`)
      appModule.sendSuccessNotice("Operation is done.")
      appModule.closeNoticeWithDelay(3000)
    } catch (error) {
      console.error(error)
      appModule.sendErrorNotice("Operation failed! Please try again later.")
      appModule.closeNoticeWithDelay(5000)
    } finally {
      this.setLoading(false)
    }
  }

  deletePInventoryline = (pinventoryline: PInventoryline) => {
    if (pinventoryline && pinventoryline.id && this.pinventory.id) {
      const pinventoryId = this.pinventory.id
      const pinventorylineId = pinventoryline.id
      const { pinventorylines } = this.pinventory
      if (pinventorylines) {
        pinventorylines.splice(
          pinventorylines.findIndex((p: PInventoryline) => p.id === pinventoryline.id),
          1
        )
        this.setPInventoryline(pinventorylines)
        deleteData(`pinventorylines/${pinventorylineId.toString()}`)
          .then(() => {
            this.getPInventoryById(pinventoryId.toString())
            const newPInventory = this.pinventory
            this.setPInventory(newPInventory)
            appModule.sendSuccessNotice("Operation is done.")
            appModule.closeNoticeWithDelay(3000)
          })
          .catch(err => {
            console.log(err)
            appModule.sendErrorNotice("Operation failed! Please try again later.")
            appModule.closeNoticeWithDelay(5000)
          })
      }
    }
  }

  clearPInventoryline = () => {
    this.setLoading(true)
    const pinventoryline: PInventoryline[] = []
    this.setPInventoryline(pinventoryline)
    this.setLoading(false)
  }

  setDataTable = (items: PInventory[]) => {
    const pagination = getPagination(items)
    this.setPagination(pagination)
    this.setItems(items)
  }

  setEmployees = (employees: Employee[]) => {
    this.employees = employees
  }

  setPInventoryId = (id: number | null) => {
    this.pinventoryId = id
  }

  setPInventory = (pinventory: PInventory) => {
    this.pinventory = pinventory
  }

  setPInventoryline = (pinventoryline: PInventoryline[]) => {
    this.pinventoryline = pinventoryline
  }

  setTracksheet = (tracksheet: Tracksheet) => {
    this.tracksheet = tracksheet
  }

  setProduct = (product: Product) => {
    this.product = product
  }

  setItems = (pinventories: PInventory[]) => {
    this.items = pinventories
  }

  setPagination = (pagination: Pagination) => {
    this.pagination = pagination
  }

  setLoading = (loading: boolean) => {
    this.loading = loading
  }
}

// Create and export a singleton instance
const app = createApp(PInventoryModule)
const vm = app.mount(document.createElement('div'))
export const pinventoryModule = vm as InstanceType<typeof PInventoryModule>
