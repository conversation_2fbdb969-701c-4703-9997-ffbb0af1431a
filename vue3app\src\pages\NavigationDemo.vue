<template>
  <v-container fluid class="pa-4">
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title class="text-h4">
            <v-icon class="mr-3" color="primary">mdi-navigation</v-icon>
            Vue 3 導航系統演示
          </v-card-title>
          
          <v-card-text>
            <v-alert type="info" class="mb-4">
              <strong>🎯 新的Vue 3導航系統特性</strong>
              <br>
              組件化架構、智能分組、響應式設計、徽章系統
            </v-alert>

            <!-- 導航特性展示 -->
            <v-row>
              <v-col cols="12" md="6">
                <v-card variant="outlined" class="mb-4">
                  <v-card-title class="text-h6">
                    <v-icon class="mr-2" color="success">mdi-vuejs</v-icon>
                    Vue 3 特性
                  </v-card-title>
                  <v-card-text>
                    <v-list density="compact">
                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="success">mdi-check-circle</v-icon>
                        </template>
                        <v-list-item-title>Composition API</v-list-item-title>
                        <v-list-item-subtitle>使用 &lt;script setup&gt; 語法</v-list-item-subtitle>
                      </v-list-item>
                      
                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="success">mdi-check-circle</v-icon>
                        </template>
                        <v-list-item-title>TypeScript 支援</v-list-item-title>
                        <v-list-item-subtitle>完整的類型定義和檢查</v-list-item-subtitle>
                      </v-list-item>
                      
                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="success">mdi-check-circle</v-icon>
                        </template>
                        <v-list-item-title>組件化設計</v-list-item-title>
                        <v-list-item-subtitle>模組化和可重用組件</v-list-item-subtitle>
                      </v-list-item>
                    </v-list>
                  </v-card-text>
                </v-card>
              </v-col>

              <v-col cols="12" md="6">
                <v-card variant="outlined" class="mb-4">
                  <v-card-title class="text-h6">
                    <v-icon class="mr-2" color="primary">mdi-responsive</v-icon>
                    響應式特性
                  </v-card-title>
                  <v-card-text>
                    <v-list density="compact">
                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="primary">mdi-cellphone</v-icon>
                        </template>
                        <v-list-item-title>移動端適配</v-list-item-title>
                        <v-list-item-subtitle>觸摸友好的交互設計</v-list-item-subtitle>
                      </v-list-item>
                      
                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="primary">mdi-desktop-classic</v-icon>
                        </template>
                        <v-list-item-title>Rail 模式</v-list-item-title>
                        <v-list-item-subtitle>桌面端摺疊導航支援</v-list-item-subtitle>
                      </v-list-item>
                      
                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="primary">mdi-auto-fix</v-icon>
                        </template>
                        <v-list-item-title>自動適配</v-list-item-title>
                        <v-list-item-subtitle>根據設備自動調整布局</v-list-item-subtitle>
                      </v-list-item>
                    </v-list>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>

            <!-- 導航組件架構 -->
            <v-card variant="outlined" class="mb-4">
              <v-card-title class="text-h6">
                <v-icon class="mr-2" color="orange">mdi-sitemap</v-icon>
                組件架構
              </v-card-title>
              <v-card-text>
                <v-row>
                  <v-col cols="12" sm="6" md="3">
                    <v-card color="blue-lighten-5" class="text-center pa-3">
                      <v-icon size="large" color="blue">mdi-view-dashboard</v-icon>
                      <div class="text-subtitle2 mt-2">NavigationDrawer</div>
                      <div class="text-caption">主容器</div>
                    </v-card>
                  </v-col>
                  
                  <v-col cols="12" sm="6" md="3">
                    <v-card color="green-lighten-5" class="text-center pa-3">
                      <v-icon size="large" color="green">mdi-account-circle</v-icon>
                      <div class="text-subtitle2 mt-2">NavigationUserInfo</div>
                      <div class="text-caption">用戶信息</div>
                    </v-card>
                  </v-col>
                  
                  <v-col cols="12" sm="6" md="3">
                    <v-card color="purple-lighten-5" class="text-center pa-3">
                      <v-icon size="large" color="purple">mdi-menu</v-icon>
                      <div class="text-subtitle2 mt-2">NavigationMenu</div>
                      <div class="text-caption">菜單列表</div>
                    </v-card>
                  </v-col>
                  
                  <v-col cols="12" sm="6" md="3">
                    <v-card color="orange-lighten-5" class="text-center pa-3">
                      <v-icon size="large" color="orange">mdi-folder-multiple</v-icon>
                      <div class="text-subtitle2 mt-2">NavigationSection</div>
                      <div class="text-caption">功能區段</div>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>

            <!-- 徽章系統展示 -->
            <v-card variant="outlined" class="mb-4">
              <v-card-title class="text-h6">
                <v-icon class="mr-2" color="red">mdi-label</v-icon>
                徽章系統
              </v-card-title>
              <v-card-text>
                <v-row>
                  <v-col cols="6" sm="3">
                    <div class="text-center">
                      <v-chip color="success" class="mb-2">NEW</v-chip>
                      <div class="text-caption">新功能標記</div>
                    </div>
                  </v-col>
                  
                  <v-col cols="6" sm="3">
                    <div class="text-center">
                      <v-chip color="primary" class="mb-2">Vue3</v-chip>
                      <div class="text-caption">Vue3升級功能</div>
                    </div>
                  </v-col>
                  
                  <v-col cols="6" sm="3">
                    <div class="text-center">
                      <v-chip color="green" class="mb-2">Android</v-chip>
                      <div class="text-caption">Android優化</div>
                    </div>
                  </v-col>
                  
                  <v-col cols="6" sm="3">
                    <div class="text-center">
                      <v-chip color="purple" class="mb-2">Config</v-chip>
                      <div class="text-caption">配置驅動</div>
                    </div>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>

            <!-- 快速導航 -->
            <v-card variant="outlined">
              <v-card-title class="text-h6">
                <v-icon class="mr-2" color="indigo">mdi-rocket-launch</v-icon>
                快速導航
              </v-card-title>
              <v-card-text>
                <v-row>
                  <v-col cols="12" sm="6" md="4">
                    <v-btn
                      color="primary"
                      block
                      size="large"
                      @click="navigateTo('/vue3-form-test-center')"
                      prepend-icon="mdi-vuejs"
                    >
                      Vue3 測試中心
                    </v-btn>
                  </v-col>
                  
                  <v-col cols="12" sm="6" md="4">
                    <v-btn
                      color="green"
                      block
                      size="large"
                      @click="navigateTo('/test-vue3/em-production-query-android')"
                      prepend-icon="mdi-android"
                    >
                      Android Layout
                    </v-btn>
                  </v-col>
                  
                  <v-col cols="12" sm="6" md="4">
                    <v-btn
                      color="purple"
                      block
                      size="large"
                      @click="navigateTo('/test-vue3/em-production-query-config')"
                      prepend-icon="mdi-cog"
                    >
                      配置系統
                    </v-btn>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const navigateTo = (path: string) => {
  router.push(path)
}
</script>

<style scoped>
.v-card {
  transition: all 0.3s ease;
}

.v-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.v-btn {
  transition: all 0.2s ease;
}

.v-btn:hover {
  transform: translateY(-1px);
}

.v-chip {
  font-weight: 600;
  letter-spacing: 0.5px;
}
</style>
