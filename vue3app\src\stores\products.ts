import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getData } from '@/utils/backend-api'
import type { Product, Category, Remark } from '@/types'

export const useProductsStore = defineStore('products', () => {
  // State
  const products = ref<Product[]>([])
  const categories = ref<Category[]>([])
  const remarks = ref<Remark[]>([])
  const loading = ref(false)

  // Getters
  const isLoading = computed(() => loading.value)
  const allCategories = computed(() => categories.value)
  const allRemarks = computed(() => remarks.value)

  // Actions
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setProducts = (value: Product[]) => {
    products.value = value
  }

  const setCategories = (value: Category[]) => {
    categories.value = value
  }

  const setRemarks = (value: Remark[]) => {
    remarks.value = value
  }

  // API Actions
  const getProducts = async () => {
    try {
      setLoading(true)
      const res = await getData("products")
      if (res.data) {
        setProducts(res.data)
      }
      return res.data
    } catch (error) {
      console.error('獲取產品列表失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const getCategories = async () => {
    try {
      setLoading(true)
      const res = await getData("categories")
      if (res.data) {
        setCategories(res.data)
      }
      return res.data
    } catch (error) {
      console.error('獲取分類列表失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const getRemarks = async () => {
    try {
      setLoading(true)
      const res = await getData("remarks")
      if (res.data) {
        setRemarks(res.data)
      }
      return res.data
    } catch (error) {
      console.error('獲取備註列表失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const getProductById = async (id: string | number) => {
    try {
      setLoading(true)
      const res = await getData(`products/${id}`)
      return res.data
    } catch (error) {
      console.error('獲取產品詳情失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 根據 QRCode 查詢產品資料 (使用與vue2app相同的API端點)
  const getProductByQRCode = async (qrCode: string) => {
    try {
      setLoading(true)
      console.log(`正在查詢 QRCode: ${qrCode}`)
      console.log(`API 端點: products/${qrCode}`)

      // 使用與vue2app相同的API端點：products/{id}
      const res = await getData(`products/${qrCode}`)
      console.log('API 響應:', res)

      if (res && res.data) {
        console.log('查詢成功，數據:', res.data)
        // 確保返回的是數組格式
        const productData = Array.isArray(res.data) ? res.data : [res.data]
        setProducts(productData)
        return productData
      } else {
        console.log('API 響應格式異常:', res)
        setProducts([])
        return []
      }
    } catch (error: any) {
      console.error('根據 QRCode 獲取產品資料失敗:', error)
      console.error('錯誤詳情:', {
        message: error?.message,
        status: error?.status,
        response: error?.response
      })
      setProducts([])
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 新增產品
  const createProduct = async (productData: Partial<Product>) => {
    try {
      setLoading(true)
      // 使用 POST 方法創建產品
      const res = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(productData)
      })
      const data = await res.json()
      if (data) {
        // 重新載入產品列表
        await getProducts()
        return data
      }
      throw new Error('創建產品失敗')
    } catch (error) {
      console.error('創建產品失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 更新產品
  const updateProduct = async (id: number, productData: Partial<Product>) => {
    try {
      setLoading(true)
      // 使用 PUT 方法更新產品
      const res = await fetch(`/api/products/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(productData)
      })
      const data = await res.json()
      if (data) {
        // 重新載入產品列表
        await getProducts()
        return data
      }
      throw new Error('更新產品失敗')
    } catch (error) {
      console.error('更新產品失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 刪除產品
  const deleteProduct = async (id: number) => {
    try {
      setLoading(true)
      // 使用 DELETE 方法刪除產品
      const res = await fetch(`/api/products/${id}`, {
        method: 'DELETE'
      })
      const data = await res.json()
      // 重新載入產品列表
      await getProducts()
      return data
    } catch (error) {
      console.error('刪除產品失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 搜尋產品
  const searchProducts = async (searchQuery: string) => {
    try {
      setLoading(true)
      const res = await getData(`products/search?q=${encodeURIComponent(searchQuery)}`)
      if (res.data) {
        setProducts(res.data)
        return res.data
      }
      return []
    } catch (error) {
      console.error('搜尋產品失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 根據分類篩選產品
  const getProductsByCategory = async (categoryId: number) => {
    try {
      setLoading(true)
      const res = await getData(`products/category/${categoryId}`)
      if (res.data) {
        setProducts(res.data)
        return res.data
      }
      return []
    } catch (error) {
      console.error('根據分類獲取產品失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 重置狀態
  const resetState = () => {
    products.value = []
    categories.value = []
    remarks.value = []
    loading.value = false
  }

  return {
    // State
    products,
    categories,
    remarks,
    loading,

    // Getters
    isLoading,
    allCategories,
    allRemarks,

    // Actions
    setLoading,
    setProducts,
    setCategories,
    setRemarks,
    getProducts,
    getCategories,
    getRemarks,
    getProductById,
    getProductByQRCode,
    createProduct,
    updateProduct,
    deleteProduct,
    searchProducts,
    getProductsByCategory,
    resetState
  }
})
