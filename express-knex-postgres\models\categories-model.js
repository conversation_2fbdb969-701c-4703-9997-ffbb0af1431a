const db = require("../config/dbConfig.js");
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET ALL CATEGORYS
const find = () => {
  return db("m_attributevalue")
    .select({
      categoryId: "m_attributevalue.m_attributevalue_id",
      categoryNO: "m_attributevalue.value",
      categoryName: "m_attributevalue.name"
    })
    .whereIn('m_attribute_id', ['1000025'])
    .orderBy("m_attributevalue.value","asc")
    
};

// GET SPECIFIC CATEGORY BY ID
const findById = id => {
  return db("aits_categories").where("id", id);

  //SQL RAW METHOD
  // return db.raw(`SELECT * FROM categories
  //                  WHERE id = ${id}`);
};

// ADD A CATEGORY
const addCategory = category => {
  return db("aits_categories").insert(category, "id");
};

// UPDATE CATEGORY
const updateCategory = (id, post) => {
  return db("aits_categories")
    .where("id", id)
    .update(post);
};

// REMOVE CATEGORY
const removeCategory = id => {
  return db("aits_categories")
    .where("id", id)
    .del();
};

module.exports = {
  find,
  findById,
  addCategory,
  updateCategory,
  removeCategory
};
