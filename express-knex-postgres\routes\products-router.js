const router = require("express").Router();
const moment = require('moment');

const productsDB = require("../models/products-model.js");
const categoriesDB = require("../models/categories-model.js");
const remarksDB = require("../models/remarks-model.js");

// GET PRODUCTS BY QR CODE
router.get("/:id", async (req, res) => {
  const qrCode = req.params.id;
  
  try {
    console.log(`查詢 QR Code: ${qrCode}`);
    
    const product = await productsDB.findById(qrCode);
    console.log(`查詢結果:`, product);

    if (!product || product.length === 0) {
      console.log(`查無產品資料: ${qrCode}`);
      return res.status(404).json({ err: "The product with the specified QR Code does not exist" });
    }

    const [categories, remarks] = await Promise.all([
      categoriesDB.find(),
      remarksDB.find()
    ]);

    const transformedProducts = product.map(mem => {
      const pDate = moment(mem.productDate);
      const wDate = moment(mem.workDate);
      const dryTime = wDate.diff(pDate, 'hours');
      const positionNames = ['F', 'M', 'B'];
      
      mem.productDate = pDate.format("YYYY-M-D HH:mm");
      mem.workDate = wDate.format("YYYY-M-D HH:mm");
      mem.dryTime = dryTime;
      mem.positionName = positionNames[mem.position - 1] || '';
      
      const category = categories.find(info => info.id === mem.categoryId);
      mem.categoryName = category ? category.categoryname : '';
      
      const remark = remarks.find(info => info.id === mem.remarkId);
      mem.remarkName = remark ? remark.remarkname : '';

      return mem;
    });

    console.log(`轉換後的產品資料:`, transformedProducts);
    res.status(200).json(transformedProducts);
  } catch (err) {
    console.error(`查詢產品失敗:`, err);
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;
