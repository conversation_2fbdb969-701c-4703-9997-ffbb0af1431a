import { DB } from "./demo-data";
import { Entity } from "@/types";
import url from "url";
import querystring from "querystring";
import { getSeachFilters } from "./app-util";

const ds: TODO = DB;
console.log("ds is", ds);

const EXPAND = "_expand";
const EMBED = "_exbed";

function getModel(action: string) {
  if (action.includes("/")) {
    return action.substring(0, action.indexOf("/"));
  } else {
    return action;
  }
}

function getId(action: string): number {
  if (action.includes("/")) {
    return parseInt(action.substring(action.indexOf("/") + 1));
  } else {
    return 0;
  }
}

function getExpand(qs: TODO) {
  if (EXPAND in qs) {
    return qs[EXPAND];
  } else return "";
}

function parseRequest(req: string) {
  const parsedUrl = url.parse(req);
  console.log("parsedUrl is ", parsedUrl);
  const parsedQs = querystring.parse(parsedUrl.query);
  console.log("parseQs is ", parsedQs);
  const model = getModel(parsedUrl.pathname);
  console.log("model is ", model);
  const id = getId(parsedUrl.pathname);
  console.log("id is ", id);
  const exp = getExpand(parsedQs);
  console.log("exp is ", exp);
  const filters = getSeachFilters(parsedQs);
  console.log("filters is ", filters);
  return { model, id, exp, filters };
}

export function login(_action: string, data: TODO): Promise<TODO> {
  return new Promise(function(resolve, _reject) {
    if (data.username === "<EMAIL>" && data.password === "password") {
      const { accessToken: accessToken, user } = ds.token;
      setTimeout(resolve, 200, {
        data: {
          accessToken: accessToken,
          user
        }
      });
    } else {
      _reject({
        code: 403,
        text: "Your name or password is wrong"
      });
    }
  });
}
