const router = require("express").Router();
const moment = require('moment');

const downgradesDB = require("../models/downgrades-model.js");
const employeesDB = require("../models/employees-model.js");
const downgradelinesDB = require("../models/downgradelines-model.js");
const categoriesDB = require("../models/categories-model.js");
const remarksDB = require("../models/remarks-model.js");

// GET ALL DOWNGRADES OF T2
router.get("/t2", async (req, res) => {
  try {
    const urlPath = req.originalUrl.split('/')[3];
    const type = urlPath.toUpperCase();
    if (!type) {
      return res.status(400).json({ error: "Type parameter is missing." });
    }
    const downgrades = await downgradesDB.findByType(type);
    const employees = await employeesDB.findOfTW();
    // Map employees to downgrade
    downgrades.forEach(mem => {
      const employee = employees.find(info => info.employId === mem.employId);
      if (employee) {
        mem.employName = `${employee.employNO} ${employee.userName}`;
      }
    });
    // Format dates
    downgrades.forEach(mem => {
      mem.classDate = moment(mem.classDate).format("YYYY-M-D");
    });
    res.status(200).json(downgrades);
  } catch (err) {
    res.status(500).json({err: err.message });
  }
});

// GET ALL DOWNGRADES OF DISPOSAL
router.get("/disposal", async (req, res) => {
  try {
    const urlPath = req.originalUrl.split('/')[3];
    const type = urlPath.toUpperCase(); 
    if (!type){
      return res.status(400).json({ error: "Type parameter is missing." });
    }
    const downgrades = await downgradesDB.findByType(type);
    const employees = await employeesDB.findOfTW();
    // Map employees to downgrade
    downgrades.forEach(mem => {
      const employee = employees.find(info => info.employId === mem.employId);
      if (employee) {
        mem.employName = `${employee.employNO} ${employee.userName}`;
      }
    });
    // Format dates
    downgrades.forEach(mem => {
      mem.classDate = moment(mem.classDate).format("YYYY-M-D");
    });
    res.status(200).json(downgrades);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET DOWNGRADE BY ID
router.get("/:id", async (req, res) => {
  const downgradeId = req.params.id;
  try {
    const downgrade = await downgradesDB.findById(downgradeId);
    if (!downgrade) {
      return res.status(404).json({ err: "The downgrade with the specified id does not exist" });
    }
    const downgradelines = await downgradelinesDB.findById(downgrade[0].id);
    const employee = await employeesDB.findById(downgrade[0].employId);
    const categories = await categoriesDB.find();
    const remarks = await remarksDB.find();
    // Map employees to downgrade
    downgrade.forEach(mem => {      
        mem.employName = `${employee[0].employNO} ${employee[0].userName}`;      
    });
    // Format dates
    downgrade.forEach(mem => {
      mem.classDate = moment(mem.classDate).format("YYYY-M-D");
    });
    downgradelines.forEach(mem => {
      mem.workDate = moment(mem.workDate).format("YYYY-M-D HH:mm");
    });
    // Map categories to downgradelines
    downgradelines.forEach(mem => {
      const category = categories.find(info => info.categoryId === mem.categoryId);
      if (category) {
        mem.categoryName = category.categoryName;
      }
    });
    // Map remarks to downgradelines
    downgradelines.forEach(mem => {
      const remark = remarks.find(info => info.remarkId === mem.remarkId);
      if (remark) {
        mem.remarkName = remark.remarkName;
      }
    });
    // Assign downgradelines to respective downgrade
    downgrade.forEach(mem => {
      mem.downgradelines = downgradelines.filter(d => d.downgradeId === mem.id);
    });
    res.status(200).json(downgrade);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// INSERT DOWNGRADE INTO DB
router.post("/", async (req, res) => {
  const newDowngrade = req.body;
  if (!newDowngrade.shiftName) {
    res.status(404).json({ err: "Please provide the full data" });
  } else {
    try {
      const downgrade = await downgradesDB.addDowngrade(newDowngrade);
      res.status(201).json(downgrade);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

// UPDATE DOWNGRADE INTO DB
router.put("/:id", async (req, res) => {
  const downgradeId = req.params.id;
  const newChanges = req.body;
  if (!newChanges.shiftName) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const addChanges = await downgradesDB.updateDowngrade(downgradeId, newChanges);
      res.status(200).json(addChanges);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

// REMOVE DOWNGRADE INTO DB
router.delete("/:id", async (req, res) => {
  const downgradeId = req.params.id;
  try {
    const deleting = await downgradesDB.removeDowngrade(downgradeId);
    res.status(204).json(deleting);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;