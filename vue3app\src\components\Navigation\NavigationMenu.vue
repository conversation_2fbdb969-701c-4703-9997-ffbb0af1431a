<template>
  <v-list density="compact" nav>
    <v-list-item
      v-for="item in items"
      :key="item.title"
      :value="item.title"
      :prepend-icon="item.icon"
      :title="item.title"
      @click="handleNavigation(item)"
      :active="isActive(item)"
      :class="getItemClass(item)"
    >
      <!-- 徽章顯示 -->
      <template v-slot:append v-if="item.badge">
        <v-chip
          :color="item.color || 'primary'"
          size="x-small"
          variant="flat"
          class="ml-2"
        >
          {{ item.badge }}
        </v-chip>
      </template>

      <!-- Rail 模式下的工具提示 -->
      <v-tooltip 
        v-if="showTooltip" 
        activator="parent" 
        location="end"
        :disabled="!showTooltip"
      >
        {{ item.title }}
        <v-chip
          v-if="item.badge"
          :color="item.color || 'primary'"
          size="x-small"
          variant="flat"
          class="ml-1"
        >
          {{ item.badge }}
        </v-chip>
      </v-tooltip>
    </v-list-item>
  </v-list>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface AppMenu {
  icon: string
  title: string
  vertical?: string
  link: string
  badge?: string
  color?: string
}

interface Props {
  items: AppMenu[]
  activeItem: string
  isMobile: boolean
  mini?: boolean
}

interface Emits {
  (e: 'navigate', item: AppMenu): void
}

const props = withDefaults(defineProps<Props>(), {
  mini: false
})

const emit = defineEmits<Emits>()

// 計算屬性
const showTooltip = computed(() => {
  return props.mini && !props.isMobile
})

// 方法
const handleNavigation = (item: AppMenu) => {
  emit('navigate', item)
}

const isActive = (item: AppMenu) => {
  return props.activeItem.includes(item.title) || 
         props.activeItem.includes(item.vertical || '') ||
         props.activeItem.includes(item.link)
}

const getItemClass = (item: AppMenu) => {
  const classes = []
  
  if (isActive(item)) {
    classes.push('v-list-item--active')
    classes.push('text-primary')
    classes.push('bg-primary-lighten-5')
  }
  
  // 根據徽章添加特殊樣式
  if (item.badge === 'NEW') {
    classes.push('nav-item-new')
  } else if (item.badge === 'Vue3') {
    classes.push('nav-item-vue3')
  } else if (item.badge === 'Android') {
    classes.push('nav-item-android')
  }
  
  return classes.join(' ')
}
</script>

<style scoped>
/* 基本導航項目樣式 */
.v-list-item {
  margin: 2px 8px;
  border-radius: 8px !important;
  transition: all 0.2s ease;
}

.v-list-item:hover {
  background-color: rgba(25, 118, 210, 0.08) !important;
  transform: translateX(2px);
}

.v-list-item--active {
  background-color: rgba(25, 118, 210, 0.12) !important;
  border-left: 3px solid rgb(25, 118, 210);
}

.v-list-item--active:hover {
  background-color: rgba(25, 118, 210, 0.16) !important;
}

/* 特殊徽章樣式 */
.nav-item-new {
  position: relative;
}

.nav-item-new::before {
  content: '';
  position: absolute;
  left: -4px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: linear-gradient(45deg, #4CAF50, #8BC34A);
  border-radius: 2px;
  opacity: 0.8;
}

.nav-item-vue3 {
  border-left: 2px solid #4FC08D;
}

.nav-item-android {
  border-left: 2px solid #3DDC84;
}

/* 圖標樣式 */
.v-list-item :deep(.v-list-item__prepend .v-icon) {
  transition: all 0.2s ease;
}

.v-list-item:hover :deep(.v-list-item__prepend .v-icon) {
  transform: scale(1.1);
}

.v-list-item--active :deep(.v-list-item__prepend .v-icon) {
  color: rgb(25, 118, 210) !important;
}

/* 徽章樣式 */
.v-chip {
  font-size: 10px !important;
  height: 18px !important;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* 深色模式適配 */
@media (prefers-color-scheme: dark) {
  .v-list-item:hover {
    background-color: rgba(33, 150, 243, 0.08) !important;
  }
  
  .v-list-item--active {
    background-color: rgba(33, 150, 243, 0.12) !important;
    border-left-color: rgb(33, 150, 243);
  }
  
  .v-list-item--active:hover {
    background-color: rgba(33, 150, 243, 0.16) !important;
  }
  
  .v-list-item--active :deep(.v-list-item__prepend .v-icon) {
    color: rgb(33, 150, 243) !important;
  }
}

/* 響應式調整 */
@media (max-width: 599px) {
  .v-list-item {
    margin: 1px 4px;
    padding: 8px 12px;
  }
  
  .v-list-item :deep(.v-list-item-title) {
    font-size: 14px;
  }
  
  .v-chip {
    font-size: 9px !important;
    height: 16px !important;
  }
}

/* 動畫效果 */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.nav-item-new .v-chip {
  animation: pulse 2s infinite;
}

/* 減少動畫模式支援 */
@media (prefers-reduced-motion: reduce) {
  .v-list-item,
  .v-list-item :deep(.v-list-item__prepend .v-icon),
  .nav-item-new .v-chip {
    transition: none !important;
    animation: none !important;
  }
  
  .v-list-item:hover {
    transform: none !important;
  }
}

/* 高對比度模式支援 */
@media (prefers-contrast: high) {
  .v-list-item--active {
    border-left-width: 4px !important;
    background-color: rgba(25, 118, 210, 0.2) !important;
  }
  
  .v-list-item:hover {
    background-color: rgba(25, 118, 210, 0.15) !important;
  }
}
</style>
