#!/usr/bin/env node

/**
 * Vue3 遷移執行腳本
 * 
 * 簡化的執行腳本，用於快速執行 Vue3 遷移流程
 */

const fs = require('fs')
const path = require('path')

console.log('🚀 Vue3 遷移執行器')
console.log('='.repeat(50))

// 檢查是否在正確的目錄
if (!fs.existsSync('src/pages') || !fs.existsSync('package.json')) {
  console.error('❌ 請在 vue2app 目錄下執行此腳本')
  process.exit(1)
}

// 導入遷移腳本
const migration = require('./scripts/vue3-migration.js')

// 執行選項
const args = process.argv.slice(2)
const command = args[0] || 'help'

async function main() {
  switch (command) {
    case 'analyze':
      console.log('🔍 分析 Vue2 表單...')
      const analysis = await migration.analyzeVue2Forms()
      console.log('\n📊 分析結果:')
      Object.entries(analysis).forEach(([file, data]) => {
        console.log(`\n📄 ${file}:`)
        console.log(`  - 類型: ${data.type || '未知'}`)
        console.log(`  - 數據模型: ${Object.keys(data.dataModel || {}).length} 個屬性`)
        console.log(`  - API 調用: ${(data.apiCalls || []).length} 個`)
        console.log(`  - 驗證規則: ${(data.validationRules || []).length} 個`)
        console.log(`  - Store 模組: ${data.storeModule || '未知'}`)

        if (data.type === 'form' && data.detailOperations) {
          console.log(`  - 明細操作: ${data.detailOperations.length} 個`)
        }

        if (data.type === 'list' && data.tableHeaders) {
          console.log(`  - 表格欄位: ${data.tableHeaders.length} 個`)
        }
      })
      break
      
    case 'test':
      console.log('🧪 生成測試代碼...')
      await migration.generateTestCode()
      console.log('✅ 測試代碼生成完成')
      break
      
    case 'integrate':
      console.log('🔗 整合數據連結...')
      const vue2Analysis = await migration.analyzeVue2Forms()
      await migration.integrateDataConnections(vue2Analysis)
      console.log('✅ 數據整合完成')
      break
      
    case 'replace':
      console.log('🔄 替換表單...')
      await migration.replaceForms()
      await migration.updateRoutes()
      console.log('✅ 表單替換完成')
      break
      
    case 'full':
      console.log('🚀 執行完整遷移...')
      await migration.runMigration()
      break
      
    case 'status':
      showMigrationStatus()
      break
      
    case 'help':
    default:
      showHelp()
      break
  }
}

function showMigrationStatus() {
  console.log('\n📊 Vue3 遷移狀態')
  console.log('-'.repeat(40))
  
  migration.formMappings.forEach(mapping => {
    const vue2FormPath = path.join('src/pages', mapping.vue2Form)
    const vue2ListPath = path.join('src/pages', mapping.vue2List)
    const vue3TestPath = path.join('src/pages', mapping.vue3Test)
    const vue3FinalFormPath = path.join('src/pages', mapping.vue3FinalForm)
    const vue3FinalListPath = path.join('src/pages', mapping.vue3FinalList)

    console.log(`\n📋 ${mapping.name}:`)
    console.log(`  Vue2 主檔: ${fs.existsSync(vue2FormPath) ? '✅' : '❌'} ${mapping.vue2Form}`)
    console.log(`  Vue2 明細: ${fs.existsSync(vue2ListPath) ? '✅' : '❌'} ${mapping.vue2List}`)
    console.log(`  Vue3 測試: ${fs.existsSync(vue3TestPath) ? '✅' : '❌'} ${mapping.vue3Test}`)
    console.log(`  Vue3 主檔: ${fs.existsSync(vue3FinalFormPath) ? '✅' : '❌'} ${mapping.vue3FinalForm}`)
    console.log(`  Vue3 明細: ${fs.existsSync(vue3FinalListPath) ? '✅' : '❌'} ${mapping.vue3FinalList}`)

    // 檢查主檔是否已經是 Vue3 版本
    if (fs.existsSync(vue3FinalFormPath)) {
      const content = fs.readFileSync(vue3FinalFormPath, 'utf8')
      const isVue3 = content.includes('<script setup') || content.includes('ref(') || content.includes('reactive(')
      const isVue2 = content.includes('@Component') || content.includes('class ') && content.includes('extends Vue')

      if (isVue3) {
        console.log(`  主檔狀態: 🟢 已遷移到 Vue3`)
      } else if (isVue2) {
        console.log(`  主檔狀態: 🟡 仍為 Vue2`)
      } else {
        console.log(`  主檔狀態: ⚪ 未知`)
      }
    }

    // 檢查明細檔是否已經是 Vue3 版本
    if (fs.existsSync(vue3FinalListPath)) {
      const content = fs.readFileSync(vue3FinalListPath, 'utf8')
      const isVue3 = content.includes('<script setup') || content.includes('ref(') || content.includes('reactive(')
      const isVue2 = content.includes('@Component') || content.includes('class ') && content.includes('extends Vue')

      if (isVue3) {
        console.log(`  明細狀態: 🟢 已遷移到 Vue3`)
      } else if (isVue2) {
        console.log(`  明細狀態: 🟡 仍為 Vue2`)
      } else {
        console.log(`  明細狀態: ⚪ 未知`)
      }
    }
  })
  
  // 統計
  const totalForms = migration.formMappings.length
  const vue3Forms = migration.formMappings.filter(mapping => {
    const finalFormPath = path.join('src/pages', mapping.vue3FinalForm)
    if (fs.existsSync(finalFormPath)) {
      const content = fs.readFileSync(finalFormPath, 'utf8')
      return content.includes('<script setup') || content.includes('ref(') || content.includes('reactive(')
    }
    return false
  }).length
  
  const migrationProgress = totalForms > 0 ? (vue3Forms / totalForms * 100).toFixed(1) : 0
  
  console.log('\n📈 總體進度:')
  console.log(`  總表單數: ${totalForms}`)
  console.log(`  已遷移: ${vue3Forms}`)
  console.log(`  進度: ${migrationProgress}%`)
  
  if (migrationProgress >= 100) {
    console.log('🎉 所有表單已完成 Vue3 遷移！')
  } else {
    console.log(`⚠️  還有 ${totalForms - vue3Forms} 個表單需要遷移`)
  }
}

function showHelp() {
  console.log('\n📖 使用說明:')
  console.log('node run-migration.js <command>')
  console.log('')
  console.log('可用命令:')
  console.log('  analyze   - 分析 Vue2 表單結構')
  console.log('  test      - 生成測試代碼')
  console.log('  integrate - 整合數據連結')
  console.log('  replace   - 替換表單文件')
  console.log('  full      - 執行完整遷移流程')
  console.log('  status    - 顯示遷移狀態')
  console.log('  help      - 顯示此幫助信息')
  console.log('')
  console.log('範例:')
  console.log('  node run-migration.js status    # 查看當前狀態')
  console.log('  node run-migration.js analyze   # 分析 Vue2 表單')
  console.log('  node run-migration.js full      # 執行完整遷移')
  console.log('')
  console.log('⚠️  注意: 執行遷移前會自動創建備份')
}

// 錯誤處理
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕獲的異常:', error.message)
  console.log('🔄 如果遇到問題，請檢查備份文件並手動恢復')
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未處理的 Promise 拒絕:', reason)
  console.log('🔄 如果遇到問題，請檢查備份文件並手動恢復')
  process.exit(1)
})

// 執行主函數
main().catch(error => {
  console.error('❌ 執行失敗:', error.message)
  console.log('\n🔧 故障排除建議:')
  console.log('1. 確保在 vue2app 目錄下執行')
  console.log('2. 檢查文件權限')
  console.log('3. 確保所有依賴已安裝')
  console.log('4. 查看備份文件進行手動恢復')
  process.exit(1)
})
