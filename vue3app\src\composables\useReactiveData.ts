import { ref, reactive, computed, watch, toRefs, unref } from 'vue'
import { cloneDeep, isEqual } from 'lodash'

// 響應式數據選項
export interface ReactiveDataOptions<T> {
  defaultValue?: T
  deep?: boolean
  immediate?: boolean
  onChanged?: (newValue: T, oldValue: T) => void
  validator?: (value: T) => boolean
  transformer?: (value: T) => T
}

// 響應式數據管理
export function useReactiveData<T>(
  initialValue: T,
  options: ReactiveDataOptions<T> = {}
) {
  const {
    defaultValue = initialValue,
    deep = true,
    immediate = false,
    onChanged,
    validator,
    transformer
  } = options

  // 創建響應式數據
  const data = ref<T>(cloneDeep(initialValue))
  const originalData = ref<T>(cloneDeep(initialValue))
  const isDirty = ref(false)

  // 設置數據
  const setData = (newValue: T) => {
    // 驗證數據
    if (validator && !validator(newValue)) {
      console.warn('Data validation failed:', newValue)
      return false
    }

    // 轉換數據
    const finalValue = transformer ? transformer(newValue) : newValue
    
    data.value = cloneDeep(finalValue)
    return true
  }

  // 重置數據
  const resetData = () => {
    data.value = cloneDeep(defaultValue)
    originalData.value = cloneDeep(defaultValue)
    isDirty.value = false
  }

  // 恢復到原始數據
  const revertData = () => {
    data.value = cloneDeep(originalData.value)
    isDirty.value = false
  }

  // 保存當前數據為原始數據
  const saveAsOriginal = () => {
    originalData.value = cloneDeep(data.value)
    isDirty.value = false
  }

  // 更新特定欄位
  const updateField = (path: string, value: any) => {
    const keys = path.split('.')
    let current = data.value as any
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {}
      }
      current = current[keys[i]]
    }
    
    current[keys[keys.length - 1]] = value
  }

  // 獲取特定欄位值
  const getField = (path: string) => {
    const keys = path.split('.')
    let current = data.value as any
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key]
      } else {
        return undefined
      }
    }
    
    return current
  }

  // 監聽數據變化
  watch(
    data,
    (newValue, oldValue) => {
      // 檢查是否有變化
      isDirty.value = !isEqual(newValue, originalData.value)
      
      // 調用變化回調
      if (onChanged) {
        onChanged(newValue, oldValue)
      }
    },
    { deep, immediate }
  )

  return {
    data,
    originalData,
    isDirty,
    setData,
    resetData,
    revertData,
    saveAsOriginal,
    updateField,
    getField
  }
}

// 表單數據管理
export function useFormData<T extends Record<string, any>>(
  initialData: T,
  options: ReactiveDataOptions<T> = {}
) {
  const {
    data,
    originalData,
    isDirty,
    setData,
    resetData,
    revertData,
    saveAsOriginal,
    updateField,
    getField
  } = useReactiveData(initialData, options)

  // 批量更新欄位
  const updateFields = (updates: Partial<T>) => {
    Object.keys(updates).forEach(key => {
      if (key in data.value) {
        (data.value as any)[key] = updates[key]
      }
    })
  }

  // 獲取表單變化的欄位
  const getChangedFields = (): Partial<T> => {
    const changes: Partial<T> = {}
    
    Object.keys(data.value).forEach(key => {
      if (!isEqual(data.value[key], originalData.value[key])) {
        changes[key] = data.value[key]
      }
    })
    
    return changes
  }

  // 檢查特定欄位是否有變化
  const isFieldChanged = (fieldName: keyof T): boolean => {
    return !isEqual(data.value[fieldName], originalData.value[fieldName])
  }

  // 重置特定欄位
  const resetField = (fieldName: keyof T) => {
    data.value[fieldName] = cloneDeep(originalData.value[fieldName])
  }

  return {
    formData: data,
    originalData,
    isDirty,
    setData,
    resetData,
    revertData,
    saveAsOriginal,
    updateField,
    getField,
    updateFields,
    getChangedFields,
    isFieldChanged,
    resetField
  }
}

// 列表數據管理
export function useListData<T>(
  initialItems: T[] = [],
  options: {
    keyField?: keyof T
    onItemAdded?: (item: T) => void
    onItemUpdated?: (item: T, index: number) => void
    onItemRemoved?: (item: T, index: number) => void
  } = {}
) {
  const {
    keyField = 'id' as keyof T,
    onItemAdded,
    onItemUpdated,
    onItemRemoved
  } = options

  const items = ref<T[]>(cloneDeep(initialItems))
  const selectedItems = ref<T[]>([])
  const loading = ref(false)

  // 添加項目
  const addItem = (item: T) => {
    items.value.push(cloneDeep(item))
    if (onItemAdded) {
      onItemAdded(item)
    }
  }

  // 更新項目
  const updateItem = (index: number, item: T) => {
    if (index >= 0 && index < items.value.length) {
      items.value[index] = cloneDeep(item)
      if (onItemUpdated) {
        onItemUpdated(item, index)
      }
    }
  }

  // 根據key更新項目
  const updateItemByKey = (key: any, item: T) => {
    const index = items.value.findIndex(i => i[keyField] === key)
    if (index !== -1) {
      updateItem(index, item)
    }
  }

  // 移除項目
  const removeItem = (index: number) => {
    if (index >= 0 && index < items.value.length) {
      const removedItem = items.value.splice(index, 1)[0]
      if (onItemRemoved) {
        onItemRemoved(removedItem, index)
      }
    }
  }

  // 根據key移除項目
  const removeItemByKey = (key: any) => {
    const index = items.value.findIndex(i => i[keyField] === key)
    if (index !== -1) {
      removeItem(index)
    }
  }

  // 清空列表
  const clearItems = () => {
    items.value = []
    selectedItems.value = []
  }

  // 設置列表
  const setItems = (newItems: T[]) => {
    items.value = cloneDeep(newItems)
  }

  // 查找項目
  const findItem = (predicate: (item: T) => boolean): T | undefined => {
    return items.value.find(predicate)
  }

  // 根據key查找項目
  const findItemByKey = (key: any): T | undefined => {
    return items.value.find(i => i[keyField] === key)
  }

  // 過濾項目
  const filterItems = (predicate: (item: T) => boolean): T[] => {
    return items.value.filter(predicate)
  }

  // 選擇項目
  const selectItem = (item: T) => {
    if (!selectedItems.value.includes(item)) {
      selectedItems.value.push(item)
    }
  }

  // 取消選擇項目
  const deselectItem = (item: T) => {
    const index = selectedItems.value.indexOf(item)
    if (index !== -1) {
      selectedItems.value.splice(index, 1)
    }
  }

  // 切換選擇狀態
  const toggleSelection = (item: T) => {
    if (selectedItems.value.includes(item)) {
      deselectItem(item)
    } else {
      selectItem(item)
    }
  }

  // 全選
  const selectAll = () => {
    selectedItems.value = [...items.value]
  }

  // 取消全選
  const deselectAll = () => {
    selectedItems.value = []
  }

  // 計算屬性
  const itemCount = computed(() => items.value.length)
  const selectedCount = computed(() => selectedItems.value.length)
  const hasItems = computed(() => items.value.length > 0)
  const hasSelection = computed(() => selectedItems.value.length > 0)
  const isAllSelected = computed(() => 
    items.value.length > 0 && selectedItems.value.length === items.value.length
  )

  return {
    items,
    selectedItems,
    loading,
    
    // 項目操作
    addItem,
    updateItem,
    updateItemByKey,
    removeItem,
    removeItemByKey,
    clearItems,
    setItems,
    
    // 查找和過濾
    findItem,
    findItemByKey,
    filterItems,
    
    // 選擇操作
    selectItem,
    deselectItem,
    toggleSelection,
    selectAll,
    deselectAll,
    
    // 計算屬性
    itemCount,
    selectedCount,
    hasItems,
    hasSelection,
    isAllSelected
  }
}

// 分頁數據管理
export function usePaginationData() {
  const pagination = reactive({
    page: 1,
    rowsPerPage: 10,
    pages: 1,
    totalItems: 0
  })

  const setPagination = (newPagination: Partial<typeof pagination>) => {
    Object.assign(pagination, newPagination)
  }

  const resetPagination = () => {
    pagination.page = 1
    pagination.pages = 1
    pagination.totalItems = 0
  }

  const updateTotalItems = (total: number) => {
    pagination.totalItems = total
    pagination.pages = Math.ceil(total / pagination.rowsPerPage)
  }

  const goToPage = (page: number) => {
    if (page >= 1 && page <= pagination.pages) {
      pagination.page = page
    }
  }

  const nextPage = () => {
    if (pagination.page < pagination.pages) {
      pagination.page++
    }
  }

  const prevPage = () => {
    if (pagination.page > 1) {
      pagination.page--
    }
  }

  // 計算屬性
  const hasNextPage = computed(() => pagination.page < pagination.pages)
  const hasPrevPage = computed(() => pagination.page > 1)
  const startIndex = computed(() => (pagination.page - 1) * pagination.rowsPerPage)
  const endIndex = computed(() => Math.min(startIndex.value + pagination.rowsPerPage, pagination.totalItems))

  return {
    pagination,
    setPagination,
    resetPagination,
    updateTotalItems,
    goToPage,
    nextPage,
    prevPage,
    hasNextPage,
    hasPrevPage,
    startIndex,
    endIndex
  }
}
