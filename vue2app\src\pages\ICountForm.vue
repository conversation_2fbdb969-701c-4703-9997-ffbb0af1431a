<template>
  <v-container fluid>
    <v-flex xs12>
      <v-card class="grey lighten-4 elevation-0">
        <v-form ref="validForm" v-model="formValid" lazy-validation>
          <v-card-title class="title">
            {{ title }}
            <v-spacer></v-spacer>
            <v-btn
              elevation="4"
              fab
              small
              dark
              class="grey mr-2"
              @click.native="cancel()"
            >
              <v-icon dark="">mdi-close-circle-outline</v-icon>
            </v-btn>
            <v-btn
              elevation="4"
              fab
              small
              dark
              class="purple mr-2"
              :disabled="!formValid"
              @click.native="save()"
            >
              <v-icon>mdi-content-save-all</v-icon>
            </v-btn>
            <v-btn
              elevation="4"
              fab
              small
              dark
              class="blue"
              :disabled="!formValid"
              @click.native="addTracksheet()"
            >
              <v-icon>mdi-plus</v-icon>
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-container fluid grid-list-md>
              <v-layout row wrap>
                <v-flex md4 xs12>
                  <v-text-field
                    name="id"
                    label="單號"
                    type="number"
                    hint="ICountID is required"
                    value="Input text"
                    v-model="icount.id"
                    class="input-group--focused"
                    readonly
                  ></v-text-field>
                </v-flex>
                <v-flex md4 xs12>
                  <v-menu
                    :close-on-content-click="false"
                    v-model="classDateMenu"
                    transition="v-scale-transition"
                    offset-y
                    :nudge-left="40"
                    max-width="290px"
                  >
                    <template v-slot:activator="{ on }">
                      <v-text-field
                        v-on="on"
                        label="日期"
                        v-model="icount.classDate"
                        prepend-icon="mdi-calendar"
                        readonly
                      ></v-text-field>
                    </template>
                    <v-date-picker
                      v-model="icount.classDate"
                      no-title
                      scrollable
                    >
                    </v-date-picker>
                  </v-menu>
                </v-flex>
                <v-flex md4 xs12>
                  <v-radio-group
                    name="shiftName"
                    label="勤別"
                    v-model="icount.shiftName"
                    :rules="[value => !!value || '必要!!請選擇']"
                    required
                    row
                  >
                    <v-radio label="I" value="1"></v-radio>
                    <v-radio label="II" value="2"></v-radio>
                    <v-radio label="III" value="3"></v-radio>
                  </v-radio-group>
                </v-flex>
                <v-flex md6 xs12>
                  <v-autocomplete
                    v-bind:items="employees"
                    label="人員"
                    item-text="employName"
                    item-value="employId"
                    v-model="icount.employId"
                    :rules="[value => !!value || '必要!!請選擇']"
                    required
                  ></v-autocomplete>
                </v-flex>
                <v-flex md4 xs12>
                  <v-radio-group
                    name="groupName"
                    label="組別"
                    v-model="icount.groupName"
                    :rules="[value => !!value || '必要!!請選擇']"
                    required
                    row
                  >
                    <v-radio label="A" value="A"></v-radio>
                    <v-radio label="B" value="B"></v-radio>
                    <v-radio label="C" value="C"></v-radio>
                    <v-radio label="D" value="D"></v-radio>
                  </v-radio-group>
                </v-flex>
                <v-flex md4 xs12>
                  <v-text-field
                    name="quantity"
                    label="小計"
                    type="number"
                    v-model="icount.quantity"
                    class="input-group--focused"
                    readonly
                  ></v-text-field>
                </v-flex>

                <v-flex xs12>
                  <v-card>
                    <Table
                      v-if="loading === false"
                      :headers="headers"
                      :items="icount.icountlines"
                      :pagination="pagination"
                      :setSearch="true"
                      :setEdit="false"
                      :setRemove="true"
                      :disableSort="false"
                      @remove="remove"
                    >
                    </Table>
                  </v-card>
                </v-flex>
              </v-layout>
            </v-container>
          </v-card-text>
        </v-form>
      </v-card>
    </v-flex>
    <v-layout row justify-center>
      <v-dialog v-model="addTracksheetModal" width="700" persistent>
        <v-form ref="validDetail" v-model="detailValid" lazy-validation>
          <v-card>
            <v-card-title
              >{{ title }}
              <v-spacer></v-spacer>
              <v-card-actions>
                <v-btn
                  class="green lighten-1"
                  text="text"
                  :disabled="!detailValid"
                  @click.native="saveICountline"
                  >Confirm</v-btn
                >
                <v-btn
                  class="orange lighten-1"
                  text="text"
                  @click.native="cancelAddTracksheet"
                  >Cancel</v-btn
                >
              </v-card-actions>
            </v-card-title>
            <v-card-text>
              1.請掃Yarn傳票QRCode 2.輸入個數
              <v-text-field
                ref="qrCodeInput"
                v-model="searchFilter.contain.tracksheetNO"
                append-icon="mdi-magnify"
                label="Yarn傳票QRCode"
                @keydown.enter.prevent="getTracksheet"
                counter="19"
                :rules="[value => !!value || '必要!!請選擇']"
                required
              ></v-text-field>
              <v-container fluid grid-list-md>
                <v-layout row wrap>
                  <v-flex
                    md6
                    xs12
                    v-for="(item, index) in tracksheet"
                    :key="index"
                  >
                    <v-text-field
                      v-model="item.tracksheetNO"
                      label="單號"
                      readonly
                    ></v-text-field>
                    <v-text-field
                      v-model="item.furnaceName"
                      label="爐別"
                      readonly
                    ></v-text-field>
                    <v-text-field
                      v-model="item.twisterNO"
                      label="捻線機號"
                      readonly
                    ></v-text-field>
                    <v-text-field
                      v-model="item.tracksheetTime"
                      label="下機時間"
                      readonly
                    ></v-text-field>
                    <v-text-field
                      v-model="item.productName"
                      label="品種"
                      readonly
                    ></v-text-field>
                    <v-text-field
                      label="品檢T1個數"
                      value="item.icountT1Qty"
                      v-model="item.icountT1Qty"
                    ></v-text-field>
                    <v-text-field
                      label="品檢T2個數"
                      value="item.icountT2Qty"
                      v-model="item.icountT2Qty"
                    ></v-text-field>
                  </v-flex>
                </v-layout>
              </v-container>
            </v-card-text>
          </v-card>
        </v-form>
      </v-dialog>
    </v-layout>
    <confirm-dialog
      :dialog="dialog"
      :dialogTitle="dialogTitle"
      :dialogText="dialogText"
      @onConfirm="onConfirm"
      @onCancel="onCancel"
    ></confirm-dialog>
    <v-snackbar
      v-if="loading === false"
      :top="'top'"
      :right="true"
      :timeout="5000"
      :color="mode"
      v-model="snackbar"
    >
    <div class="text-center">
      {{ notice }}
      <v-btn dark text @click.native="closeSnackbar">Close</v-btn>
    </div>
    </v-snackbar>
  </v-container>
</template>
<script lang="ts">
import Vue from "vue";
import Table from "@/components/Table.vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
import { Component } from "vue-property-decorator";
import { tracksheetModule } from "@/store/modules/tracksheets";
import { icountModule } from "@/store/modules/icounts";
import { appModule } from "@/store/modules/app";
import { buildSearchFilters, getISOClassDate } from "@/utils/app-util";

@Component({
  components: {
    Table,
    ConfirmDialog
  }
})
export default class ICountForm extends Vue {
  private modalTitle = "新增品檢計數(明細)";
  private modalText = "請掃Yarn傳票";
  private addTracksheetModal = false;
  private dialog = false;
  private dialogTitle = "品檢計數(明細)刪除確認";
  private dialogText = "刪除該筆記錄?";
  private classDateMenu = false;
  private errors = [];
  private formValid = false;
  private detailValid = false;
  private title = "";
  private type = "YARN";
  private trackId = "";
  private tracks : string[] = [];
  private isYarnTrackSheetNO = null;
  private icountId = null;
  private icountlineId = null;
  private color = "";
  private selectedICountline: null;
  private query = "";
  private funo = "";

  search = "";
  headers = [
    { text: "序號", left: true, value: "countdown" },
    { text: "傳票單號", value: ".tracksheetNO" },
    { text: "爐別", value: "furnaceName" },
    { text: "品種", left: true, value: "productName" },
    { text: "捻線機號", left: true, value: "twisterNO" },
    { text: "下機時間", left: true, value: "tracksheetTime" },
    { text: "品檢T1個數", value: "icountT1Qty" },
    { text: "品檢T2個數", value: "icountT2Qty" },
    { text: "", value: "actions", sortable: false }
  ];
  searchFilter = { contain: { tracksheetNO: "" } };
  //
  get employees() {
    return icountModule.employees;
  }

  get icount() {
    return icountModule.icount;
  }

  get icountline() {
    return icountModule.icountline;
  }

  get tracksheet() {
    return tracksheetModule.tracksheet;
  }

  get loading() {
    return appModule.loading;
  }

  get mode() {
    return appModule.mode;
  }

  get snackbar() {
    return appModule.snackbar;
  }

  get notice() {
    return appModule.notice;
  }

  save() {
    if (!this.icount.id) {
        icountModule.saveICount(this.icount)
      .then(() => {
        this.saveRoute();
      })
      .catch((err: Error) => {
        console.error("Error:",err.message);
      });
    } else {
      icountModule.saveICount(this.icount)
      .then(() => {
      })
      .catch((err: Error) => {
        console.error("Error:",err.message);
      });
    }
  }

    saveRoute() {
    this.icountId = icountModule.icountId;
    this.$router.push(`icount/${this.icountId}`);
  }

  getICountById() {
    icountModule.getICountById(this.$route.params.id);
  }

  async getTracksheet() {
    buildSearchFilters(this.searchFilter);
    const tracksheetNO = this.searchFilter.contain.tracksheetNO;
    const trimmedTracksheetNO = tracksheetNO.trim();
    if (!trimmedTracksheetNO) {
      appModule.sendErrorNotice("請輸入追蹤單號!");
      appModule.closeNoticeWithDelay(3000);
      return "";
    }
    this.isYarnTrackSheetNO = /^G\d{12}-\d{1}$/.test(trimmedTracksheetNO) || /^G\d{12}-\d{1}-S\d{2}$/.test(trimmedTracksheetNO);
    if (!this.isYarnTrackSheetNO) {
      appModule.sendErrorNotice("無效的追蹤單號!");
      appModule.closeNoticeWithDelay(3000);
      return "";
    }
    if (this.isYarnTrackSheetNO) {
      this.trackId = trimmedTracksheetNO.slice(0, 19).trim();
      this.query = trimmedTracksheetNO.slice(1, 13);
      this.funo = trimmedTracksheetNO.slice(16, 19);
      this.tracks = [this.type,this.query,this.trackId];
      const isDuplicate = await icountModule.getDuplicateICountlineByCode(this.trackId);
      if (isDuplicate) {
        appModule.sendErrorNotice("重複的追蹤單號!");
        appModule.closeNoticeWithDelay(5000);
        return "";
      }
      await tracksheetModule.getTracksheetByCode(this.tracks);
      if (this.tracksheet[0]=== undefined) {
        appModule.sendErrorNotice("查無資料!");
        appModule.closeNoticeWithDelay(5000);
        return "";
      } else {
          if (this.funo) {
            this.tracksheet[0].furnaceName =this.funo;
          }
        return this.tracksheet;
      }
    }
  }

  cancel() {
    this.$router.push({ name: "icounts" });
  }

  remove(item) {
    this.selectedICountline = item;
    this.dialog = true;
  }

  onConfirm() {
    icountModule.deleteICountline(this.selectedICountline);
    this.selectedICountline = null;
    this.getICountById();
    this.dialog = false;
  }

  onCancel() {
    this.selectedICountline = null;
    this.dialog = false;
  }

  addTracksheet() {
    this.addTracksheetModal = true;
    this.isYarnTrackSheetNO = null;
    this.query = "";
    this.searchFilter.contain.tracksheetNO = "";
    this.icountId = this.icount.id;
    tracksheetModule.clearTracksheets();
    this.$nextTick(() => {
      const validDetailRef = this.$refs.validDetail as Vue & { validate: () => boolean };
      if (validDetailRef) {
        validDetailRef.validate();
      }
      this.$nextTick(() => {
        const qrCodeInput = this.$refs.qrCodeInput as HTMLElement;
        if (qrCodeInput) {
          qrCodeInput.focus();
        }
      });
    });
  }

  saveICountline() {
    const ICountId = { icountId: this.icount.id };
    const addTracksheetNO = { tracksheetNO: this.searchFilter.contain.tracksheetNO };
    const addICountline = this.icountline;
    const addTracksheet = this.tracksheet[0];
    const newICountline = {
      ...ICountId,
      ...addICountline,
      ...addTracksheet,
      ...addTracksheetNO,
    };
    icountModule.addICountlineToICount(newICountline);
    this.icountlineId = null;
    this.getICountById();
    this.resetForm();
  }

  resetForm() {
    this.isYarnTrackSheetNO = null;
    this.query = "";
    this.searchFilter.contain.tracksheetNO = "";
    this.funo = "";
    tracksheetModule.clearTracksheets();
    this.$nextTick(() => {
      const qrCodeInput = this.$refs.qrCodeInput as HTMLElement;
      if (qrCodeInput) {
        qrCodeInput.focus();
      }
    });
  }

  cancelAddTracksheet() {
    this.addTracksheetModal = false;
    this.query = "";
    this.searchFilter.contain.tracksheetNO = "";
    icountModule.clearICountline();
    tracksheetModule.clearTracksheets();
  }

  closeSnackbar() {
    appModule.closeNotice();
  }

  get pagination() {
    return icountModule.pagination;
  }

  created() {
    this.getICountById();
    icountModule.getEmployees();
  }

  mounted() {
    window.scrollTo(0, 0);
    if (this.$route.params.id) {
      this.title = "QI品檢計數作業(明細)";
      icountModule.clearICountline();
      icountModule.getICountById(this.$route.params.id);
      this.$nextTick(() => {
      });
    } else {
      this.title = "QI品檢計數作業(新增)";
      this.icount.typeName ="YARN";
      const toDate = getISOClassDate();
      this.icount.classDate = toDate.slice(0, 10);
      this.$nextTick(() => {
        const validDetailRef = this.$refs.validForm as Vue & { validate: () => boolean };
          if (validDetailRef) {
            validDetailRef.validate();
          }
      });
    }
  }
}
</script>
