<template>
  <v-container fluid>
    <v-flex xs12>
      <v-card>
        <v-card-title>
          <span class="title"
            >{{title}} {{ pagination ? "(" + pagination.totalItems + ")" : "" }}
          </span>
          <v-spacer></v-spacer>
          <table-header-buttons
            :add="add"
            :reloadData="reloadData"
          ></table-header-buttons>
        </v-card-title>
        <Table
          v-if="loading === false"
          :headers="headers"
          :items="items"
          :pagination="pagination"
          :setSearch="true"
          :setEdit="true"
          :setRemove="true"
          @edit="edit"
          @remove="remove"
        ></Table>
      </v-card>
    </v-flex>
    <search-panel
      :rightDrawer="rightDrawer"
      @cancelSearch="cancelSearch"
      @searchData="searchWorks"
    >
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="reference"
            label="Reference"
            variant="outlined"
            v-model="searchFilter.contain.reference"
          ></v-text-field>
        </v-flex>
      </v-layout>
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="customer"
            label="Customer"
            variant="outlined"
            v-model="searchFilter.contain.customer"
          ></v-text-field>
        </v-flex>
      </v-layout>
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="minAmount"
            type="number"
            label="Min Amount"
            variant="outlined"
            v-model="searchFilter.greaterThanOrEqual.amount"
          ></v-text-field>
        </v-flex>
      </v-layout>
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="maxAmount"
            type="number"
            label="Max Amount"
            variant="outlined"
            v-model="searchFilter.lessThanOrEqual.amount"
          ></v-text-field>
        </v-flex>
      </v-layout>
    </search-panel>
    <confirm-dialog
      :dialog="dialog"
      :dialogTitle="dialogTitle"
      :dialogText="dialogText"
      @onConfirm="onConfirm"
      @onCancel="onCancel"
    ></confirm-dialog>
    <v-snackbar
      v-if="loading === false"
      location="top end"
      :timeout="5000"
      :color="mode"
      :model-value="snackbar"
      @update:model-value="updateSnackbar"
    >
      {{ notice }}
      <template v-slot:actions>
        <v-btn variant="text" @click="closeSnackbar">Close</v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-facing-decorator'
import { useRouter } from 'vue-router'
import Table from '@/components/Table.vue'
import TableHeaderButtons from '@/components/TableHeaderButtons.vue'
import SearchPanel from '@/components/SearchPanel.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import { debounce } from 'lodash'
import {
  buildSearchFilters,
  buildJsonServerQuery,
  clearSearchFilters
} from '@/utils/app-util'
import { workModule } from '@/store/modules/works'
import { appModule } from '@/store/modules/app'

interface Header {
  text: string
  left?: boolean
  value: string
  sortable?: boolean
}

@Component({
  name: 'WorkList',
  components: {
    Table,
    TableHeaderButtons,
    SearchPanel,
    ConfirmDialog
  }
})
export default class WorkList extends Vue {
  private router = useRouter()
  public dialog = false
  public dialogTitle = "捻線開機刪除確認"
  public dialogText = "確定要刪除此記錄?"
  public showSearchPanel = false
  public right = true
  public search = ""
  public headers: Header[] = [
    { text: "單號", left: true, value: "id" },
    { text: "日期", value: "workDate" },
    { text: "勤別", value: "shiftName" },
    { text: "組別", value: "groupName" },
    { text: "開機台數", value: "quantity" },
    { text: "", value: "actions", sortable: false }
  ]

  private searchFilter = {
    contain: {
      reference: "",
      customer: ""
    },
    greaterThanOrEqual: {
      amount: 0
    },
    lessThanOrEqual: {
      amount: 0
    }
  }

  private title = ""
  private query = ""
  private color = ""
  private quickSearchFilter = ""
  private itemId = -1
  private dateFormat = "DD-MM-YYYY"

  edit(item: any) {
    this.router.push(`/work/${item.id}`)
  }

  add() {
    this.router.push("/works/new")
  }

  remove(item: any) {
    this.itemId = item.id
    this.dialog = true
  }

  async onConfirm() {
    await workModule.deleteWork(this.itemId)
    this.dialog = false
  }

  onCancel() {
    this.itemId = -1
    this.dialog = false
  }

  searchWorks() {
    this.showSearchPanel = !this.showSearchPanel
    buildSearchFilters(this.searchFilter)
    this.query = buildJsonServerQuery(this.searchFilter)
    this.quickSearch = ""
    workModule.searchWorks(this.query)
  }

  clearSearchFilters() {
    this.showSearchPanel = !this.showSearchPanel
    clearSearchFilters(this.searchFilter)
    workModule.getAllWorks()
  }

  reloadData() {
    this.query = ""
    workModule.getAllWorks()
  }

  updateSearchPanel() {
    this.rightDrawer = !this.rightDrawer
  }

  cancelSearch() {
    this.showSearchPanel = false
  }

  closeSnackbar() {
    appModule.closeNotice()
  }

  updateSnackbar(value: boolean) {
    if (!value) {
      appModule.closeNotice()
    }
  }

  quickSearchWorks = debounce(function(this: WorkList) {
    workModule.quickSearch(this.headers, this.quickSearchFilter)
  }, 500)

  get items() {
    return workModule.items
  }

  get pagination() {
    return workModule.pagination
  }

  get loading() {
    return appModule.loading
  }

  get setSearch() {
    return true
  }

  get mode() {
    return appModule.mode
  }

  get snackbar() {
    return appModule.snackbar
  }

  get notice() {
    return appModule.notice
  }

  get rightDrawer() {
    return this.showSearchPanel
  }

  set rightDrawer(value: boolean) {
    this.showSearchPanel = value
  }

  get quickSearch() {
    return this.quickSearchFilter
  }

  set quickSearch(val: string) {
    this.quickSearchFilter = val
    if (this.quickSearchFilter) {
      this.quickSearchWorks()
    }
  }

  created() {
    workModule.getAllWorks()
  }

  mounted() {
    this.$nextTick(() => {
      this.title = "捻線開機"
    })
  }
}
</script>
