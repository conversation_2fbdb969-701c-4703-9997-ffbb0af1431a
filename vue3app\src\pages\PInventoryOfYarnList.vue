<template>
  <v-container fluid>
    <v-card>
      <v-card-title>
        <span class="title">Yarn盤點 {{ pagination.totalItems ? "(" + pagination.totalItems + ")" : "" }}</span>
        <v-spacer></v-spacer>
        <v-btn
          elevation="4"
          color="green"
          size="small"
          icon
          class="mr-2"
          @click="add"
        >
          <v-icon>mdi-plus</v-icon>
        </v-btn>
        <v-btn
          elevation="4"
          color="brown-lighten-1"
          size="small"
          icon
          class="mr-2"
          @click="reloadData"
        >
          <v-icon>mdi-refresh</v-icon>
        </v-btn>
      </v-card-title>

      <!-- Loading indicator -->
      <v-card-text v-if="loading">
        <v-progress-linear indeterminate></v-progress-linear>
        <p class="text-center mt-2">載入中...</p>
      </v-card-text>

      <!-- Results table -->
      <v-card-text v-if="!loading && items.length > 0">
        <v-data-table-server
          :headers="headers"
          :items="items"
          :items-length="pagination.totalItems"
          :items-per-page="pagination.limit"
          :page="pagination.page"
          @update:options="updateOptions"
          class="elevation-1 colored-pagination"
        >
          <template v-slot:item.actions="{ item }">
            <v-btn
              :elevation="4"
              icon
              size="x-small"
              color="purple"
              class="mr-2"
              @click="edit(item)"
            >
              <v-icon>mdi-pencil</v-icon>
            </v-btn>
            <v-btn
              :elevation="4"
              icon
              size="x-small"
              color="red"
              @click="remove(item)"
            >
              <v-icon>mdi-delete</v-icon>
            </v-btn>
          </template>
        </v-data-table-server>
      </v-card-text>

      <!-- No results message -->
      <v-card-text v-if="!loading && items.length === 0">
        <v-alert type="info" variant="tonal">
          目前沒有 Yarn 盤點記錄
        </v-alert>
      </v-card-text>
    </v-card>

    <!-- Confirm Dialog -->
    <v-dialog v-model="dialog" max-width="500px">
      <v-card>
        <v-card-title class="text-h5">{{ dialogTitle }}</v-card-title>
        <v-card-text>{{ dialogText }}</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="blue darken-1" variant="text" @click="onCancel">取消</v-btn>
          <v-btn color="blue darken-1" variant="text" @click="onConfirm">確認</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Snackbar -->
    <v-snackbar
      v-model="snackbar.show"
      location="top end"
      :timeout="5000"
      :color="snackbar.color"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn variant="text" @click="snackbar.show = false">Close</v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { usePInventoriesStore } from '@/stores/pinventories'
import { useAppStore } from '@/stores/app'

interface Header {
  title: string
  key: string
  align?: string
  sortable?: boolean
}

interface PInventoryItem {
  id: number
  classDate: string
  shiftName: string
  employName: string
  groupName: string
  quantity: number
  [key: string]: any
}

// Stores
const pinventoriesStore = usePInventoriesStore()
const appStore = useAppStore()

// Reactive state
const router = useRouter()
const dialog = ref(false)
const itemId = ref(-1)

const snackbar = ref({
  show: false,
  message: "",
  color: "error"
})

// Pagination state
const pagination = ref({
  page: 1,
  limit: 10,
  totalItems: 0,
  totalPages: 0,
  hasNextPage: false,
  hasPrevPage: false
})

// Computed properties
const loading = computed(() => pinventoriesStore.isLoading)
const items = computed(() => pinventoriesStore.items as PInventoryItem[])

// Headers for the data table
const headers: Header[] = [
  { title: "單號", key: "id", align: "start" },
  { title: "日期", key: "classDate", align: "start" },
  { title: "勤別", key: "shiftName", align: "start" },
  { title: "人員", key: "employName", align: "start" },
  { title: "組別", key: "groupName", align: "start" },
  { title: "小計", key: "quantity", align: "start" },
  { title: "操作", key: "actions", sortable: false }
]

const dialogTitle = ref("Yarn盤點(主檔)刪除確認")
const dialogText = ref("刪除該筆記錄?")

// Methods
const showError = (message: string) => {
  snackbar.value = {
    show: true,
    message,
    color: "error"
  }
}

const showSuccess = (message: string) => {
  snackbar.value = {
    show: true,
    message,
    color: "success"
  }
}

const edit = (item: PInventoryItem) => {
  router.push(`/pinventoryofyarn/${item.id}`)
}

const add = () => {
  router.push('/pinventoriesofyarn/new')
}

const remove = async (item: PInventoryItem) => {
  itemId.value = item.id

  // 先檢查是否有明細資料
  try {
    await pinventoriesStore.getPInventoryById(item.id)
    const currentPInventory = pinventoriesStore.currentPInventory
    if (currentPInventory &&
        currentPInventory.pinventorylines &&
        currentPInventory.pinventorylines.length > 0) {
      // 有明細資料，直接提示無法刪除
      showError(`無法刪除主檔：此Yarn盤點記錄(單號:${item.id})仍有 ${currentPInventory.pinventorylines.length} 筆明細資料，請先至明細頁面刪除所有明細記錄後再刪除主檔`)
      return
    }
  } catch (error) {
    console.warn('檢查明細資料時發生錯誤:', error)
    // 如果檢查失敗，仍然允許用戶嘗試刪除
  }

  // 沒有明細資料或檢查失敗，顯示確認對話框
  dialog.value = true
}

const updateOptions = (options: any) => {
  // 避免無限循環，只有當頁數或每頁項目數真的改變時才重新載入
  if (pagination.value.page !== options.page || pagination.value.limit !== options.itemsPerPage) {
    pagination.value.page = options.page
    pagination.value.limit = options.itemsPerPage
    loadYarnInventories(options.page, options.itemsPerPage)
  }
}

const onConfirm = async () => {
  try {
    await pinventoriesStore.deletePInventory(itemId.value)
    dialog.value = false
    showSuccess("刪除成功")
    appStore.sendSuccessNotice("刪除成功")
    await loadYarnInventories()
  } catch (error: any) {
    console.error('Delete error:', error)
    dialog.value = false

    // 檢查錯誤類型並提供相應的訊息
    if (error?.response?.status === 400 || error?.response?.status === 409) {
      // 明確指出是明細資料的問題
      showError("無法刪除主檔：此Yarn盤點記錄仍有明細資料存在，請先至明細頁面刪除所有明細記錄後再刪除主檔")
    } else if (error?.response?.status === 404) {
      showError("刪除失敗：Yarn盤點記錄不存在")
    } else if (error?.response?.status === 500) {
      showError("刪除失敗：伺服器錯誤，請聯絡系統管理員")
    } else if (error?.message?.includes('timeout')) {
      showError("刪除失敗：請求超時，請檢查網路連線")
    } else {
      // 檢查後端是否有具體的錯誤訊息
      const backendMessage = error?.response?.data?.err || error?.response?.data?.message
      if (backendMessage && typeof backendMessage === 'string') {
        showError(`刪除失敗：${backendMessage}`)
      } else {
        showError(`刪除失敗：${error?.message || '未知錯誤'}`)
      }
    }
    appStore.sendErrorNotice("刪除失敗，請稍後再試")
  }
}

const onCancel = () => {
  itemId.value = -1
  dialog.value = false
}

const loadYarnInventories = async (page: number = pagination.value.page, limit: number = pagination.value.limit || 10) => {
  try {
    await pinventoriesStore.getAllPInventoriesByType('YARN')

    // 從 store 獲取數據
    const storeItems = pinventoriesStore.items as PInventoryItem[]

    // 手動處理分頁
    pagination.value.totalItems = storeItems.length
    pagination.value.totalPages = Math.ceil(storeItems.length / limit)
    pagination.value.hasNextPage = page < pagination.value.totalPages
    pagination.value.hasPrevPage = page > 1

    // 更新 store 的分頁信息
    pinventoriesStore.setPagination({
      ...pagination.value,
      page,
      limit
    })

    if (storeItems.length === 0) {
      showError("目前沒有 Yarn 盤點記錄")
    }
  } catch (error) {
    console.error('PInventoryOfYarnList API Error:', error)
    showError("載入資料失敗，請稍後再試")
    appStore.sendErrorNotice("載入資料失敗，請稍後再試")
  }
}

const reloadData = () => {
  loadYarnInventories()
}

// Lifecycle
onMounted(() => {
  loadYarnInventories()
})
</script>
