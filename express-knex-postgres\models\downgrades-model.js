const db = require("../config/dbConfig.js");
require("dotenv").config();
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET ALL DOWNGRADES BY TYPE
const findByType = type => {
  return db("aits_downgrades")
    .select({
      id: "aits_downgrades.id",
      classDate: "aits_downgrades.classdate",
      shiftName: "aits_downgrades.shiftname",
      employId: "aits_downgrades.employid",
      groupName: "aits_downgrades.groupname",
      typeName: "aits_downgrades.typename",
      created: "aits_downgrades.created",
      updated: "aits_downgrades.updated",
      quantity: db.raw("count(aits_downgradelines.downgradeid)")
    })
    .leftJoin("aits_downgradelines", "aits_downgrades.id", "aits_downgradelines.downgradeid")
    .where("aits_downgrades.typename", type)
    .where("aits_downgrades.classdate", ">=", process.env.DUE_TO_QUERY_DATE) 
    .groupBy("aits_downgrades.id", "aits_downgrades.classdate", "aits_downgrades.shiftname", "aits_downgrades.employid", "aits_downgrades.groupname", "aits_downgrades.typename")
    .orderBy("aits_downgrades.id","desc")
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
  });
};

// GET SPECIFIC DOWNGRADE BY ID
const findById = id => {
  return db("aits_downgrades")    
    .select({
      id: "aits_downgrades.id",
      classDate: "aits_downgrades.classdate",
      shiftName: "aits_downgrades.shiftname",
      employId: "aits_downgrades.employid",
      groupName: "aits_downgrades.groupname",
      typeName: "aits_downgrades.typename",
      created: "aits_downgrades.created",
      updated: "aits_downgrades.updated"
    })
    .where("aits_downgrades.id", id)
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
  });
};

// ADD A DOWNGRADE
const addDowngrade = downgrade => {
  return db.transaction(trx => {
    return db("aits_downgrades")
      .insert({
        classdate: downgrade.classDate,
        shiftname: downgrade.shiftName,
        employid: downgrade.employId,
        groupname: downgrade.groupName,
        typename: downgrade.typeName,
        created: db.fn.now()
      }, "id")
      .then(trx.commit)
      .catch(trx.rollback);
    })
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

// UPDATE DOWNGRADE
const updateDowngrade = (id, downgrade) => {
  let origin,result;
  return db.transaction(trx => {
    return db("aits_downgrades")
      .where("aits_downgrades.id", id)
      .then(old_downgrade => {
        origin = old_downgrade;
        if (old_downgrade) {
          return db("aits_downgrades")
          .where("aits_downgrades.id", id)
          .update({
            classdate: downgrade.classDate,
            shiftname: downgrade.shiftName,
            employid: downgrade.employId,
            groupname: downgrade.groupName,
            typename: downgrade.typeName,
            updated: db.fn.now()
          })
          .then(trx.commit)
          .catch(trx.rollback);
        }
    })
  })
  .then(()=>{
    return db("aits_downgrades")
    .where("aits_downgrades.id", id)
    .then(new_downgrade => { 
      result = new_downgrade;
      infoLogger.info(`update downgrade content: ${JSON.stringify({origin,result})}`)
    })
  }) 
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

// REMOVE DOWNGRADE
const removeDowngrade = (id) => {
  let result;
  return db.transaction(trx => { 
    return db("aits_downgrades")
      .where("aits_downgrades.id", id) 
      .then(downgrade => {   
        result = downgrade;      
        if (downgrade) { 
          return db("aits_downgrades") 
            .where("aits_downgrades.id", id) 
            .del() 
            .then(trx.commit) 
            .catch(trx.rollback); 
        } 
      }) 
    }) 
  .then(() => { 
    infoLogger.info(`remove downgrade content: ${JSON.stringify(result)}`)
  })
  .catch((err) => { 
    console.error(err); 
    errorLogger.error(err); 
    throw err; 
  }); 
}; 

module.exports = {
  findByType,
  findById,
  addDowngrade,
  updateDowngrade,
  removeDowngrade
};
