import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { User, UserInfo } from '../types'
import { login } from '../utils/backend-api'

export const useUserStore = defineStore('user', () => {
  // State
  const token = ref<string>(localStorage.getItem('token') || '')
  const user = ref<User>({} as User)
  const userInfo = ref<UserInfo>({} as UserInfo)
  const status = ref<string>('')

  // Getters
  const isLoggedIn = computed(() => !!token.value)
  const authStatus = computed(() => status.value)
  const firstname = computed(() => userInfo.value?.firstname || '')
  const lastname = computed(() => userInfo.value?.lastname || '')

  // Actions
  async function loginUser(userData: User) {
    try {
      const resp = await login('users/login', userData)
      const tokenValue = resp.data.token
      localStorage.setItem('token', tokenValue)
      token.value = tokenValue
      user.value = userData
      userInfo.value = resp.data.user
      return resp
    } catch (err) {
      localStorage.removeItem('token')
      throw err
    }
  }

  function logout() {
    localStorage.removeItem('token')
    token.value = ''
    user.value = {} as User
    userInfo.value = {} as UserInfo
  }

  function setUserInfo(newUserInfo: UserInfo) {
    userInfo.value = newUserInfo
  }

  return {
    // State
    token,
    user,
    userInfo,
    status,
    // Getters
    isLoggedIn,
    authStatus,
    firstname,
    lastname,
    // Actions
    login: loginUser,
    logout,
    setUserInfo
  }
})

// Note: For components, use useUserStore() directly instead of these exports
