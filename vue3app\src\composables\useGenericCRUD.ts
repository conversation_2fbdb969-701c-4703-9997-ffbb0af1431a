import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getData, postData, putData, deleteData } from '@/utils/backend-api'

export interface CRUDOptions<T> {
  endpoint: string
  listRoute: string
  defaultData: T
  idField?: keyof T
  onSaveSuccess?: (data: T) => void
  onSaveError?: (error: any) => void
  onLoadSuccess?: (data: T) => void
  onLoadError?: (error: any) => void
  onDeleteSuccess?: (id: any) => void
  onDeleteError?: (error: any) => void
}

export interface CRUDState<T> {
  loading: boolean
  saving: boolean
  deleting: boolean
  formValid: boolean
  formData: T
  originalData: T
}

export function useGenericCRUD<T extends Record<string, any>>(options: CRUDOptions<T>) {
  const router = useRouter()
  const route = useRoute()

  const {
    endpoint,
    listRoute,
    defaultData,
    idField = 'id' as keyof T,
    onSaveSuccess,
    onSaveError,
    onLoadSuccess,
    onLoadError,
    onDeleteSuccess,
    onDeleteError
  } = options

  // 狀態管理
  const state = ref<CRUDState<T>>({
    loading: false,
    saving: false,
    deleting: false,
    formValid: false,
    formData: { ...defaultData },
    originalData: { ...defaultData }
  })

  // 通知訊息
  const snackbar = ref({
    show: false,
    message: '',
    color: 'success'
  })

  // 計算屬性
  const isEditMode = computed(() => !!route.params.id)
  const formTitle = computed(() => isEditMode.value ? '編輯' : '新增')
  const isDirty = computed(() => 
    JSON.stringify(state.value.formData) !== JSON.stringify(state.value.originalData)
  )
  const canSave = computed(() => 
    state.value.formValid && !state.value.saving && isDirty.value
  )

  // 通用方法
  const showMessage = (message: string, color: string = 'success') => {
    snackbar.value = {
      show: true,
      message,
      color
    }
  }

  const setLoading = (loading: boolean) => {
    state.value.loading = loading
  }

  const setSaving = (saving: boolean) => {
    state.value.saving = saving
  }

  const setDeleting = (deleting: boolean) => {
    state.value.deleting = deleting
  }

  // CRUD 操作
  const loadData = async (id?: string | number) => {
    const targetId = id || route.params.id
    if (!targetId) return

    try {
      setLoading(true)
      const response = await getData(`${endpoint}/${targetId}`)
      
      if (response.data) {
        // 處理不同的響應格式
        const data = Array.isArray(response.data) ? response.data[0] : response.data
        state.value.formData = { ...data }
        state.value.originalData = { ...data }
        onLoadSuccess?.(data)
        showMessage('數據載入成功', 'success')
      }
    } catch (error) {
      console.error('載入數據失敗:', error)
      showMessage('載入數據失敗', 'error')
      onLoadError?.(error)
    } finally {
      setLoading(false)
    }
  }

  const saveData = async () => {
    try {
      setSaving(true)
      let response

      if (isEditMode.value) {
        // 更新現有記錄
        const id = state.value.formData[idField]
        response = await putData(`${endpoint}/${id}`, state.value.formData)
        showMessage('更新成功！', 'success')
      } else {
        // 創建新記錄
        response = await postData(endpoint, state.value.formData)
        showMessage('儲存成功！', 'success')
        
        // 如果是新增模式，跳轉到編輯模式
        if (response.data && response.data[idField]) {
          router.push(`${route.path.replace('/new', '')}/${response.data[idField]}`)
        }
      }

      if (response.data) {
        state.value.formData = { ...response.data }
        state.value.originalData = { ...response.data }
        onSaveSuccess?.(response.data)
      }

      return response.data
    } catch (error) {
      console.error('保存失敗:', error)
      showMessage('操作失敗，請重試', 'error')
      onSaveError?.(error)
      throw error
    } finally {
      setSaving(false)
    }
  }

  const deleteData = async (id?: any) => {
    const targetId = id || state.value.formData[idField]
    if (!targetId) return

    try {
      setDeleting(true)
      await deleteData(`${endpoint}/${targetId}`)
      showMessage('刪除成功', 'success')
      onDeleteSuccess?.(targetId)
      
      // 刪除後返回列表頁
      router.push(listRoute)
    } catch (error) {
      console.error('刪除失敗:', error)
      showMessage('刪除失敗', 'error')
      onDeleteError?.(error)
    } finally {
      setDeleting(false)
    }
  }

  const resetForm = () => {
    state.value.formData = { ...defaultData }
    state.value.originalData = { ...defaultData }
    showMessage('表單已重設', 'info')
  }

  const goBack = () => {
    router.push(listRoute)
  }

  // 表單驗證
  const setFormValid = (valid: boolean) => {
    state.value.formValid = valid
  }

  const updateFormData = (data: Partial<T>) => {
    state.value.formData = { ...state.value.formData, ...data }
  }

  // 初始化
  const initialize = async () => {
    if (isEditMode.value) {
      await loadData()
    } else {
      resetForm()
    }
  }

  // 離開前檢查
  const beforeLeave = (): boolean => {
    if (isDirty.value) {
      return confirm('您有未保存的更改，確定要離開嗎？')
    }
    return true
  }

  return {
    // 狀態
    state: computed(() => state.value),
    snackbar,
    
    // 計算屬性
    isEditMode,
    formTitle,
    isDirty,
    canSave,
    
    // 數據
    formData: computed({
      get: () => state.value.formData,
      set: (value: T) => { state.value.formData = value }
    }),
    
    // 方法
    showMessage,
    setLoading,
    setSaving,
    setDeleting,
    loadData,
    saveData,
    deleteData,
    resetForm,
    goBack,
    setFormValid,
    updateFormData,
    initialize,
    beforeLeave
  }
}
