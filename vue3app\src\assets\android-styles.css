/* Android-specific styles for optimal mobile experience */

/* Base Android optimizations */
* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Allow text selection for input fields and content areas */
input, textarea, [contenteditable], .selectable-text {
  -webkit-user-select: text;
  -khtml-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Android-specific viewport and scrolling */
html, body {
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Touch-friendly button sizing */
.v-btn {
  min-height: 44px !important;
  min-width: 44px !important;
  touch-action: manipulation;
}

/* Enhanced touch targets for small buttons */
.v-btn--size-small {
  min-height: 40px !important;
  min-width: 40px !important;
}

.v-btn--size-x-small {
  min-height: 36px !important;
  min-width: 36px !important;
}

/* Mobile-optimized navigation drawer */
@media (max-width: 959px) {
  .v-navigation-drawer {
    position: fixed !important;
    z-index: 1006 !important;
  }
  
  .v-navigation-drawer--temporary {
    box-shadow: 0 8px 10px -5px rgba(0, 0, 0, 0.2),
                0 16px 24px 2px rgba(0, 0, 0, 0.14),
                0 6px 30px 5px rgba(0, 0, 0, 0.12) !important;
  }
}

/* App bar optimizations for Android */
.v-app-bar {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

@media (max-width: 599px) {
  .v-app-bar {
    height: 56px !important;
  }
  
  .v-app-bar .v-toolbar__content {
    padding: 0 12px !important;
  }
}

/* Form field optimizations for mobile input */
.v-text-field .v-field__input {
  min-height: 48px !important;
  font-size: 16px !important; /* Prevents zoom on iOS */
}

.v-select .v-field__input {
  min-height: 48px !important;
}

/* Data table mobile optimizations */
@media (max-width: 959px) {
  .v-data-table {
    font-size: 14px !important;
  }
  
  .v-data-table__wrapper {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }
  
  .v-data-table .v-data-table__td {
    padding: 8px 12px !important;
    white-space: nowrap !important;
  }
  
  .v-data-table .v-data-table__th {
    padding: 8px 12px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
  }
}

/* Card optimizations for mobile */
@media (max-width: 599px) {
  .v-card {
    margin: 8px !important;
    border-radius: 12px !important;
  }
  
  .v-card-title {
    font-size: 18px !important;
    line-height: 1.3 !important;
    padding: 16px 16px 8px !important;
  }
  
  .v-card-text {
    padding: 8px 16px 16px !important;
  }
}

/* List item optimizations for touch */
.v-list-item {
  min-height: 48px !important;
  padding: 8px 16px !important;
}

.v-list-item--density-comfortable {
  min-height: 52px !important;
}

.v-list-item--density-compact {
  min-height: 44px !important;
}

/* Dialog optimizations for mobile */
@media (max-width: 599px) {
  .v-dialog {
    margin: 16px !important;
    max-height: calc(100vh - 32px) !important;
  }
  
  .v-dialog .v-card {
    margin: 0 !important;
    max-height: 100% !important;
    overflow-y: auto !important;
  }
}

/* Snackbar positioning for mobile */
@media (max-width: 599px) {
  .v-snackbar {
    margin: 8px !important;
    border-radius: 8px !important;
  }
  
  .v-snackbar--bottom {
    bottom: 16px !important;
  }
}

/* Container spacing optimizations */
@media (max-width: 599px) {
  .v-container {
    padding: 8px !important;
  }
  
  .v-container--fluid {
    padding: 0 !important;
  }
}

/* Row and column spacing for mobile */
@media (max-width: 599px) {
  .v-row {
    margin: -4px !important;
  }
  
  .v-col {
    padding: 4px !important;
  }
}

/* Fab button positioning for mobile */
.v-fab {
  position: fixed !important;
  bottom: 16px !important;
  right: 16px !important;
  z-index: 1005 !important;
}

/* Loading and progress indicators */
.v-progress-linear {
  border-radius: 2px !important;
}

.v-progress-circular {
  margin: 16px !important;
}

/* Alert optimizations for mobile */
@media (max-width: 599px) {
  .v-alert {
    margin: 8px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
  }
}

/* Chip optimizations for touch */
.v-chip {
  min-height: 32px !important;
  font-size: 14px !important;
}

/* Badge positioning */
.v-badge {
  touch-action: manipulation;
}

/* Menu optimizations for mobile */
@media (max-width: 599px) {
  .v-menu .v-list {
    max-height: 50vh !important;
    overflow-y: auto !important;
  }
}

/* Tooltip adjustments for touch devices */
@media (hover: none) {
  .v-tooltip {
    display: none !important;
  }
}

/* Focus styles for accessibility */
.v-btn:focus-visible,
.v-text-field:focus-within,
.v-select:focus-within {
  outline: 2px solid #1976D2 !important;
  outline-offset: 2px !important;
}

/* Safe area adjustments for devices with notches */
@supports (padding: max(0px)) {
  .v-app-bar {
    padding-left: max(12px, env(safe-area-inset-left)) !important;
    padding-right: max(12px, env(safe-area-inset-right)) !important;
  }
  
  .v-navigation-drawer {
    padding-top: max(0px, env(safe-area-inset-top)) !important;
  }
  
  .v-main {
    padding-bottom: max(0px, env(safe-area-inset-bottom)) !important;
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 500px) {
  .v-app-bar {
    height: 48px !important;
  }
  
  .v-navigation-drawer {
    width: 240px !important;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .v-icon {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
