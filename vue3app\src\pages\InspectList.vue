<template>
  <v-container fluid>
    <v-card>
      <v-card-title>
        <span class="title">QI品檢異常登錄</span>
        <v-spacer></v-spacer>
        <v-btn
          :elevation="4"
          icon
          size="small"
          color="blue"
          class="mr-2"
          @click="add"
        >
          <v-icon>mdi-plus</v-icon>
        </v-btn>
        <v-btn
          :elevation="4"
          icon
          size="small"
          color="grey"
          class="mr-2"
          @click="reloadData"
        >
          <v-icon>mdi-refresh</v-icon>
        </v-btn>
      </v-card-title>

      <!-- Loading indicator -->
      <v-card-text v-if="loading">
        <v-progress-linear indeterminate></v-progress-linear>
        <p class="text-center mt-2">載入中...</p>
      </v-card-text>

      <!-- Results table -->
      <v-card-text v-if="!loading && items.length > 0">
        <v-data-table-server
          :headers="headers"
          :items="items"
          :items-length="pagination.totalItems"
          :items-per-page="pagination.limit"
          :page="pagination.page"
          @update:options="updateOptions"
          class="elevation-1 colored-pagination"
        >
          <template v-slot:item.actions="{ item }">
            <v-btn
              :elevation="4"
              icon
              size="x-small"
              color="purple"
              class="mr-2"
              @click="edit(item)"
            >
              <v-icon>mdi-pencil</v-icon>
            </v-btn>
            <v-btn
              :elevation="4"
              icon
              size="x-small"
              color="red"
              @click="remove(item)"
            >
              <v-icon>mdi-delete</v-icon>
            </v-btn>
          </template>
        </v-data-table-server>
      </v-card-text>

      <!-- No results message -->
      <v-card-text v-if="!loading && items.length === 0">
        <v-alert type="info" variant="tonal">
          沒有找到相關資料
        </v-alert>
      </v-card-text>
    </v-card>

    <!-- Delete confirmation dialog -->
    <v-dialog v-model="dialog" max-width="400">
      <v-card>
        <v-card-title>QI品檢異常(主檔)刪除確認</v-card-title>
        <v-card-text>刪除該筆記錄?</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey" variant="text" @click="onCancel">取消</v-btn>
          <v-btn color="error" variant="text" @click="onConfirm">確認</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Snackbar for notifications -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="-1"
      location="top end"
      persistent
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn variant="text" @click="snackbar.show = false">Close</v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useInspectsStore } from '@/stores/inspects'

interface Header {
  title: string
  key: string
  align?: 'start' | 'end' | 'center'
  sortable?: boolean
}

interface InspectItem {
  id: number
  classDate: string
  shiftName: string
  employName: string
  groupName: string
  quantity: number
  [key: string]: any
}

// Reactive state
const router = useRouter()
const inspectsStore = useInspectsStore()
const loading = ref(false)
const items = ref<InspectItem[]>([])
const dialog = ref(false)
const itemId = ref(-1)

// 分頁狀態
const pagination = ref({
  page: 1,
  limit: 10,
  totalItems: 0,
  totalPages: 0,
  hasNextPage: false,
  hasPrevPage: false
})

const snackbar = ref({
  show: false,
  message: "",
  color: "error"
})

// Headers for the data table
const headers: Header[] = [
  { title: "單號", key: "id", align: "start" },
  { title: "日期", key: "classDate", align: "start" },
  { title: "勤別", key: "shiftName", align: "start" },
  { title: "人員", key: "employName", align: "start" },
  { title: "組別", key: "groupName", align: "start" },
  { title: "小計", key: "quantity", align: "start" },
  { title: "操作", key: "actions", sortable: false }
]

// Methods
const showError = (message: string) => {
  snackbar.value = {
    show: true,
    message,
    color: "error"
  }
}

const showSuccess = (message: string) => {
  snackbar.value = {
    show: true,
    message,
    color: "success"
  }
}

const edit = (item: InspectItem) => {
  router.push(`/inspect/${item.id}`)
}

const add = () => {
  // 在導航到新增頁面前，先重置 store 的狀態
  inspectsStore.resetState()
  router.push("/newinspect")
}

const remove = async (item: InspectItem) => {
  itemId.value = item.id

  // 先檢查是否有明細資料
  try {
    await inspectsStore.getInspectById(item.id.toString())
    if (inspectsStore.currentInspectlines &&
        inspectsStore.currentInspectlines.length > 0) {
      // 有明細資料，直接提示無法刪除
      showError(`無法刪除主檔：此檢驗記錄(單號:${item.id})仍有 ${inspectsStore.currentInspectlines.length} 筆明細資料，請先至明細頁面刪除所有明細記錄後再刪除主檔`)
      return
    }
  } catch (error) {
    console.warn('檢查明細資料時發生錯誤:', error)
    // 如果檢查失敗，仍然允許用戶嘗試刪除
  }

  // 沒有明細資料或檢查失敗，顯示確認對話框
  dialog.value = true
}

const onConfirm = async () => {
  try {
    await inspectsStore.deleteInspect(itemId.value)
    dialog.value = false
    showSuccess("刪除成功")
    await loadInspects()
  } catch (error: any) {
    console.error('Delete error:', error)
    dialog.value = false

    // 檢查錯誤類型並提供相應的訊息
    if (error?.response?.status === 400 || error?.response?.status === 409) {
      // 明確指出是明細資料的問題
      showError("無法刪除主檔：此檢驗記錄仍有明細資料存在，請先至明細頁面刪除所有明細記錄後再刪除主檔")
    } else if (error?.response?.status === 404) {
      showError("刪除失敗：檢驗記錄不存在")
    } else if (error?.response?.status === 500) {
      showError("刪除失敗：伺服器錯誤，請聯絡系統管理員")
    } else if (error?.message?.includes('timeout')) {
      showError("刪除失敗：請求超時，請檢查網路連線")
    } else {
      // 檢查後端是否有具體的錯誤訊息
      const backendMessage = error?.response?.data?.err || error?.response?.data?.message
      if (backendMessage && typeof backendMessage === 'string') {
        showError(`刪除失敗：${backendMessage}`)
      } else {
        showError(`刪除失敗：${error?.message || '未知錯誤'}`)
      }
    }
  }
}

const onCancel = () => {
  itemId.value = -1
  dialog.value = false
}

const loadInspects = async (page: number = pagination.value.page, limit: number = pagination.value.limit) => {
  try {
    await inspectsStore.loadInspects(page, limit)
    // 確保 items 有正確的類型
    items.value = inspectsStore.items.map((item: any) => ({
      id: item.id,
      classDate: item.classDate || '',
      shiftName: item.shiftName || '',
      employName: item.employName || '',
      groupName: item.groupName || '',
      quantity: item.quantity || 0,
      ...item
    })) as InspectItem[]
    pagination.value = {
      ...pagination.value,
      ...inspectsStore.pagination
    }
  } catch (error) {
    console.error('載入檢驗列表失敗:', error)
    showError("載入資料失敗，請稍後再試")
    items.value = []
    pagination.value.totalItems = 0
  }
}

// 處理分頁變更
const updateOptions = (options: any) => {
  console.log('分頁選項變更:', options)

  // 避免無限循環，只有當頁數或每頁項目數真的改變時才重新載入
  if (pagination.value.page !== options.page || pagination.value.limit !== options.itemsPerPage) {
    pagination.value.page = options.page
    pagination.value.limit = options.itemsPerPage
    loadInspects(options.page, options.itemsPerPage)
  }
}

const reloadData = () => {
  loadInspects()
}

// 分頁導航方法
const goToPage = (page: number) => {
  if (page >= 1 && page <= Math.ceil(pagination.value.totalItems / pagination.value.limit)) {
    pagination.value.page = page
    loadInspects(page, pagination.value.limit)
  }
}

// Lifecycle
onMounted(() => {
  loadInspects()
})
</script>

<style scoped>
/* 自定義分頁按鈕顏色 */
:deep(.colored-pagination .v-data-table-footer .v-btn) {
  color: rgb(33, 150, 243) !important; /* 藍色 */
}

:deep(.colored-pagination .v-data-table-footer .v-btn:hover) {
  background-color: rgba(33, 150, 243, 0.1) !important;
}

:deep(.colored-pagination .v-data-table-footer .v-btn.v-btn--active) {
  background-color: rgb(33, 150, 243) !important;
  color: white !important;
}

:deep(.colored-pagination .v-data-table-footer .v-btn.v-btn--disabled) {
  color: rgba(0, 0, 0, 0.26) !important;
}

/* 分頁選擇器樣式 */
:deep(.colored-pagination .v-data-table-footer .v-select) {
  color: rgb(33, 150, 243) !important;
}

:deep(.colored-pagination .v-data-table-footer .v-select .v-field__input) {
  color: rgb(33, 150, 243) !important;
}
</style>
