{"name": "express-knex-postgres", "version": "1.0.0", "description": "Boilerplate code for quick setup for CRUD applications using express/knex/postgres", "main": "index.js", "scripts": {"test": "DB_ENV=testing jest --watch --verbose", "server": "nodemon index.js", "start": "node index.js"}, "jest": {"testEnvironment": "node"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^8.2.0", "express": "^4.17.1", "jsonwebtoken": "^9.0.2", "knex": "^2.5.1", "moment": "^2.29.1", "morgan": "^1.10.0", "pg": "^8.5.1", "winston": "^3.10.0"}, "devDependencies": {"cross-env": "^7.0.3", "jest": "^26.6.3", "nodemon": "^3.0.1", "supertest": "^6.1.3"}}