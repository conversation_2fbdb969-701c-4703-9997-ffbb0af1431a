<template>
  <v-container fluid>
    <v-card>
      <v-card-title>
        <span class="title">EM即時生產查詢</span>
        <v-spacer></v-spacer>
        <v-btn
          elevation="4"
          color="brown-lighten-1"
          size="small"
          icon
          class="mr-2"
          @click="clearData"
        >
          <v-icon>mdi-refresh</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-text-field
          ref="searchInput"
          v-model="searchFilter.contain.trackNO"
          append-icon="mdi-magnify"
          label="傳票QRCode 或 Cap QRCode"
          @change="getTrack"
          counter="27"
          variant="outlined"
          single-line
          hide-details
        ></v-text-field>
      </v-card-text>
      
      <!-- Loading indicator -->
      <v-card-text v-if="loading">
        <v-progress-linear indeterminate></v-progress-linear>
        <p class="text-center mt-2">載入中...</p>
      </v-card-text>
      
      <!-- Results table -->
      <v-card-text v-if="!loading && items.length > 0">
        <v-data-table
          :headers="selectedHeaders"
          :items="items"
          :items-per-page="10"
          class="elevation-1"
        >
        </v-data-table>
      </v-card-text>
      
      <!-- No results message -->
      <v-card-text v-if="!loading && searchAttempted && items.length === 0">
        <v-alert type="info" variant="tonal">
          沒有找到相關資料
        </v-alert>
      </v-card-text>
    </v-card>

    <!-- Snackbar for notifications -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
      location="top end"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn variant="text" @click="snackbar.show = false">Close</v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { getData } from '@/utils/backend-api'

interface Header {
  title: string
  key: string
  align?: string
  sortable?: boolean
}

interface TracksheetItem {
  [key: string]: any
}

// Reactive state
const searchInput = ref<HTMLElement>()
const loading = ref(false)
const searchAttempted = ref(false)
const items = ref<TracksheetItem[]>([])
const isYarnQRcode = ref<boolean | null>(null)
const isCakeTrackSheetNO = ref<boolean | null>(null)
const isYarnTrackSheetNO = ref<boolean | null>(null)

const searchFilter = ref({
  contain: { trackNO: "" }
})

const snackbar = ref({
  show: false,
  message: "",
  color: "error"
})

// Headers for different types
const defaultHeaders: Header[] = [
  { title: "追踨傳票單號", key: "tracksheetNO", align: "start" },
  { title: "日期時間", key: "tracksheetTime", align: "start" },
  { title: "爐別", key: "furnaceName", align: "start" },
  { title: "品種", key: "productName", align: "start" },
  { title: "個數", key: "tracksheetQty" },
  { title: "淨重(kg)", key: "tracksheetNetWeight" }
]

const yarnQRcodeHeaders: Header[] = [
  { title: "BI檢測", key: "biName" },
  { title: "TEX檢測", key: "texName" },
  { title: "Lot NO", key: "batchName" },
  { title: "乾燥時間(hrs)", key: "dryTime" },
  { title: "過磅日期", key: "productDate", align: "start" },
  { title: "品種", key: "productName", align: "start" },
  { title: "等級", key: "gradeName" },
  { title: "爐別", key: "furnaceName", align: "start" },
  { title: "Bushing NO", key: "bushingNO" },
  { title: "Cake位置", key: "positionName" },
  { title: "Cake重量(g)", key: "cakeWeight" },
  { title: "開機日期", key: "workDate", align: "start" },
  { title: "Twister NO", key: "twisterNO" },
  { title: "Spindle NO", key: "spindleNO" },
  { title: "Is MFD", key: "isMFD" }
]

const cakeTrackSheetHeaders: Header[] = [
  { title: "追踪傳票單號", key: "tracksheetNO", align: "start" },
  { title: "日期時間", key: "tracksheetTime", align: "start" },
  { title: "爐別", key: "furnaceName", align: "start" },
  { title: "品種", key: "productName", align: "start" },
  { title: "等級", key: "gradeName" },
  { title: "個數", key: "tracksheetQty" },
  { title: "淨重(kg)", key: "tracksheetNetWeight" }
]

const yarnTrackSheetHeaders: Header[] = [
  { title: "追踪傳票單號", key: "documentNO", align: "start" },
  { title: "日期時間", key: "tracksheetTime", align: "start" },
  { title: "品種", key: "productName", align: "start" },
  { title: "Twister NO", key: "twisterNO" },
  { title: "開機T1個數", key: "trackT1Qty", align: "start" },
  { title: "開機T2個數", key: "trackT2Qty", align: "start" },
  { title: "品檢T1小計", key: "icountT1Sum", align: "start" },
  { title: "品檢T2小計", key: "icountT2Sum", align: "start" },
  { title: "追踪傳票序號", key: "tracksheetNO", align: "start" },
  { title: "爐別", key: "furnaceName", align: "start" },
  { title: "品檢T1個數", key: "icountT1Qty", align: "start" },
  { title: "品檢T2個數", key: "icountT2Qty", align: "start" }
]

// Computed
const selectedHeaders = computed(() => {
  if (isCakeTrackSheetNO.value) {
    return cakeTrackSheetHeaders
  } else if (isYarnTrackSheetNO.value) {
    return yarnTrackSheetHeaders
  } else if (isYarnQRcode.value) {
    return yarnQRcodeHeaders
  }
  return defaultHeaders
})

// Methods
const showError = (message: string) => {
  snackbar.value = {
    show: true,
    message,
    color: "error"
  }
}

const getTrack = async () => {
  const trackNO = searchFilter.value.contain.trackNO.trim()
  
  if (!trackNO) {
    showError("請輸入傳票單號or Cap QRCode")
    return
  }

  loading.value = true
  searchAttempted.value = true
  items.value = []

  try {
    // Determine the type of input
    isYarnQRcode.value = /^\d{14}[a-zA-Z0-9\s.]*$/.test(trackNO)
    isCakeTrackSheetNO.value = !!(
      /^F\d{11}\s*,[A-Za-z0-9]{10}$/.test(trackNO) ||
      /^D\d{11}\s*$/.test(trackNO)
    )
    isYarnTrackSheetNO.value = /^G\d{12}-\d{1}$/.test(trackNO) || /^G\d{12}-\d{1}-S\d{2}$/.test(trackNO)

    if (!isYarnQRcode.value && !isCakeTrackSheetNO.value && !isYarnTrackSheetNO.value) {
      showError("無效的傳票單號or Cap QRCode!")
      return
    }

    let apiUrl = ""
    let processedTrackNO = ""

    if (isYarnQRcode.value) {
      processedTrackNO = trackNO.slice(0, 14)
      apiUrl = `products/${processedTrackNO}`
    } else if (isCakeTrackSheetNO.value) {
      processedTrackNO = trackNO.slice(1, 12)
      apiUrl = `tracksheets/cake/${processedTrackNO}`
    } else if (isYarnTrackSheetNO.value) {
      processedTrackNO = trackNO.slice(1, 13)
      apiUrl = `tracksheets/qi/${processedTrackNO}`
    }

    const response = await getData(apiUrl)
    
    if (response.data && Array.isArray(response.data)) {
      items.value = response.data
    } else if (response.data) {
      items.value = [response.data]
    } else {
      items.value = []
    }

  } catch (error) {
    console.error('API Error:', error)
    showError("查詢失敗，請稍後再試")
    items.value = []
  } finally {
    loading.value = false
  }
}

const clearData = () => {
  searchFilter.value.contain.trackNO = ""
  items.value = []
  searchAttempted.value = false
  isYarnQRcode.value = null
  isCakeTrackSheetNO.value = null
  isYarnTrackSheetNO.value = null
  
  nextTick(() => {
    if (searchInput.value) {
      searchInput.value.focus()
    }
  })
}

// Lifecycle
onMounted(() => {
  nextTick(() => {
    if (searchInput.value) {
      searchInput.value.focus()
    }
  })
})
</script>
