<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <span class="text-h4">
              <v-icon class="mr-2">mdi-counter</v-icon>
              {{ isEditMode ? '編輯QI品檢計數作業' : '新增QI品檢計數作業' }}
            </span>
          </v-card-title>
          
          <v-card-text>
            <v-alert type="info" class="mb-4" v-if="!isEditMode">
              <strong>📊 新增QI品檢計數作業</strong>
              請填寫QI品檢計數作業的基本資訊。
            </v-alert>
            
            <v-alert type="warning" class="mb-4" v-if="isEditMode">
              <strong>✏️ 編輯QI品檢計數作業</strong>
              正在編輯ID為 {{ route.params.id }} 的QI品檢計數作業記錄。
            </v-alert>
            
            <v-form ref="form" v-model="formValid">
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.id"
                    label="單號"
                    :rules="[rules.required]"
                    outlined
                    dense
                    readonly
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.classDate"
                    label="班別日期"
                    type="date"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.shiftName"
                    :items="shiftOptions"
                    label="勤別"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.employeeId"
                    :items="employeeOptions"
                    item-title="employName"
                    item-value="id"
                    label="人員"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.groupName"
                    :items="groupOptions"
                    label="組別"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.typeName"
                    :items="typeOptions"
                    label="類型"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.totalCount"
                    label="總計數"
                    type="number"
                    :rules="[rules.required, rules.number]"
                    outlined
                    dense
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.passCount"
                    label="合格數"
                    type="number"
                    :rules="[rules.required, rules.number]"
                    outlined
                    dense
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.failCount"
                    label="不合格數"
                    type="number"
                    :rules="[rules.required, rules.number]"
                    outlined
                    dense
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="passRate"
                    label="合格率(%)"
                    readonly
                    outlined
                    dense
                    suffix="%"
                  />
                </v-col>
              </v-row>
            </v-form>
            
            <v-divider class="my-4" />
            
            <v-row>
              <v-col cols="12">
                <h3>測試狀態</h3>
                <v-list>
                  <v-list-item>
                    <template v-slot:prepend>
                      <v-icon color="success">mdi-check-circle</v-icon>
                    </template>
                    <v-list-item-title>Vue3 Composition API</v-list-item-title>
                    <v-list-item-subtitle>QI品檢計數表單響應式數據正常運作</v-list-item-subtitle>
                  </v-list-item>
                  
                  <v-list-item>
                    <template v-slot:prepend>
                      <v-icon color="success">mdi-check-circle</v-icon>
                    </template>
                    <v-list-item-title>計數功能</v-list-item-title>
                    <v-list-item-subtitle>品檢計數和合格率計算功能正常運作</v-list-item-subtitle>
                  </v-list-item>
                  
                  <v-list-item>
                    <template v-slot:prepend>
                      <v-icon color="success">mdi-check-circle</v-icon>
                    </template>
                    <v-list-item-title>路由參數</v-list-item-title>
                    <v-list-item-subtitle>
                      當前路由: {{ $route.path }}
                      {{ $route.params.id ? `(編輯模式 - ID: ${$route.params.id})` : '(新增模式)' }}
                    </v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>
            
            <v-row class="mt-4">
              <v-col cols="12">
                <h3>表單數據 (即時更新)</h3>
                <v-card variant="outlined">
                  <v-card-text>
                    <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          
          <v-card-actions>
            <v-btn
              color="primary"
              @click="saveForm"
              :disabled="!formValid"
              prepend-icon="mdi-content-save"
            >
              {{ isEditMode ? '更新' : '儲存' }}
            </v-btn>
            
            <v-btn
              color="secondary"
              @click="resetForm"
              prepend-icon="mdi-refresh"
            >
              重設
            </v-btn>
            
            <v-btn
              color="error"
              @click="goBack"
              prepend-icon="mdi-arrow-left"
            >
              返回
            </v-btn>
            
            <v-spacer />
            
            <v-chip color="info">
              表單狀態: {{ formValid ? '有效' : '無效' }}
            </v-chip>
            
            <v-chip color="success" class="ml-2" v-if="passRate">
              合格率: {{ passRate }}%
            </v-chip>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <!-- 成功訊息 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          關閉
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 響應式數據
const form = ref()
const formValid = ref(false)

const formData = ref({
  id: 0,
  classDate: new Date().toISOString().slice(0, 10),
  shiftName: '',
  employeeId: 0,
  groupName: '',
  typeName: 'YARN',
  totalCount: 0,
  passCount: 0,
  failCount: 0
})

const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

// 選項數據
const shiftOptions = ['早班', '中班', '晚班']
const groupOptions = ['A組', 'B組', 'C組', 'D組']
const typeOptions = ['YARN', 'CAKE', 'PACK']

const employeeOptions = ref([
  { id: 1, employName: '張三' },
  { id: 2, employName: '李四' },
  { id: 3, employName: '王五' }
])

// 驗證規則
const rules = {
  required: (value: any) => !!value || '此欄位為必填',
  number: (value: any) => !isNaN(Number(value)) || '請輸入有效數字'
}

// 計算屬性
const isEditMode = computed(() => !!route.params.id)

const passRate = computed(() => {
  if (formData.value.totalCount > 0) {
    return ((formData.value.passCount / formData.value.totalCount) * 100).toFixed(2)
  }
  return '0.00'
})

// 監聽總計數和合格數的變化，自動計算不合格數
watch([() => formData.value.totalCount, () => formData.value.passCount], ([total, pass]) => {
  if (total >= 0 && pass >= 0 && pass <= total) {
    formData.value.failCount = total - pass
  }
})

// 監聽總計數和不合格數的變化，自動計算合格數
watch([() => formData.value.totalCount, () => formData.value.failCount], ([total, fail]) => {
  if (total >= 0 && fail >= 0 && fail <= total) {
    formData.value.passCount = total - fail
  }
})

// 方法
const saveForm = async () => {
  try {
    // 模擬保存操作
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    showMessage(
      isEditMode.value ? 'QI品檢計數記錄更新成功！' : 'QI品檢計數記錄儲存成功！',
      'success'
    )
    
    if (!isEditMode.value) {
      const newId = Math.floor(Math.random() * 1000) + 1
      router.push(`/test-vue3/icount/${newId}`)
    }
  } catch (error) {
    showMessage('操作失敗，請重試', 'error')
  }
}

const resetForm = () => {
  formData.value = {
    id: 0,
    classDate: new Date().toISOString().slice(0, 10),
    shiftName: '',
    employeeId: 0,
    groupName: '',
    typeName: 'YARN',
    totalCount: 0,
    passCount: 0,
    failCount: 0
  }
  form.value?.resetValidation()
  showMessage('表單已重設', 'info')
}

const goBack = () => {
  router.push('/vue3-form-test-center')
}

const showMessage = (message: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}

// 生命週期
onMounted(() => {
  if (isEditMode.value) {
    // 模擬載入編輯數據
    formData.value = {
      id: Number(route.params.id),
      classDate: new Date().toISOString().slice(0, 10),
      shiftName: '早班',
      employeeId: 1,
      groupName: 'A組',
      typeName: 'YARN',
      totalCount: 100,
      passCount: 95,
      failCount: 5
    }
    
    showMessage(`載入QI品檢計數記錄編輯數據 (ID: ${route.params.id})`, 'info')
  } else {
    showMessage('QI品檢計數記錄新增模式已準備就緒', 'info')
  }
})
</script>

<style scoped>
.v-card {
  margin-bottom: 16px;
}

pre {
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
