import { getData, putData, postData, deleteData } from "@/utils/backend-api";
import { Entity, Product, Category, Remark } from "@/types";
import { getDefaultPagination, getPagination } from "@/utils/store-util";
import { appModule } from "./app";
import { get } from "lodash";
import {
  VuexModule,
  Module,
  Mutation,
  Action,
  getModule
} from "vuex-module-decorators";
import store from "@/store";

export interface ProductState {
  items: Entity[];
  pagination: Pagination;
  loading: boolean;
  product: Product;
  categories: Category[];
  remarks: Remark[];
}

@Module({ store, dynamic: true, name: "products" })
class ProductModule extends VuexModule implements ProductState {
  public items: Entity[] = [];
  public pagination = getDefaultPagination();
  public loading = false;
  public product = {} as Product;
  public categories: Category[] = [];
  public remarks: Remark[] = [];

  @Action
  getCategories() {
    getData("categories/").then(res => {
      const categories = res.data.map((c: Category) => {
        c.text = c.categoryName;
        c.value = c.id;
        return c;
      });
      this.setCategories(categories);
    });
  }

  @Action
  getRemarks() {
    getData("remarks/").then(res => {
      const remarks = res.data.map((c: Remark) => {
        c.remarkName = c.remarkNO + "  " + c.remarkName;
        c.value = c.id;
        return c;
      });
      this.setRemarks(remarks);
    });
  }

  @Action
  getProductById(id: string) {
    this.setLoading(true);
    if (id) {
      getData("products/" + id).then(
        res => {
          const product = res.data;
          this.setDataTable(product);
          this.setLoading(false);
        },
        (err: TODO) => {
          console.log(err);
          this.setLoading(false);
        }
      );
    } else {
      this.setProduct({} as Product);
      this.setLoading(false);
    }
  }

  @Action
  getAllProducts() {
    this.setLoading(true);
  }

  @Action
  clearProducts() {
    this.setLoading(true);
    const products = [];
    const product = {} as Product;
    this.setDataTable(products);
    this.setProduct(product);
    this.setLoading(false);
  }

  @Action
  searchProducts(searchQuery: string) {
    getData("products" + searchQuery).then(res => {
      const products = res.data;
      this.setDataTable(products);
      this.setLoading(false);
    });
  }

  @Action
  quickSearch(headers: TableHeader[], qsFilter: SeachQuery): void {
    getData("products").then(res => {
      const products = res.data.filter((r: TODO) =>
        headers.some((header: TODO) => {
          const val = get(r, [header.value]);
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter)) ||
            false
          );
        })
      );
      this.setDataTable(products);
      this.setLoading(false);
    });
  }

  @Action
  deleteProduct(id: number) {
    deleteData(`products/${id.toString()}`)
      .then(() => {
        this.getAllProducts();
        appModule.sendSuccessNotice("Operation is done.");
        appModule.closeNoticeWithDelay(3000);
      })
      .catch((err: TODO) => {
        console.log(err);
        appModule.sendErrorNotice("Operation failed! Please try again later. ");
        appModule.closeNoticeWithDelay(5000);
      });
  }

  @Action saveProduct(product: Product) {
    delete product.category;
    if (!product.id) {
      postData("products/", product)
        .then(res => {
          const product = res.data;
          this.setProduct(product);
          appModule.sendSuccessNotice("New record has been added.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    } else {
      putData("products/" + product.id.toString(), product)
        .then(res => {
          const product = res.data;
          this.setProduct(product);
          appModule.sendSuccessNotice("The record has been updated.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    }
  }

  @Action
  setDataTable(items: Product[]) {
    const pagination = getPagination(items);
    this.setPagination(pagination);
    this.setItems(items);
  }

  @Mutation
  setCategories(categories: Category[]) {
    this.categories = categories;
  }

  @Mutation
  setRemarks(remarks: Remark[]) {
    this.remarks = remarks;
  }

  @Mutation
  setItems(products: Product[]) {
    this.items = products;
  }

  @Mutation
  setPagination(pagination: Pagination) {
    this.pagination = pagination;
  }

  @Mutation
  setLoading(loading: boolean) {
    this.loading = loading;
  }

  @Mutation
  setProduct(product: Product) {
    this.product = product;
  }
}

export const productModule = getModule(ProductModule);
