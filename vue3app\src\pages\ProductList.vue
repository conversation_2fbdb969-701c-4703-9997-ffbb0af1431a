<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <span class="text-h4">
              <v-icon class="mr-2">mdi-package-variant</v-icon>
              產品管理
            </span>
            <v-spacer />
            <v-btn
              color="primary"
              prepend-icon="mdi-plus"
              @click="createProduct"
              class="mr-2"
            >
              新增產品
            </v-btn>
            <v-btn
              color="brown-lighten-1"
              size="small"
              icon
              @click="refreshData"
              :loading="isLoading"
            >
              <v-icon>mdi-refresh</v-icon>
            </v-btn>
          </v-card-title>

          <v-card-text>
            <!-- 搜尋區域 -->
            <v-row class="mb-4">
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="searchQuery"
                  label="搜尋產品 (名稱、代碼、QRCode)"
                  prepend-inner-icon="mdi-magnify"
                  variant="outlined"
                  clearable
                  @input="handleSearch"
                  @keyup.enter="performSearch"
                  hide-details
                />
              </v-col>
              <v-col cols="12" md="3">
                <v-select
                  v-model="selectedCategory"
                  :items="categoryOptions"
                  label="產品分類"
                  variant="outlined"
                  clearable
                  @update:model-value="handleCategoryFilter"
                  hide-details
                />
              </v-col>
              <v-col cols="12" md="3">
                <v-btn
                  color="primary"
                  @click="performSearch"
                  :loading="isLoading"
                  block
                  prepend-icon="mdi-magnify"
                >
                  搜尋
                </v-btn>
              </v-col>
            </v-row>

            <!-- 統計資訊 -->
            <v-row class="mb-4">
              <v-col cols="12" md="3">
                <v-card variant="outlined">
                  <v-card-text class="text-center">
                    <v-icon size="large" color="primary">mdi-package-variant</v-icon>
                    <div class="text-h6 mt-2">{{ filteredProducts.length }}</div>
                    <div class="text-caption">總產品數</div>
                  </v-card-text>
                </v-card>
              </v-col>
              <v-col cols="12" md="3">
                <v-card variant="outlined">
                  <v-card-text class="text-center">
                    <v-icon size="large" color="success">mdi-check-circle</v-icon>
                    <div class="text-h6 mt-2">{{ activeProductsCount }}</div>
                    <div class="text-caption">啟用產品</div>
                  </v-card-text>
                </v-card>
              </v-col>
              <v-col cols="12" md="3">
                <v-card variant="outlined">
                  <v-card-text class="text-center">
                    <v-icon size="large" color="info">mdi-tag-multiple</v-icon>
                    <div class="text-h6 mt-2">{{ allCategories.length }}</div>
                    <div class="text-caption">產品分類</div>
                  </v-card-text>
                </v-card>
              </v-col>
              <v-col cols="12" md="3">
                <v-card variant="outlined">
                  <v-card-text class="text-center">
                    <v-icon size="large" color="warning">mdi-clock</v-icon>
                    <div class="text-h6 mt-2">{{ formatDateTime(lastUpdateTime) }}</div>
                    <div class="text-caption">最後更新</div>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>

            <!-- 產品列表 -->
            <v-data-table
              :headers="headers"
              :items="filteredProducts"
              :loading="isLoading"
              :items-per-page="itemsPerPage"
              :search="searchQuery"
              class="elevation-1"
              loading-text="載入產品資料中..."
              no-data-text="查無產品資料"
            >
              <template v-slot:item.productCode="{ item }">
                <v-chip color="primary" size="small">
                  {{ item.code }}
                </v-chip>
              </template>

              <template v-slot:item.categoryName="{ item }">
                <v-chip
                  :color="getCategoryColor(item.categoryName || '')"
                  size="small"
                  variant="tonal"
                >
                  {{ item.categoryName || '未分類' }}
                </v-chip>
              </template>

              <template v-slot:item.isActive="{ item }">
                <v-chip
                  color="success"
                  size="small"
                >
                  <v-icon start>mdi-check</v-icon>
                  啟用
                </v-chip>
              </template>

              <template v-slot:item.createdAt="{ item }">
                <span>{{ formatDateTime(item.createdAt?.toString() || '') }}</span>
              </template>

              <template v-slot:item.actions="{ item }">
                <v-btn
                  color="primary"
                  size="small"
                  variant="text"
                  @click="editProduct(item)"
                  icon
                >
                  <v-icon>mdi-pencil</v-icon>
                </v-btn>
                <v-btn
                  color="info"
                  size="small"
                  variant="text"
                  @click="viewProduct(item)"
                  icon
                >
                  <v-icon>mdi-eye</v-icon>
                </v-btn>
                <v-btn
                  color="error"
                  size="small"
                  variant="text"
                  @click="deleteProduct(item)"
                  icon
                >
                  <v-icon>mdi-delete</v-icon>
                </v-btn>
              </template>

              <template v-slot:no-data>
                <v-alert type="info" class="ma-4">
                  <div class="text-center">
                    <v-icon size="large" class="mb-2">mdi-package-variant-closed</v-icon>
                    <div>目前沒有產品資料</div>
                    <v-btn
                      color="primary"
                      @click="createProduct"
                      class="mt-2"
                      prepend-icon="mdi-plus"
                    >
                      新增第一個產品
                    </v-btn>
                  </div>
                </v-alert>
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 確認刪除對話框 -->
    <v-dialog v-model="deleteDialog" max-width="400">
      <v-card>
        <v-card-title>
          <v-icon color="error" class="mr-2">mdi-delete</v-icon>
          確認刪除
        </v-card-title>
        <v-card-text>
          確定要刪除產品「{{ selectedProduct?.productName }}」嗎？
          <br>
          <small class="text-error">此操作無法復原</small>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="deleteDialog = false">取消</v-btn>
          <v-btn
            color="error"
            @click="confirmDelete"
            :loading="isDeleting"
          >
            刪除
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 成功訊息 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          關閉
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useProductsStore } from '@/stores/products'
import type { Product } from '@/types'

const router = useRouter()
const productsStore = useProductsStore()

// 響應式數據
const searchQuery = ref('')
const selectedCategory = ref('')
const itemsPerPage = ref(10)
const deleteDialog = ref(false)
const selectedProduct = ref<Product | null>(null)
const isDeleting = ref(false)
const lastUpdateTime = ref(new Date().toISOString())

const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

// 計算屬性
const isLoading = computed(() => productsStore.isLoading)
const products = computed(() => productsStore.products)
const allCategories = computed(() => productsStore.allCategories)

const categoryOptions = computed(() => [
  { title: '全部分類', value: '' },
  ...allCategories.value.map(cat => ({
    title: cat.categoryName,
    value: cat.categoryName
  }))
])

const filteredProducts = computed(() => {
  let filtered = products.value

  if (selectedCategory.value) {
    filtered = filtered.filter(product =>
      product.categoryName === selectedCategory.value
    )
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(product =>
      product.productName?.toLowerCase().includes(query) ||
      product.name?.toLowerCase().includes(query) ||
      product.code?.toLowerCase().includes(query)
    )
  }

  return filtered
})

const activeProductsCount = computed(() =>
  products.value.length
)

// 表格標題
const headers = [
  { title: '產品代碼', key: 'productCode', align: 'start' as const },
  { title: '產品名稱', key: 'productName', align: 'start' as const },
  { title: '產品分類', key: 'categoryName', align: 'start' as const },
  { title: '狀態', key: 'isActive', align: 'center' as const },
  { title: '建立時間', key: 'createdAt', align: 'start' as const },
  { title: '操作', key: 'actions', align: 'center' as const, sortable: false }
]

// 方法
const loadData = async () => {
  try {
    await Promise.all([
      productsStore.getProducts(),
      productsStore.getCategories()
    ])
    lastUpdateTime.value = new Date().toISOString()
    showMessage('產品資料載入完成', 'success')
  } catch (error) {
    console.error('載入產品資料失敗:', error)
    showMessage('載入產品資料失敗', 'error')
  }
}

const refreshData = async () => {
  await loadData()
}

const handleSearch = () => {
  // 即時搜尋，由 computed 自動處理
}

const performSearch = async () => {
  if (searchQuery.value && searchQuery.value.length === 14) {
    // 如果是14位數的QRCode，執行特殊查詢
    try {
      await productsStore.getProductByQRCode(searchQuery.value)
      showMessage('QRCode查詢完成', 'success')
    } catch (error) {
      showMessage('QRCode查詢失敗', 'error')
    }
  }
}

const handleCategoryFilter = () => {
  // 分類篩選，由 computed 自動處理
}

const createProduct = () => {
  router.push('/products/new')
}

const editProduct = (product: Product) => {
  router.push(`/product/${product.id}`)
}

const viewProduct = (product: Product) => {
  router.push(`/product/${product.id}/view`)
}

const deleteProduct = (product: Product) => {
  selectedProduct.value = product
  deleteDialog.value = true
}

const confirmDelete = async () => {
  if (!selectedProduct.value) return

  isDeleting.value = true
  try {
    // 這裡應該調用 API 刪除產品
    // await productsStore.deleteProduct(selectedProduct.value.id)
    showMessage(`產品「${selectedProduct.value.productName}」已刪除`, 'success')
    deleteDialog.value = false
    await loadData()
  } catch (error) {
    showMessage('刪除產品失敗', 'error')
  } finally {
    isDeleting.value = false
    selectedProduct.value = null
  }
}

const getCategoryColor = (categoryName: string) => {
  const colors = ['primary', 'secondary', 'success', 'info', 'warning']
  const index = categoryName ? categoryName.length % colors.length : 0
  return colors[index]
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-TW')
}

const showMessage = (message: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}

// 監聽器
watch(searchQuery, (newValue) => {
  if (newValue && newValue.length === 14) {
    performSearch()
  }
})

// 生命週期
onMounted(async () => {
  await loadData()
})
</script>

<style scoped>
.v-card {
  margin-bottom: 16px;
}

.v-data-table {
  border-radius: 8px;
}

.text-h6 {
  font-weight: 600;
}

.text-caption {
  opacity: 0.7;
}
</style>
