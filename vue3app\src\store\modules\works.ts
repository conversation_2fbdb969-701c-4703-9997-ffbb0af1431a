import { Component, Vue } from 'vue-facing-decorator'
import { createApp } from 'vue'
import { getData, putData, postData, deleteData } from '@/utils/backend-api'
import { Employee, Work, Entity, Track, Workline, Pagination, TableHeader } from '@/types'
import { getDefaultPagination, getPagination } from '@/utils/store-util'
import { appModule } from '@/stores/app'
import { get } from 'lodash'

export interface WorkState {
  items: Entity[]
  pagination: Pagination
  loading: boolean
  work: Work
  customer: string
  workline: Workline[]
  track: Track
  employees: Employee[]
}

@Component({
  name: 'WorkModule'
})
class WorkModule extends Vue implements WorkState {
  items: Entity[] = []
  pagination: Pagination = getDefaultPagination()
  loading = false
  work: Work = {
    id: null,
    value: null,
    worklines: [],
    quantity: 0,
    workDate: '',
    group: '',
    shift: '',
    amount: 0,
    shiftName: '',
    groupName: ''
  }
  customer = ""
  workline: Workline[] = []
  track: Track = {
    id: null,
    value: null,
    furnaceName: '',
    productName: '',
    twistName: '',
    trackDate: '',
    trackTime: '',
    twisterNO: '',
    trackT1Qty: 0,
    trackT2Qty: 0
  }
  employees: Employee[] = []

  async getEmployees() {
    const res = await getData("employees/tw")
    if (res.data) {
      const employees = res.data.map((c: Employee) => {
        c.employName = c.employNO + " " + c.userName
        c.value = c.id
        return c
      })
      this.setEmployees(employees)
    }
  }

  async getWorkById(id: string) {
    if (id) {
      try {
        const res = await getData("works/" + id)
        const _work = res.data
        const work = _work[0]
        work.worklines = work.worklines?.filter((p: Work) => p !== null && p !== undefined) || []
        this.setWork(work)
      } catch (err) {
        console.log(err)
      }
    } else {
      const work = {
        id: null,
        value: null,
        worklines: [],
        quantity: 0,
        workDate: '',
        group: '',
        shift: '',
        amount: 0,
        shiftName: '',
        groupName: ''
      }
      this.setWork(work)
      this.setLoading(false)
    }
  }

  async getTrackById(id: string) {
    this.setLoading(true)
    if (id) {
      try {
        const res = await getData("tracks/" + id)
        const track = res.data
        this.setTrack(track)
        this.setLoading(false)
      } catch (err) {
        console.log(err)
        this.setLoading(false)
      }
    } else {
      this.setTrack({
        id: null,
        value: null,
        furnaceName: '',
        productName: '',
        twistName: '',
        trackDate: '',
        trackTime: '',
        twisterNO: '',
        trackT1Qty: 0,
        trackT2Qty: 0
      })
      this.setLoading(false)
    }
  }

  async getAllWorks() {
    this.setLoading(true)
    const res = await getData("works")
    const works = res.data
    works.forEach((item: Work) => {
      item.quantity = item.worklines?.length || 0
    })
    this.setDataTable(works)
    this.setLoading(false)
  }

  async searchWorks(searchQuery: string) {
    const res = await getData("works" + searchQuery)
    const works = res.data
    works.forEach((item: Work) => {
      item.amount = item.worklines?.reduce(
        (p: any, c: any) => p + (c.unitPrice || 0),
        0
      ) || 0
      item.quantity = item.worklines?.length || 0
    })
    this.setDataTable(works)
    this.setLoading(false)
  }

  async quickSearch(headers: TableHeader[], qsFilter: string) {
    const res = await getData("works")
    const works = res.data.filter((r: any) =>
      headers.some((header: TableHeader) => {
        const val = get(r, [header.value])
        return (
          (val &&
            val
              .toString()
              .toLowerCase()
              .includes(qsFilter)) ||
          false
        )
      })
    )
    works.forEach((item: Work) => {
      item.quantity = item.worklines?.length || 0
    })
    this.setDataTable(works)
    this.setLoading(false)
  }

  async deleteWork(id: number) {
    try {
      await deleteData(`works/${id.toString()}`)
      appModule.sendSuccessNotice("Operation is done.")
      appModule.closeNoticeWithDelay(3000)
      await this.getAllWorks()
    } catch (err) {
      console.log(err)
      appModule.sendErrorNotice("Operation failed! Please try again later.")
      appModule.closeNoticeWithDelay(3000)
    }
  }

  async addWorklineToWork(workline: Workline) {
    if (workline && this.work.id) {
      await this.saveWorkline(workline)
      await this.getWorkById(this.work.id.toString())
      const newWork = this.work
      this.setWork(newWork)
    }
  }

  async deleteWorkline(workline: Workline) {
    if (workline && workline.id && this.work.id) {
      try {
        await deleteData(`worklines/${workline.id.toString()}`)
        await this.getWorkById(this.work.id.toString())
        const newWork = this.work
        this.setWork(newWork)
        appModule.sendSuccessNotice("Operation is done.")
      } catch (err) {
        console.log(err)
        appModule.sendErrorNotice("Operation failed! Please try again later.")
        appModule.closeNoticeWithDelay(3000)
      }
    }
  }

  async saveWork(workData: Work) {
    try {
      if (!workData.id) {
        const res = await postData("works/", workData)
        const savedWork = res.data
        const WorkId = { id: savedWork[0] }
        const addWork = { ...WorkId, ...this.work }
        this.setWork(addWork)
        appModule.sendSuccessNotice("New work has been added.")
        appModule.closeNoticeWithDelay(3000)
      } else {
        const res = await putData("works/" + workData.id.toString(), workData)
        const updatedWork = res.data
        this.setWork(updatedWork)
        appModule.sendSuccessNotice("Work has been updated.")
      }
    } catch (err) {
      console.log(err)
      appModule.sendErrorNotice("Operation failed! Please try again later.")
      appModule.closeNoticeWithDelay(3000)
    }
  }

  async saveWorkline(workline: Workline) {
    try {
      if (!workline.id) {
        await postData("worklines/", workline)
        appModule.sendSuccessNotice("New workline has been added.")
      } else {
        const res = await putData("works/" + workline.id.toString(), workline)
        const work = res.data
        this.setWork(work)
        appModule.sendSuccessNotice("Workline has been updated.")
      }
    } catch (err) {
      console.log(err)
      appModule.sendErrorNotice("Operation failed! Please try again later.")
      appModule.closeNoticeWithDelay(3000)
    }
  }

  clearWorkline() {
    this.setLoading(true)
    const workline: Workline[] = []
    this.setWorkline(workline)
    this.setLoading(false)
  }

  setDataTable(items: Work[]) {
    const pagination = getPagination(items)
    this.setPagination(pagination)
    this.setItems(items)
  }

  setEmployees(employees: Employee[]) {
    this.employees = employees
  }

  setWorkline(workline: Workline[]) {
    this.workline = workline
  }

  setItems(works: Work[]) {
    this.items = works
  }

  setPagination(pagination: Pagination) {
    this.pagination = pagination
  }

  setLoading(loading: boolean) {
    this.loading = loading
  }

  setWork(work: Work) {
    this.work = work
  }

  setTrack(track: Track) {
    this.track = track
  }
}

// Create and export a singleton instance
const app = createApp(WorkModule)
const vm = app.mount(document.createElement('div'))
export const workModule = vm as InstanceType<typeof WorkModule>
