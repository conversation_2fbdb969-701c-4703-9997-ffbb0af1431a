const db = require("../config/dbConfig.js");
require("dotenv").config();
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET ALL RELABELS OF MFD N
const findByType = type => {
  return db("aits_relabels")
    .select({
      id: "aits_relabels.id",
      classDate: "aits_relabels.classdate",
      shiftName: "aits_relabels.shiftname",
      employId: "aits_relabels.employid",
      groupName: "aits_relabels.groupname",
      typeName: "aits_relabels.typename",
      created: "aits_relabels.created",
      updated: "aits_relabels.updated",
      quantity: db.raw("count(aits_relabellines.relabelid)")
    })
    .leftJoin("aits_relabellines", "aits_relabels.id", "aits_relabellines.relabelid")
    .where("aits_relabels.typename", type)    
    .where("aits_relabels.classdate", ">=", process.env.DUE_TO_QUERY_DATE)
    .groupBy("aits_relabels.id", "aits_relabels.classdate", "aits_relabels.shiftname", "aits_relabels.employid", "aits_relabels.groupname", "aits_relabels.typename")
    .orderBy("aits_relabels.id", "desc")
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

// GET RELABEL BY ID
const findById = id => {
  return db("aits_relabels")    
    .select({
      id: "aits_relabels.id",
      classDate: "aits_relabels.classdate",
      shiftName: "aits_relabels.shiftname",
      employId: "aits_relabels.employid",
      groupName: "aits_relabels.groupname",
      typeName: "aits_relabels.typename",
      created: "aits_relabels.created",
      updated: "aits_relabels.updated"
    })
    .where("aits_relabels.id", id)
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

// ADD A RELABEL
const addRelabel = relabel => {
  return db.transaction(trx => {
    return db("aits_relabels")
    .insert({
    classdate: relabel.classDate,
    shiftname: relabel.shiftName,
    employid: relabel.employId,
    groupname: relabel.groupName,
    typename: relabel.typeName,
    created: db.fn.now()
    }, "id")
    .then(trx.commit)
    .catch(trx.rollback);
    })  
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

// UPDATE RELABEL
const updateRelabel = (id, relabel) => {
  let origin,result;
  return db.transaction(trx => {
    return db("aits_relabels")
      .where("id", id)
      .then(old_relabel => {
        origin = old_relabel;
        if (old_relabel) {
          return db("aits_relabels")
          .where("aits_relabels.id", id)
          .update({
            classdate: relabel.classDate,
            shiftname: relabel.shiftName,
            employid: relabel.employId,
            groupname: relabel.groupName,
            typename: relabel.typeName,
            updated: db.fn.now()
          })
          .then(trx.commit)
          .catch(trx.rollback);
        }
    })
  })
  .then(()=>{
    return db("aits_relabels")
    .where("aits_relabels.id", id)
    .then(new_relabel => { 
      result = new_relabel;
      infoLogger.info(`update relabel content: ${JSON.stringify({origin,result})}`)
    })
  }) 
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

// REMOVE RELABEL
const removeRelabel = id => {
  let result;
  return db.transaction(trx => {
    return db("aits_relabels")
      .where("aits_relabels.id", id)
      .then(relabel => {
        result = relabel;
        if (relabel) { 
          return db("aits_relabels")
            .where("aits_relabels.id", id)
            .del()
            .then(trx.commit)
            .catch(trx.rollback);
        }
      })
    })
  .then(() => { 
    infoLogger.info(`remove relabel content: ${JSON.stringify(result)}`)
  })
  .catch((err) => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

// GET COUNT BY TYPE FOR PAGINATION
const countByType = type => {
  return db("aits_relabels")
    .count("id as count")
    .where("aits_relabels.typename", type)
    .where("aits_relabels.classdate", ">=", process.env.DUE_TO_QUERY_DATE)
    .first()
    .then(result => parseInt(result.count))
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

// GET RELABELS BY TYPE WITH PAGINATION
const findByTypeWithPagination = (type, limit, offset) => {
  return db("aits_relabels")
    .select({
      id: "aits_relabels.id",
      classDate: "aits_relabels.classdate",
      shiftName: "aits_relabels.shiftname",
      employId: "aits_relabels.employid",
      groupName: "aits_relabels.groupname",
      typeName: "aits_relabels.typename",
      created: "aits_relabels.created",
      updated: "aits_relabels.updated",
      quantity: db.raw("count(aits_relabellines.relabelid)")
    })
    .leftJoin("aits_relabellines", "aits_relabels.id", "aits_relabellines.relabelid")
    .where("aits_relabels.typename", type)    
    .where("aits_relabels.classdate", ">=", process.env.DUE_TO_QUERY_DATE)
    .groupBy("aits_relabels.id", "aits_relabels.classdate", "aits_relabels.shiftname", "aits_relabels.employid", "aits_relabels.groupname", "aits_relabels.typename", "aits_relabels.created", "aits_relabels.updated")
    .orderBy("aits_relabels.id", "desc")
    .limit(limit)
    .offset(offset)
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

module.exports = {
  findByType,
  findById,
  addRelabel,
  updateRelabel,
  removeRelabel,
  countByType,
  findByTypeWithPagination
};
