const db = require("../config/dbConfig.js");
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET SPECIFIC RELABELLINE BY ID
const findById = id => {
  return db("aits_relabellines")
    .select({
    id: "aits_relabellines.id",
    relabelId: "aits_relabellines.relabelid",
    furnaceName: "aits_relabellines.furnacename",
    productName: "aits_relabellines.productname",
    gradeName: "aits_relabellines.gradename",
    isMFD: "aits_relabellines.ismfd",
    categoryId: "aits_relabellines.categoryid",
    remarkId: "aits_relabellines.remarkid",
    workDate: "aits_relabellines.workdate",
    twisterNO: "aits_relabellines.twisterno",
    spindleNO: "aits_relabellines.spindleno",
    codeName: "aits_relabellines.codename",
    m_product_id: "aits_relabellines.m_product_id",
    m_twqrcodeline_id: "aits_relabellines.m_twqrcodeline_id",
    created: "aits_relabellines.created"
    })
    .where("aits_relabellines.relabelid", id)
    .orderBy("id", "desc") 
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

// GET SPECIFIC RELABELLINE BY CODE
const findByCode = code => {
  return db("aits_relabellines")
    .select({
    codeName: "aits_relabellines.codename"    
    }) 
    .where(db.raw("TRIM(aits_relabellines.codename)"), code)
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
  });
};

// ADD A RELABELLINE
const addRelabelline = relabelline => {
  return db.transaction(trx => {
    return trx("aits_relabellines")
      .insert({     
        relabelid: relabelline.relabelId,
        furnacename: relabelline.furnaceName,
        productname: relabelline.productName,
        gradename: relabelline.gradeName,
        ismfd: relabelline.isMFD,
        categoryid: relabelline.categoryId,
        remarkid: relabelline.remarkId,
        workdate: relabelline.workDate,
        twisterno: relabelline.twisterNO,
        spindleno: relabelline.spindleNO,
        codename: relabelline.codeName,      
        m_product_id: relabelline.m_product_id,
        m_twqrcodeline_id: relabelline.m_twqrcodeline_id,
        created: db.fn.now()
      }, "id")
      .then(() => {
        return trx("m_twqrcodeline")
          .where("m_twqrcodeline_id", relabelline.m_twqrcodeline_id)
          .update({
            ismfd: "N",
            updated: db.fn.now()
          });
      })
      .then(trx.commit)
      .catch(trx.rollback);
  })  
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

// REMOVE RELABELLINE
const removeRelabelline = id => {
  let result;
  return db.transaction(trx => {
    return db("aits_relabellines")
      .where("id", id)
      .then(relabelline => {
        result = relabelline;
        const twqrcodelineIds = relabelline.map(relabelline => relabelline.m_twqrcodeline_id);
        return trx("m_twqrcodeline")
          .whereIn("m_twqrcodeline_id", twqrcodelineIds) // Use the correct column name, "m_twqrcodeline_id".
          .update({
            ismfd: "Y",
            updated: db.fn.now()
          })
          .then(() => {
            return trx("aits_relabellines")
              .where("id", id)
              .del();
          });
      })
      .then(trx.commit)
      .catch(trx.rollback);
  })
  .then(() => { 
    infoLogger.info(`remove relabelline content: ${JSON.stringify(result)}`)
  })
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

module.exports = {
  findById,
  findByCode,
  addRelabelline,
  removeRelabelline
};
