<template>
  <v-container fluid>
    <v-card class="grey lighten-4 elevation-0">
      <v-form ref="validForm" v-model="formValid">
        <v-card-title class="title">
          {{ title }}
          <v-spacer></v-spacer>
          <v-btn
            :elevation="4"
            icon
            size="small"
            color="grey"
            class="mr-2"
            @click="cancel()"
          >
            <v-icon>mdi-close-circle-outline</v-icon>
          </v-btn>
          <v-btn
            :elevation="4"
            icon
            size="small"
            color="purple"
            class="mr-2"
            @click="save()"
            :disabled="isSaving || !formValid"
            :loading="isSaving"
          >
            <v-icon>mdi-content-save-all</v-icon>
          </v-btn>
          <v-btn
            :elevation="4"
            icon
            size="small"
            color="blue"
            @click="addProduct()"
          >
            <v-icon>mdi-plus</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-container fluid grid-list-md>
            <v-row>
              <v-col md="4" cols="12">
                <v-text-field
                  name="id"
                  label="單號"
                  type="number"
                  hint="InspectID is required"
                  v-model="inspect.id"
                  class="input-group--focused"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col md="4" cols="12">
                <v-menu
                  :close-on-content-click="false"
                  v-model="classDateMenu"
                  transition="v-scale-transition"
                  offset-y
                  :nudge-left="40"
                  max-width="290px"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      v-bind="props"
                      label="日期"
                      v-model="inspect.classDate"
                      prepend-icon="mdi-calendar"
                      readonly
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="inspect.classDate"
                    no-title
                    scrollable
                  >
                  </v-date-picker>
                </v-menu>
              </v-col>
              <v-col md="4" cols="12">
                <v-radio-group
                  name="shiftName"
                  label="勤別"
                  v-model="inspect.shiftName"
                  :rules="[value => !!value || '必要!!請選擇']"
                  required
                  row
                >
                  <v-radio label="I" value="1"></v-radio>
                  <v-radio label="II" value="2"></v-radio>
                  <v-radio label="III" value="3"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col md="6" cols="12">
                <v-autocomplete
                  :items="employees"
                  label="人員"
                  item-title="employName"
                  item-value="employId"
                  v-model="inspect.employId"
                  :rules="[value => !!value || '必要!!請選擇']"
                  required
                ></v-autocomplete>
              </v-col>
              <v-col md="4" cols="12">
                <v-radio-group
                  name="groupName"
                  label="組別"
                  v-model="inspect.groupName"
                  :rules="[value => !!value || '必要!!請選擇']"
                  required
                  row
                >
                  <v-radio label="A" value="A"></v-radio>
                  <v-radio label="B" value="B"></v-radio>
                  <v-radio label="C" value="C"></v-radio>
                  <v-radio label="D" value="D"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col md="4" cols="12">
                <v-text-field
                  name="quantity"
                  label="個數"
                  type="number"
                  v-model="inspect.quantity"
                  class="input-group--focused"
                  readonly
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-card>
                  <v-data-table
                    v-if="loading === false"
                    :headers="headers"
                    :items="inspectlinesWithIndex"
                    :items-per-page="10"
                    class="elevation-1 colored-pagination"
                  >
                    <template v-slot:item.countdown="{ index }">
                      {{ index + 1 }}
                    </template>
                    <template v-slot:item.actions="{ item }">
                      <v-btn
                        :elevation="4"
                        icon
                        size="x-small"
                        color="red"
                        @click="remove(item)"
                      >
                        <v-icon>mdi-delete</v-icon>
                      </v-btn>
                    </template>
                  </v-data-table>
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
      </v-form>
    </v-card>

    <!-- 明細編輯對話框 -->
    <v-dialog v-model="addProductModal" width="700" persistent>
      <v-form ref="validDetail" v-model="detailValid">
        <v-card>
          <v-card-title>
            {{ title }}
            <v-spacer></v-spacer>
            <v-card-actions>
              <v-btn
                class="green lighten-1"
                text
                :disabled="!detailValid"
                @click="saveInspectline"
              >
                Confirm
              </v-btn>
              <v-btn
                class="orange lighten-1"
                text
                @click="cancelAddProduct"
              >
                Cancel
              </v-btn>
            </v-card-actions>
          </v-card-title>
          <v-card-text>
            1.請掃Cap QRCode 2.選擇原因 3.變更定長別
            <v-text-field
              ref="qrCodeInput"
              v-model="searchFilter.contain.codeName"
              append-icon="mdi-magnify"
              label="Cap QRCode"
              @change="getProduct"
              :counter="14"
              :rules="[value => !!value || '必要!!請選擇']"
              required
            ></v-text-field>
            <v-container fluid grid-list-md>
              <v-row>
                <v-col md="6" cols="12">
                  <v-autocomplete
                    :items="remarks"
                    label="原因"
                    v-model="inspectline.remarkId"
                    item-title="remarkName"
                    item-value="remarkId"
                    :rules="[value => !!value || '必要!!請選擇']"
                    required
                  ></v-autocomplete>
                </v-col>
                <v-col md="6" cols="12">
                  <v-autocomplete
                    :items="categories"
                    label="定長別變更"
                    v-model="inspectline.categoryId"
                    item-title="categoryName"
                    item-value="categoryId"
                  ></v-autocomplete>
                </v-col>
                <v-col
                  md="6"
                  cols="12"
                  v-for="(item, index) in product"
                  :key="index"
                >
                  <v-text-field
                    v-model="item.biName"
                    label="BI檢測"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.productName"
                    label="品種"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.gradeName"
                    label="定長別"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.texName"
                    label="Tex檢測"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.batchName"
                    label="Lot NO"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.productDate"
                    label="過磅日期"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.bushingNO"
                    label="Bushing NO"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.positionName"
                    label="Cake位置"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.cakeWeight"
                    label="Cake重量"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.workDate"
                    label="開機日期"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.twisterNO"
                    label="Twister NO"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.spindleNO"
                    label="Spindle NO"
                    readonly
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-container>
          </v-card-text>
        </v-card>
      </v-form>
    </v-dialog>

    <!-- 確認對話框 -->
    <v-dialog v-model="dialog" max-width="400">
      <v-card>
        <v-card-title>{{ dialogTitle }}</v-card-title>
        <v-card-text>{{ dialogText }}</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey" variant="text" @click="onCancel">取消</v-btn>
          <v-btn color="error" variant="text" @click="onConfirm">確認</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 訊息提示 -->
    <v-snackbar
      v-if="loading === false"
      :top="true"
      :right="true"
      :timeout="5000"
      :color="mode"
      v-model="snackbar"
    >
      <div class="text-center">
        {{ notice }}
      </div>
      <template v-slot:actions>
        <v-btn dark text @click="closeSnackbar">Close</v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useInspectsStore } from '@/stores/inspects'
import { useProductsStore } from '@/stores/products'
import { useAppStore } from '@/stores/app'

const route = useRoute()
const router = useRouter()

// Store 實例
const inspectsStore = useInspectsStore()
const productsStore = useProductsStore()
const appStore = useAppStore()

// 響應式數據
const validForm = ref()
const validDetail = ref()
const qrCodeInput = ref()
const formValid = ref(false)
const detailValid = ref(false)

// Vue2 風格的數據結構
const title = ref("")
const type = ref("YARN")
const inspectId = ref(0)
const inspectlineId = ref(null)
const categoryId = ref(0)
const remarkId = ref(0)
const selectedInspectline = ref(null)
const isYarnQRcode = ref(null)
const query = ref("")
const classDateMenu = ref(false)
const addProductModal = ref(false)
const dialog = ref(false)
const dialogTitle = ref("QI品檢異常(明細)刪除確認")
const dialogText = ref("刪除該筆記錄?")
const isSaving = ref(false)  // 防止重複儲存
const lastSaveTime = ref(0)  // 記錄上次儲存時間，防止短時間內重複儲存

// 明細分頁狀態
const detailPagination = ref({
  page: 1,
  limit: 10,
  totalItems: 0,
  totalPages: 0,
  hasNextPage: false,
  hasPrevPage: false
})

// 表格標題
const headers = ref([
  { title: "序號", key: "countdown", align: "start" },
  { title: "BI檢測", key: "biName" },
  { title: "乾燥時間(hrs)", key: "dryTime" },
  { title: "品種", key: "productName", align: "start" },
  { title: "定長別", key: "gradeName" },
  { title: "定長別變更", key: "categoryName" },
  { title: "原因", key: "remarkName" },
  { title: "TEX檢測", key: "texName" },
  { title: "Lot NO", key: "batchName" },
  { title: "過磅日期", key: "productDate", align: "start" },
  { title: "Bushing NO", key: "bushingNO" },
  { title: "Cake位置", key: "positionName" },
  { title: "Cake重量(g)", key: "cakeWeight" },
  { title: "開機日期", key: "workDate", align: "start" },
  { title: "Twister NO", key: "twisterNO" },
  { title: "Spindle NO", key: "spindleNO" },
  { title: "", key: "actions", sortable: false }
])

const searchFilter = ref({ contain: { codeName: "" } })

// 計算屬性 - 從 store 獲取數據
const employees = computed(() => inspectsStore.employees)

// 使用本地響應式數據來避免直接修改 store 的計算屬性
const inspect = ref({} as any)

// 監聽 store 的變化並同步到本地
const storeInspect = computed(() => inspectsStore.currentInspect)
const isUpdatingFromStore = ref(false)

watch(storeInspect, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0 && !isUpdatingFromStore.value) {
    // 創建深拷貝以避免響應性問題
    inspect.value = JSON.parse(JSON.stringify(newValue))
    // 計算個數（明細檔數量）
    if (inspect.value.inspectlines) {
      inspect.value.quantity = inspect.value.inspectlines.length
    }
  }
}, { immediate: true, deep: true })
const categories = computed(() => inspectsStore.categories)
const remarks = computed(() => inspectsStore.remarks)
const inspectlinesWithIndex = computed(() => {
  if (inspect.value && inspect.value.inspectlines) {
    return inspect.value.inspectlines.map((item, index) => ({
      ...item,
      countdown: index + 1
    }))
  }
  return []
})
const inspectline = ref({
  inspectId: 0,
  remarkId: null,
  categoryId: null
})
const product = computed(() => productsStore.products)
const loading = computed(() => appStore.loading)
const mode = computed(() => appStore.mode)
const snackbar = computed(() => appStore.snackbar)
const notice = computed(() => appStore.notice)

// 方法
const save = async () => {
  const currentTime = Date.now()

  // 如果正在儲存中，直接返回
  if (isSaving.value) {
    console.log('正在儲存中，忽略重複點擊')
    appStore.sendErrorNotice("正在儲存中，請稍候...")
    return
  }

  // 防止短時間內重複點擊（1秒內）
  if (currentTime - lastSaveTime.value < 1000) {
    console.log('短時間內重複點擊，忽略')
    appStore.sendErrorNotice("請勿重複點擊儲存按鈕")
    return
  }

  // 先驗證表單
  const { valid } = await validForm.value.validate()

  if (!valid) {
    appStore.sendErrorNotice("請填寫所有必填欄位!")
    return
  }

  // 檢查必填欄位
  if (!inspect.value.shiftName || !inspect.value.employId || !inspect.value.groupName) {
    appStore.sendErrorNotice("請填寫所有必填欄位!")
    return
  }

  // 設置儲存中狀態和時間戳
  isSaving.value = true
  lastSaveTime.value = currentTime
  console.log('開始儲存，設置 isSaving = true')

  // 準備儲存資料，不自動填入預設值
  const inspectData = {
    ...inspect.value,
    typeName: inspect.value.typeName || "YARN",
    classDate: inspect.value.classDate || new Date().toISOString().slice(0, 10)
  }

  console.log('準備保存的資料:', inspectData)

  // 設置標誌防止 watch 覆蓋用戶變更
  isUpdatingFromStore.value = true

  try {
    if (!inspectData.id || inspectData.id <= 0) {
      // 新增模式
      console.log('執行新增模式儲存')
      await inspectsStore.saveInspect(inspectData)

      // 儲存成功後，更新本地的 inspect 資料，包含新的 ID
      const savedInspect = inspectsStore.currentInspect
      if (savedInspect && savedInspect.id) {
        inspect.value = { ...inspect.value, id: savedInspect.id }
        console.log('新增成功，更新本地 ID:', savedInspect.id)
      }

      saveRoute()
    } else {
      // 編輯模式
      console.log('執行編輯模式儲存，ID:', inspectData.id)
      await inspectsStore.saveInspect(inspectData)
      appStore.sendSuccessNotice("保存成功!")
    }
  } catch (error: any) {
    console.error("Error:", error.message)
    appStore.sendErrorNotice("保存失敗: " + error.message)
  } finally {
    // 重置標誌
    console.log('儲存完成，設置 isSaving = false')
    isSaving.value = false
    setTimeout(() => {
      isUpdatingFromStore.value = false
    }, 100)
  }
}

const saveRoute = () => {
  inspectId.value = inspectsStore.inspectId
  router.push(`/inspect/${inspectId.value}`)
}

const getInspectById = () => {
  inspectsStore.getInspectById(route.params.id as string)
}

const getProduct = async () => {
  try {
    const codeName = searchFilter.value.contain.codeName
    const trimmedCodeName = codeName.trim()

    if (!trimmedCodeName) {
      appStore.sendErrorNotice("請輸入Cap QRcode!")
      appStore.closeNoticeWithDelay(5000)
      return ""
    }

    isYarnQRcode.value = /^\d{14}[a-zA-Z0-9\s.]*$/.test(trimmedCodeName)

    if (!isYarnQRcode.value) {
      appStore.sendErrorNotice("無效 Cap QRcode!")
      appStore.closeNoticeWithDelay(5000)
      return ""
    }

    if (isYarnQRcode.value) {
      query.value = trimmedCodeName.slice(0, 14)

      try {
        const isDuplicate = await inspectsStore.getDuplicateInspectlineByCode(query.value)
        if (isDuplicate) {
          appStore.sendErrorNotice("重複 Cap QRcode!")
          appStore.closeNoticeWithDelay(5000)
          return ""
        }
      } catch (error) {
        console.error('檢查重複 QRCode 失敗:', error)
        // 如果檢查重複失敗，繼續執行（可能是網路問題）
      }

      try {
        await productsStore.getProductByQRCode(query.value)

        if (!product.value || product.value.length === 0) {
          appStore.sendErrorNotice("查無資料!")
          appStore.closeNoticeWithDelay(5000)
          return ""
        } else {
          return product.value
        }
      } catch (error) {
        console.error('獲取產品資料失敗:', error)
        if (error.code === 'ECONNABORTED') {
          appStore.sendErrorNotice("網路連線超時，請稍後再試!")
        } else if (error.response?.status === 404) {
          appStore.sendErrorNotice("查無資料!")
        } else {
          appStore.sendErrorNotice("獲取產品資料失敗，請稍後再試!")
        }
        appStore.closeNoticeWithDelay(5000)
        return ""
      }
    }
  } catch (error) {
    console.error('getProduct 執行失敗:', error)
    appStore.sendErrorNotice("系統錯誤，請稍後再試!")
    appStore.closeNoticeWithDelay(5000)
    return ""
  }
}

const cancel = () => {
  router.push({ name: "inspects" })
}

const remove = (item: any) => {
  selectedInspectline.value = item
  dialog.value = true
}

const onConfirm = async () => {
  try {
    await inspectsStore.deleteInspectline(selectedInspectline.value)

    // 確保本地狀態能夠正確更新
    isUpdatingFromStore.value = false

    // 手動觸發本地狀態更新
    const updatedInspect = inspectsStore.currentInspect
    if (updatedInspect) {
      inspect.value = { ...updatedInspect }
      inspect.value.quantity = updatedInspect.inspectlines ? updatedInspect.inspectlines.length : 0
      console.log('明細刪除成功，更新本地狀態:', inspect.value.quantity)
    }

    selectedInspectline.value = null
    dialog.value = false
    appStore.sendSuccessNotice("明細刪除成功!")
  } catch (error) {
    console.error('刪除明細失敗:', error)
    appStore.sendErrorNotice("刪除明細失敗: " + error.message)
  }
}

const onCancel = () => {
  selectedInspectline.value = null
  dialog.value = false
}

const addProduct = async () => {
  // 如果是新增模式且還沒有 ID，先保存主檔
  if (!inspect.value.id || inspect.value.id <= 0) {
    // 先驗證表單
    const { valid } = await validForm.value.validate()

    if (!valid) {
      appStore.sendErrorNotice("請先填寫所有必填欄位!")
      return
    }

    try {
      const inspectData = {
        ...inspect.value,
        shiftName: inspect.value.shiftName,
        employId: inspect.value.employId,
        groupName: inspect.value.groupName,
        typeName: inspect.value.typeName || "YARN",
        classDate: inspect.value.classDate || new Date().toISOString().slice(0, 10)
      }

      await inspectsStore.saveInspect(inspectData)
      // 更新路由到編輯模式
      const newId = inspectsStore.inspectId
      router.replace(`/inspect/${newId}`)
    } catch (error) {
      console.error("保存主檔失敗:", error)
      appStore.sendErrorNotice("請先完成主檔資料並保存")
      return
    }
  }

  addProductModal.value = true
  query.value = ""
  searchFilter.value.contain.codeName = ""
  inspectId.value = inspect.value.id

  // 重置 inspectline 表單
  inspectline.value = {
    inspectId: inspect.value.id,
    remarkId: null,
    categoryId: null
  }

  productsStore.resetState()

  nextTick(() => {
    if (validDetail.value) {
      validDetail.value.validate()
    }
    nextTick(() => {
      if (qrCodeInput.value) {
        qrCodeInput.value.focus()
      }
    })
  })
}

const saveInspectline = async () => {
  const InspectId = { inspectId: inspect.value.id }
  const addInspectline = inspectline.value
  const addProduct = product.value[0]
  const newInspectline = {
    ...InspectId,
    ...addInspectline,
    ...addProduct
  }

  try {
    await inspectsStore.addInspectlineToInspect(newInspectline)

    // 確保本地狀態能夠正確更新
    isUpdatingFromStore.value = false

    // 手動觸發本地狀態更新
    const updatedInspect = inspectsStore.currentInspect
    if (updatedInspect && updatedInspect.inspectlines) {
      inspect.value = { ...updatedInspect }
      inspect.value.quantity = updatedInspect.inspectlines.length
      console.log('明細新增成功，更新本地狀態:', inspect.value.inspectlines.length)
    }

    inspectlineId.value = null
    // 不關閉新增視窗，保持開啟狀態讓用戶可以繼續新增
    // addProductModal.value = false
    resetForm()
    // 只顯示一個成功訊息，移除重複的訊息
    appStore.sendSuccessNotice("明細新增成功!")
  } catch (error) {
    console.error('保存明細失敗:', error)
    appStore.sendErrorNotice("保存明細失敗: " + error.message)
  }
}

const resetForm = () => {
  searchFilter.value.contain.codeName = ""
  inspectline.value = {
    inspectId: inspect.value.id,
    remarkId: null,
    categoryId: null
  }
  productsStore.resetState()
  nextTick(() => {
    if (qrCodeInput.value) {
      qrCodeInput.value.focus()
    }
  })
}

const cancelAddProduct = () => {
  addProductModal.value = false
  query.value = ""
  searchFilter.value.contain.codeName = ""
  productsStore.resetState()
  // 不需要重置 inspectsStore 或重新載入數據，保持當前狀態
}


const closeSnackbar = () => {
  appStore.closeNotice()
}

// 處理明細分頁變更
const updateDetailOptions = (options: any) => {
  console.log('明細分頁選項變更:', options)

  // 更新分頁狀態
  if (detailPagination.value.page !== options.page || detailPagination.value.limit !== options.itemsPerPage) {
    detailPagination.value.page = options.page
    detailPagination.value.limit = options.itemsPerPage
    // 由於明細資料是本地的，不需要重新載入
  }
}

// 生命週期
onMounted(async () => {
  // 載入基礎數據
  await inspectsStore.getEmployees()
  await inspectsStore.getCategories()
  await inspectsStore.getRemarks()

  window.scrollTo(0, 0)

  if (route.params.id) {
    title.value = "QI品檢異常(明細)"
    inspectsStore.resetState()
    await inspectsStore.getInspectById(route.params.id as string)

  } else {
    title.value = "QI品檢異常(新增)"
    // 新增模式：確保完全重置狀態
    inspectsStore.resetState()

    // 設置新的空白資料，不設定預設值
    const newInspect = {
      id: null,
      typeName: type.value,
      classDate: new Date().toISOString().slice(0, 10),
      shiftName: null,  // 不設定預設值
      employId: null,
      groupName: null,  // 不設定預設值
      quantity: 0,
      inspectlines: []
    }

    // 重置本地狀態
    inspect.value = { ...newInspect }
    inspectsStore.setInspect(newInspect)
    inspectsStore.setInspectlines([])

    nextTick(() => {
      if (validForm.value) {
        // 先重置驗證，然後觸發驗證以顯示必填提示
        validForm.value.resetValidation()
        setTimeout(() => {
          validForm.value.validate()
        }, 100)
      }
    })
  }
})
</script>

<style scoped>
/* 自定義分頁按鈕顏色 */
:deep(.colored-pagination .v-data-table-footer .v-btn) {
  color: rgb(33, 150, 243) !important; /* 藍色 */
}

:deep(.colored-pagination .v-data-table-footer .v-btn:hover) {
  background-color: rgba(33, 150, 243, 0.1) !important;
}

:deep(.colored-pagination .v-data-table-footer .v-btn.v-btn--active) {
  background-color: rgb(33, 150, 243) !important;
  color: white !important;
}

:deep(.colored-pagination .v-data-table-footer .v-btn.v-btn--disabled) {
  color: rgba(0, 0, 0, 0.26) !important;
}

/* 分頁選擇器樣式 */
:deep(.colored-pagination .v-data-table-footer .v-select) {
  color: rgb(33, 150, 243) !important;
}

:deep(.colored-pagination .v-data-table-footer .v-select .v-field__input) {
  color: rgb(33, 150, 243) !important;
}
</style>
