<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <span class="text-h3">
              <v-icon class="mr-3" color="primary">mdi-test-tube</v-icon>
              Vue3 表單測試中心
            </span>
          </v-card-title>

          <v-card-text>
            <v-alert type="success" class="mb-6">
              <strong>🎉 Vue3 表單升級完成！</strong><br>
              所有主要表單已成功升級到Vue3 Composition API，可以在此進行統一測試。
            </v-alert>

            <!-- 升級狀態總覽 -->
            <v-row class="mb-6">
              <v-col cols="12">
                <h2 class="mb-4">
                  <v-icon class="mr-2">mdi-chart-pie</v-icon>
                  升級狀態總覽
                </h2>

                <v-row>
                  <v-col cols="12" md="3">
                    <v-card color="success" variant="tonal">
                      <v-card-text class="text-center">
                        <div class="text-h2 font-weight-bold">{{ completedForms.length }}</div>
                        <div class="text-subtitle-1">已完成升級</div>
                      </v-card-text>
                    </v-card>
                  </v-col>

                  <v-col cols="12" md="3">
                    <v-card color="warning" variant="tonal">
                      <v-card-text class="text-center">
                        <div class="text-h2 font-weight-bold">{{ partialForms.length }}</div>
                        <div class="text-subtitle-1">部分升級</div>
                      </v-card-text>
                    </v-card>
                  </v-col>

                  <v-col cols="12" md="3">
                    <v-card color="info" variant="tonal">
                      <v-card-text class="text-center">
                        <div class="text-h2 font-weight-bold">{{ testForms.length }}</div>
                        <div class="text-subtitle-1">測試版本</div>
                      </v-card-text>
                    </v-card>
                  </v-col>

                  <v-col cols="12" md="3">
                    <v-card color="primary" variant="tonal">
                      <v-card-text class="text-center">
                        <div class="text-h2 font-weight-bold">{{ totalForms }}</div>
                        <div class="text-subtitle-1">總計表單</div>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>

            <!-- 已完成升級的表單 -->
            <v-row class="mb-6">
              <v-col cols="12">
                <h2 class="mb-4">
                  <v-icon class="mr-2" color="success">mdi-check-circle</v-icon>
                  已完成升級的表單
                </h2>

                <v-row>
                  <v-col cols="12" md="6" lg="4" v-for="form in completedForms" :key="form.name">
                    <v-card variant="outlined" class="form-card">
                      <v-card-title>
                        <v-icon :color="form.color" class="mr-2">{{ form.icon }}</v-icon>
                        {{ form.name }}
                      </v-card-title>

                      <v-card-text>
                        <p class="text-body-2 mb-3">{{ form.description }}</p>

                        <v-chip-group>
                          <v-chip size="small" color="success" v-for="feature in form.features" :key="feature">
                            {{ feature }}
                          </v-chip>
                        </v-chip-group>
                      </v-card-text>

                      <v-card-actions>
                        <v-btn
                          color="primary"
                          variant="text"
                          @click="navigateToForm(form.newPath)"
                          prepend-icon="mdi-plus"
                          size="small"
                        >
                          測試新增
                        </v-btn>

                        <v-btn
                          color="secondary"
                          variant="text"
                          @click="navigateToForm(form.editPath)"
                          prepend-icon="mdi-pencil"
                          size="small"
                        >
                          測試編輯
                        </v-btn>

                        <v-spacer />

                        <v-chip size="small" :color="form.statusColor">
                          {{ form.status }}
                        </v-chip>
                      </v-card-actions>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>

            <!-- 部分升級的表單 -->
            <v-row class="mb-6">
              <v-col cols="12">
                <h2 class="mb-4">
                  <v-icon class="mr-2" color="warning">mdi-progress-wrench</v-icon>
                  部分升級的表單
                </h2>

                <v-row>
                  <v-col cols="12" md="6" lg="4" v-for="form in partialForms" :key="form.name">
                    <v-card variant="outlined" class="form-card">
                      <v-card-title>
                        <v-icon :color="form.color" class="mr-2">{{ form.icon }}</v-icon>
                        {{ form.name }}
                      </v-card-title>

                      <v-card-text>
                        <p class="text-body-2 mb-3">{{ form.description }}</p>

                        <v-alert type="warning" density="compact" class="mb-3">
                          {{ form.note }}
                        </v-alert>

                        <v-chip-group>
                          <v-chip size="small" color="warning" v-for="feature in form.features" :key="feature">
                            {{ feature }}
                          </v-chip>
                        </v-chip-group>
                      </v-card-text>

                      <v-card-actions>
                        <v-btn
                          color="primary"
                          variant="text"
                          @click="navigateToForm(form.newPath)"
                          prepend-icon="mdi-plus"
                          size="small"
                        >
                          測試新增
                        </v-btn>

                        <v-btn
                          color="secondary"
                          variant="text"
                          @click="navigateToForm(form.editPath)"
                          prepend-icon="mdi-pencil"
                          size="small"
                        >
                          測試編輯
                        </v-btn>

                        <v-spacer />

                        <v-chip size="small" :color="form.statusColor">
                          {{ form.status }}
                        </v-chip>
                      </v-card-actions>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>

            <!-- 測試版本表單 -->
            <v-row class="mb-6">
              <v-col cols="12">
                <h2 class="mb-4">
                  <v-icon class="mr-2" color="info">mdi-flask</v-icon>
                  測試版本表單
                </h2>

                <v-row>
                  <v-col cols="12" md="6" lg="4" v-for="form in testForms" :key="form.name">
                    <v-card variant="outlined" class="form-card">
                      <v-card-title>
                        <v-icon :color="form.color" class="mr-2">{{ form.icon }}</v-icon>
                        {{ form.name }}
                      </v-card-title>

                      <v-card-text>
                        <p class="text-body-2 mb-3">{{ form.description }}</p>

                        <v-chip-group>
                          <v-chip size="small" color="info" v-for="feature in form.features" :key="feature">
                            {{ feature }}
                          </v-chip>
                        </v-chip-group>
                      </v-card-text>

                      <v-card-actions>
                        <v-btn
                          color="primary"
                          variant="text"
                          @click="navigateToForm(form.newPath)"
                          prepend-icon="mdi-test-tube"
                          size="small"
                        >
                          測試功能
                        </v-btn>

                        <v-spacer />

                        <v-chip size="small" :color="form.statusColor">
                          {{ form.status }}
                        </v-chip>
                      </v-card-actions>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>

            <!-- 快速導航 -->
            <v-row>
              <v-col cols="12">
                <h2 class="mb-4">
                  <v-icon class="mr-2">mdi-navigation</v-icon>
                  快速導航
                </h2>

                <v-card variant="outlined">
                  <v-card-text>
                    <v-btn-group divided>
                      <v-btn
                        color="primary"
                        @click="navigateToForm('/vue3-test')"
                        prepend-icon="mdi-home"
                      >
                        基礎測試頁面
                      </v-btn>

                      <v-btn
                        color="success"
                        @click="navigateToForm('/test-vue3/inspects/new')"
                        prepend-icon="mdi-clipboard-check"
                      >
                        品質檢驗記錄
                      </v-btn>

                      <v-btn
                        color="warning"
                        @click="navigateToForm('/test-vue3/downgrades/new')"
                        prepend-icon="mdi-arrow-down-bold"
                      >
                        降級表單
                      </v-btn>

                      <v-btn
                        color="info"
                        @click="navigateToForm('/downgrades')"
                        prepend-icon="mdi-format-list-bulleted"
                      >
                        降級列表
                      </v-btn>

                      <v-btn
                        color="indigo"
                        @click="navigateToForm('/test-vue3/em-production-query')"
                        prepend-icon="mdi-note-multiple-outline"
                      >
                        EM即時生產查詢
                      </v-btn>

                      <v-btn
                        color="green"
                        @click="navigateToForm('/test-vue3/em-production-query-android')"
                        prepend-icon="mdi-android"
                      >
                        EM查詢 (Android版)
                      </v-btn>

                      <v-btn
                        color="purple"
                        @click="navigateToForm('/test-vue3/em-production-query-config')"
                        prepend-icon="mdi-cog"
                      >
                        EM查詢 (配置版)
                      </v-btn>

                      <v-btn
                        color="indigo"
                        @click="navigateToForm('/navigation-demo')"
                        prepend-icon="mdi-navigation"
                      >
                        導航系統演示
                      </v-btn>
                    </v-btn-group>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 成功訊息 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          關閉
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

// 已完成升級的表單
const completedForms = ref([
  {
    name: '品質檢驗記錄表單',
    description: '一般品質檢驗記錄的主檔明細表單，支援完整的CRUD操作',
    icon: 'mdi-clipboard-check',
    color: 'success',
    status: '完全升級',
    statusColor: 'success',
    features: ['Vue3 Composition API', '主檔明細關聯', '即時數據顯示', '表單驗證'],
    newPath: '/test-vue3/inspects/new',
    editPath: '/test-vue3/inspect/1'
  },
  {
    name: 'Downgrade T2表單',
    description: 'Downgrade T2記錄的主檔明細表單，包含完整的明細檔管理',
    icon: 'mdi-arrow-down-bold',
    color: 'warning',
    status: '完全升級',
    statusColor: 'success',
    features: ['Vue3 Composition API', '明細檔CRUD', '響應式UI', '數據驗證'],
    newPath: '/test-vue3/downgrades/new',
    editPath: '/test-vue3/downgrade/1'
  },
  {
    name: 'Yarn盤點表單',
    description: 'Yarn盤點記錄的主檔明細表單，支援完整的盤點功能',
    icon: 'mdi-package-variant',
    color: 'blue',
    status: '完全升級',
    statusColor: 'success',
    features: ['Vue3 Composition API', '盤點明細管理', '即時數據顯示', '表單驗證'],
    newPath: '/test-vue3/pinventory-yarn/new',
    editPath: '/test-vue3/pinventory-yarn/1'
  },
  {
    name: 'Disposal Yarn表單',
    description: '報廢異常記錄的主檔明細表單，支援完整的報廢管理功能',
    icon: 'mdi-delete',
    color: 'error',
    status: '完全升級',
    statusColor: 'success',
    features: ['Vue3 Composition API', '報廢明細管理', '即時數據顯示', '表單驗證'],
    newPath: '/test-vue3/disposals/new',
    editPath: '/test-vue3/disposal/1'
  },
  {
    name: 'Relabel NO MFD表單',
    description: 'Relabel NO MFD記錄的主檔明細表單，支援完整的標籤重貼功能',
    icon: 'mdi-label',
    color: 'info',
    status: '完全升級',
    statusColor: 'success',
    features: ['Vue3 Composition API', 'Relabel明細管理', '即時數據顯示', '表單驗證'],
    newPath: '/test-vue3/relabels/new',
    editPath: '/test-vue3/relabel/1'
  },
  {
    name: 'QI品檢計數作業',
    description: 'QI品檢計數作業表單，支援品檢計數和合格率計算功能',
    icon: 'mdi-counter',
    color: 'teal',
    status: '完全升級',
    statusColor: 'success',
    features: ['Vue3 Composition API', '計數功能', '合格率計算', '表單驗證'],
    newPath: '/test-vue3/icounts/new',
    editPath: '/test-vue3/icount/1'
  },
  {
    name: 'Cake盤點表單',
    description: 'Cake盤點記錄的主檔明細表單，支援完整的Cake盤點功能',
    icon: 'mdi-cake',
    color: 'orange',
    status: '完全升級',
    statusColor: 'success',
    features: ['Vue3 Composition API', 'Cake盤點明細管理', '即時數據顯示', '表單驗證'],
    newPath: '/test-vue3/pinventory-cake/new',
    editPath: '/test-vue3/pinventory-cake/1'
  },
  {
    name: 'Pack盤點表單',
    description: 'Pack盤點記錄的主檔明細表單，支援完整的Pack盤點功能',
    icon: 'mdi-package',
    color: 'brown',
    status: '完全升級',
    statusColor: 'success',
    features: ['Vue3 Composition API', 'Pack盤點明細管理', '即時數據顯示', '表單驗證'],
    newPath: '/test-vue3/pinventory-pack/new',
    editPath: '/test-vue3/pinventory-pack/1'
  },
  {
    name: 'QI品檢異常登錄',
    description: 'QI品檢異常登錄表單，支援異常記錄和矯正措施管理功能',
    icon: 'mdi-alert-circle',
    color: 'red',
    status: '完全升級',
    statusColor: 'success',
    features: ['Vue3 Composition API', '異常明細管理', '嚴重程度分級', '狀態追蹤'],
    newPath: '/test-vue3/qi-abnormals/new',
    editPath: '/test-vue3/qi-abnormal/1'
  },
  {
    name: 'EM即時生產查詢',
    description: 'EM即時生產查詢Vue3完整版，支援多種QRCode類型的智能識別和即時查詢功能',
    icon: 'mdi-note-multiple-outline',
    color: 'indigo',
    status: '完全升級',
    statusColor: 'success',
    features: ['Vue3 Composition API', '智能QRCode識別', '動態表格', '即時查詢', '多類型支援'],
    newPath: '/test-vue3/em-production-query',
    editPath: '/test-vue3/em-production-query'
  },
  {
    name: 'EM即時生產查詢 (Android版)',
    description: 'EM即時生產查詢Android優化版，專為移動設備設計的響應式layout和觸摸優化',
    icon: 'mdi-android',
    color: 'green',
    status: '完全升級',
    statusColor: 'success',
    features: ['Android優化', '響應式設計', '觸摸友好', '移動端卡片列表', '設備檢測'],
    newPath: '/test-vue3/em-production-query-android',
    editPath: '/test-vue3/em-production-query-android'
  },
  {
    name: 'EM即時生產查詢 (配置版)',
    description: 'EM即時生產查詢統一配置版，使用Android Layout配置系統，無需逐一修改表單',
    icon: 'mdi-cog',
    color: 'purple',
    status: '完全升級',
    statusColor: 'success',
    features: ['統一配置', '即插即用', '組件化設計', '配置驅動', '快速部署'],
    newPath: '/test-vue3/em-production-query-config',
    editPath: '/test-vue3/em-production-query-config'
  },

])

// 部分升級的表單
const partialForms = ref([
])

// 測試版本表單
const testForms = ref([
  {
    name: '降級測試頁面',
    description: '用於測試降級表單路由和基本功能的測試頁面',
    icon: 'mdi-test-tube',
    color: 'purple',
    status: '測試版',
    statusColor: 'info',
    features: ['路由測試', '基本結構', '狀態顯示'],
    newPath: '/test-vue3/downgrade-test/new'
  }
])

// 計算屬性
const totalForms = computed(() =>
  completedForms.value.length + partialForms.value.length + testForms.value.length
)

// 方法
const navigateToForm = (path: string) => {
  try {
    router.push(path)
    showMessage(`正在導航到: ${path}`, 'info')
  } catch (error) {
    showMessage('導航失敗，請檢查路由配置', 'error')
  }
}

const showMessage = (message: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}
</script>

<style scoped>
.form-card {
  height: 100%;
  transition: all 0.3s ease;
}

.form-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.12);
}

.v-chip-group {
  margin-top: 8px;
}

.v-btn-group {
  width: 100%;
}
</style>
