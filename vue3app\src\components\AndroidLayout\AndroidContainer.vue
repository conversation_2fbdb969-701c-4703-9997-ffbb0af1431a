<template>
  <v-container 
    :fluid="fluid"
    :class="containerClass"
  >
    <slot />
    
    <!-- 設備信息浮動按鈕 -->
    <v-fab
      v-if="showFab && isMobile"
      :icon="showDevInfo ? 'mdi-information' : 'mdi-information-outline'"
      :color="fabColor"
      :size="fabSize"
      @click="toggleDeviceInfo"
      :style="fabStyle"
    />
    
    <!-- 設備信息面板 -->
    <v-row v-if="showDevInfo && showDeviceInfoPanel" class="mt-4">
      <v-col cols="12">
        <v-card :class="cardClass" variant="outlined">
          <v-card-title class="text-h6">
            <v-icon class="mr-2">mdi-android</v-icon>
            設備信息
          </v-card-title>
          
          <v-card-text>
            <v-list density="compact">
              <v-list-item>
                <template v-slot:prepend>
                  <v-icon :color="isMobile ? 'success' : 'primary'">
                    {{ isMobile ? 'mdi-cellphone' : 'mdi-desktop-classic' }}
                  </v-icon>
                </template>
                <v-list-item-title>設備類型</v-list-item-title>
                <v-list-item-subtitle>
                  {{ deviceType }} ({{ currentBreakpoint }})
                </v-list-item-subtitle>
              </v-list-item>
              
              <v-list-item>
                <template v-slot:prepend>
                  <v-icon color="info">mdi-monitor</v-icon>
                </template>
                <v-list-item-title>螢幕尺寸</v-list-item-title>
                <v-list-item-subtitle>
                  {{ screenWidth }} × {{ screenHeight }}
                </v-list-item-subtitle>
              </v-list-item>
              
              <v-list-item>
                <template v-slot:prepend>
                  <v-icon :color="touchSupported ? 'success' : 'warning'">
                    {{ touchSupported ? 'mdi-gesture-tap' : 'mdi-mouse' }}
                  </v-icon>
                </template>
                <v-list-item-title>觸摸支援</v-list-item-title>
                <v-list-item-subtitle>
                  {{ touchSupported ? '支援觸摸操作' : '僅支援滑鼠操作' }}
                </v-list-item-subtitle>
              </v-list-item>
              
              <v-list-item v-if="showConfigInfo">
                <template v-slot:prepend>
                  <v-icon color="purple">mdi-cog</v-icon>
                </template>
                <v-list-item-title>Android優化</v-list-item-title>
                <v-list-item-subtitle>
                  {{ config.enabled ? '已啟用' : '已停用' }}
                </v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAndroidLayout, type UseAndroidLayoutOptions } from '@/composables/useAndroidLayout'

interface Props {
  // 容器屬性
  fluid?: boolean
  
  // Android Layout選項
  androidOptions?: UseAndroidLayoutOptions
  
  // FAB設定
  showFab?: boolean
  fabColor?: string
  fabSize?: 'x-small' | 'small' | 'default' | 'large' | 'x-large'
  fabPosition?: {
    bottom?: string
    right?: string
    left?: string
    top?: string
  }
  
  // 設備信息面板
  showDeviceInfoPanel?: boolean
  showConfigInfo?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  fluid: true,
  showFab: true,
  fabColor: 'primary',
  fabSize: 'small',
  fabPosition: () => ({ bottom: '80px', right: '16px' }),
  showDeviceInfoPanel: true,
  showConfigInfo: false
})

// 使用Android Layout composable
const {
  config,
  isMobile,
  isTablet,
  isDesktop,
  deviceType,
  currentBreakpoint,
  screenWidth,
  screenHeight,
  touchSupported,
  showDevInfo,
  toggleDeviceInfo,
  containerClass,
  cardClass
} = useAndroidLayout(props.androidOptions)

// 計算FAB樣式
const fabStyle = computed(() => {
  const style: Record<string, string> = {
    position: 'fixed',
    zIndex: '1005'
  }
  
  Object.entries(props.fabPosition).forEach(([key, value]) => {
    if (value) {
      style[key] = value
    }
  })
  
  return style
})
</script>

<style scoped>
/* 容器特定樣式 */
.v-container {
  position: relative;
}

/* FAB定位 */
.v-fab {
  position: fixed !important;
  z-index: 1005 !important;
}

/* 設備信息面板動畫 */
.v-card {
  transition: all 0.3s ease;
}

/* 安全區域適配 */
@supports (padding: max(0px)) {
  .v-container {
    padding-bottom: max(16px, env(safe-area-inset-bottom)) !important;
  }
}
</style>
