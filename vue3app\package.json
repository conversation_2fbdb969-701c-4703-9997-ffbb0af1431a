{"name": "vue3app", "version": "3.0.0", "private": true, "scripts": {"start": "vite", "dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "cypress open", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@mdi/font": "^7.4.47", "axios": "^1.7.7", "chart.js": "^4.4.6", "core-js": "^3.39.0", "lodash": "^4.17.21", "moment": "^2.30.1", "pinia": "^2.2.8", "register-service-worker": "^1.7.2", "vite-plugin-vuetify": "^2.0.4", "vue": "^3.5.13", "vue-chartjs": "^5.3.1", "vue-facing-decorator": "^4.0.1", "vue-router": "^4.5.0", "vuetify": "^3.7.3"}, "devDependencies": {"@types/lodash": "^4.17.13", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "@vitejs/plugin-vue": "^5.2.1", "@vitest/coverage-v8": "^2.1.9", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.20", "cypress": "^13.17.0", "eslint": "^9.17.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.32.0", "jsdom": "^26.1.0", "postcss": "^8.5.1", "prettier": "^3.4.2", "sass": "^1.82.0", "typescript": "~5.7.2", "vite": "^6.0.5", "vitest": "^2.1.8", "vue-tsc": "^2.1.10"}}