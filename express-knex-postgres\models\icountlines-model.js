const db = require("../config/dbConfig.js");
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET SPECIFIC ICOUNTLINE BY ICOUNTID
const findById = id => {
  return db("aits_icountlines")
    .select({
    id: "aits_icountlines.id",
    icountId: "aits_icountlines.icountid",
    tracksheetNO: "aits_icountlines.tracksheetno",
    documentNO: "aits_icountlines.documentno",
    furnaceName: "aits_icountlines.furnacename",
    productName: "aits_icountlines.productname",
    twisterNO: "m_twqrcode.mach_tmis_no",
    tracksheetTime: "m_twqrcode.created",
    icountT1Qty: "aits_icountlines.icountt1qty",
    icountT2Qty: "aits_icountlines.icountt2qty",
    created: "aits_icountlines.created"
    })
    .innerJoin('m_twqrcode', 'm_twqrcode.m_twqrcode_id', 'aits_icountlines.m_twqrcode_id')
    .where("aits_icountlines.icountid", id)
    .orderBy("id", "desc") 
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

// GET SPECIFIC COUNTLINE BY CODE
const findByCode = code => {
  return db("aits_icountlines")
    .select({
    tracksheetno: "aits_icountlines.tracksheetno"    
    }) 
    .where(db.raw("TRIM(aits_icountlines.tracksheetno)"), code)
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
  });
};

// ADD A ICOUNTLINE
const addICountline = icountline => {
  return db.transaction(trx => {
    return db("aits_icountlines")
      .insert({
        icountid: icountline.icountId,
        tracksheetno: icountline.tracksheetNO,
        documentno: icountline.documentNO,
        furnacename: icountline.furnaceName,
        productname: icountline.productName,
        twisterno : icountline.twisterNO,
        icountt1qty: icountline.icountT1Qty,
        icountt2qty: icountline.icountT2Qty,
        m_product_id: icountline.m_product_id,
        m_twqrcode_id: icountline.m_twqrcode_id,
        created: db.fn.now()
      }, "id")
      .then(trx.commit)
      .catch(trx.rollback);
  })
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

// REMOVE ICOUNTLINE
const removeICountline = id => {  
  let result;
  return db.transaction(trx => {
    return db("aits_icountlines")
      .where("id", id)
      .then(icountline => {
        result = icountline;     
        if (icountline) { 
          return trx("aits_icountlines") 
            .where("aits_icountlines.id", id) 
            .del();
        }
      })
      .then(trx.commit)
      .catch(trx.rollback);
  })
  .then(() => { 
    infoLogger.info(`remove icountline content: ${JSON.stringify(result)}`)
  })
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

module.exports = {
  findById,
  findByCode,
  addICountline,
  removeICountline
};
