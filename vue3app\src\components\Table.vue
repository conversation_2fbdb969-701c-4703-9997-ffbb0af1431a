<template>
  <div>
    <v-card-text>
      <v-text-field
        v-if="setSearch"
        v-model="search"
        append-icon="mdi-magnify"
        label="Search"
        single-line
        hide-details
      ></v-text-field>
    </v-card-text>
    <v-data-table
      class="elevation-1"
      :search="search"
      :headers="headers"
      :items="items"
      v-model:page="pagination.page"
      :items-per-page="pagination.rowsPerPage"
      :disable-sort="disableSort"
      hide-default-footer
    >
      <template #[`item.count`]="{ index }">
        {{(pagination.page - 1) * pagination.rowsPerPage + index + 1}}
      </template>

      <template #[`item.countdown`]="{ index }">
        {{ pagination.totalItems - ((pagination.page - 1) * pagination.rowsPerPage + index) }}
      </template>

      <template #[`item.avatar`]="{ item }">
        <v-img
          v-if="(item as any).avatar !== null"
          small
          max-width="2em"
          :src="(item as any).avatar"
          class="avatar"
          :srcset="(item as any).avatar"
        />
      </template>

      <template #[`item.membership`]="{ item }">
        <v-icon v-if="(item as any).membership === true">
          mdi-checkbox-marked-circle-outline
        </v-icon>
        <v-icon v-if="(item as any).membership === false">
          mdi-close-circle-outline
        </v-icon>
      </template>
      <template #[`item.actions`]="{ item }">
        <v-btn
          v-if="setEdit"
          elevation="4"
          fab
          class="teal mr-2"
          small
          dark
          @click="$emit('edit', item)"
        >
          <v-icon>
            mdi-pencil
          </v-icon>
        </v-btn>
        <v-btn
          v-if="setRemove"
          elevation="4"
          fab
          class="cyan"
          small
          @click="$emit('remove', item)"
        >
          <v-icon>
            mdi-trash-can-outline
          </v-icon>
        </v-btn>
      </template>
      <template #no-data>
        <span>
          <p class="pt-2 blue--text subheading">
            <v-icon medium class="blue--text">mdi-info</v-icon>Sorry, nothing to
            display here :(
          </p>
        </span>
      </template>
    </v-data-table>
    <div class="text-xs-center pt-2" v-if="isNotEmpty">
      <v-pagination
        v-model="pagination.page"
        :length="pagination.pages"
        :total-visible="5"
        circle
      ></v-pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Entity } from '@/types'

interface Header {
  key: string
  title: string
  value?: string
  align?: 'start' | 'center' | 'end'
  sortable?: boolean
  width?: string | number
}

interface Pagination {
  page: number
  rowsPerPage: number
  pages: number
  totalItems: number
}

interface Props {
  headers: Header[]
  items: Entity[]
  pagination: Pagination
  setSearch?: boolean
  setEdit?: boolean
  setRemove?: boolean
  disableSort?: boolean
}

interface Emits {
  (e: 'edit', item: Entity): void
  (e: 'remove', item: Entity): void
}

const props = withDefaults(defineProps<Props>(), {
  setSearch: false,
  setEdit: false,
  setRemove: false,
  disableSort: false
})

const emit = defineEmits<Emits>()

const search = ref('')

const isNotEmpty = computed((): boolean => {
  return props.items && props.items.length > 0
})

const hasHeader = (header: Header): boolean => {
  return header?.value ? true : false
}
</script>
