<template>
  <v-container fluid>
    <v-card class="grey lighten-4 elevation-0">
      <v-form ref="validForm" v-model="formValid">
        <v-card-title class="title">
          {{ title }}
          <v-spacer></v-spacer>
          <v-btn
            :elevation="4"
            icon
            size="small"
            color="grey"
            class="mr-2"
            @click="cancel()"
          >
            <v-icon>mdi-close-circle-outline</v-icon>
          </v-btn>
          <v-btn
            :elevation="4"
            icon
            size="small"
            color="purple"
            class="mr-2"
            @click="save()"
            :disabled="isSaving || !formValid"
            :loading="isSaving"
          >
            <v-icon>mdi-content-save-all</v-icon>
          </v-btn>
          <v-btn
            :elevation="4"
            icon
            size="small"
            color="blue"
            @click="addProduct()"
          >
            <v-icon>mdi-plus</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-container fluid grid-list-md>
            <v-row>
              <v-col md="4" cols="12">
                <v-text-field
                  name="id"
                  label="單號"
                  type="number"
                  hint="DisposalID is required"
                  v-model="disposal.id"
                  class="input-group--focused"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col md="4" cols="12">
                <v-menu
                  :close-on-content-click="false"
                  v-model="classDateMenu"
                  transition="v-scale-transition"
                  offset-y
                  :nudge-left="40"
                  max-width="290px"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      v-bind="props"
                      label="日期"
                      v-model="disposal.classDate"
                      prepend-icon="mdi-calendar"
                      readonly
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="disposal.classDate"
                    no-title
                    scrollable
                  >
                  </v-date-picker>
                </v-menu>
              </v-col>
              <v-col md="4" cols="12">
                <v-radio-group
                  name="shiftName"
                  label="勤別"
                  v-model="disposal.shiftName"
                  :rules="[value => !!value || '必要!!請選擇']"
                  required
                  row
                >
                  <v-radio label="I" value="1"></v-radio>
                  <v-radio label="II" value="2"></v-radio>
                  <v-radio label="III" value="3"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col md="6" cols="12">
                <v-autocomplete
                  :items="employees"
                  label="人員"
                  item-title="employName"
                  item-value="employId"
                  v-model="disposal.employId"
                  :rules="[value => !!value || '必要!!請選擇']"
                  required
                ></v-autocomplete>
              </v-col>
              <v-col md="4" cols="12">
                <v-radio-group
                  name="groupName"
                  label="組別"
                  v-model="disposal.groupName"
                  :rules="[value => !!value || '必要!!請選擇']"
                  required
                  row
                >
                  <v-radio label="A" value="A"></v-radio>
                  <v-radio label="B" value="B"></v-radio>
                  <v-radio label="C" value="C"></v-radio>
                  <v-radio label="D" value="D"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col md="4" cols="12">
                <v-text-field
                  name="quantity"
                  label="小計"
                  type="number"
                  v-model="disposal.quantity"
                  class="input-group--focused"
                  readonly
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-card>
                  <v-data-table
                    v-if="loading === false"
                    :headers="headers"
                    :items="disposallinesWithIndex"
                    :items-per-page="10"
                    class="elevation-1 colored-pagination"
                  >
                    <template v-slot:item.countdown="{ index }">
                      {{ index + 1 }}
                    </template>
                    <template v-slot:item.actions="{ item }">
                      <v-btn
                        :elevation="4"
                        icon
                        size="x-small"
                        color="red"
                        @click="remove(item)"
                      >
                        <v-icon>mdi-delete</v-icon>
                      </v-btn>
                    </template>
                  </v-data-table>
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
      </v-form>
    </v-card>

    <!-- 明細編輯對話框 -->
    <v-dialog v-model="addProductModal" width="700" persistent>
      <v-form ref="validDetail" v-model="detailValid">
        <v-card>
          <v-card-title>
            {{ title }}
            <v-spacer></v-spacer>
            <v-card-actions>
              <v-btn
                class="green lighten-1"
                text
                :disabled="!detailValid"
                @click="saveDisposalline"
              >
                Confirm
              </v-btn>
              <v-btn
                class="orange lighten-1"
                text
                @click="cancelAddProduct"
              >
                Cancel
              </v-btn>
            </v-card-actions>
          </v-card-title>
          <v-card-text>
            1.請掃Cap QRCode 2.選擇原因
            <v-text-field
              ref="qrCodeInput"
              v-model="searchFilter.contain.codeName"
              append-icon="mdi-magnify"
              label="Cap QRCode"
              @change="getProduct"
              :counter="14"
              :rules="[value => !!value || '必要!!請選擇']"
              required
            ></v-text-field>
            <v-container fluid grid-list-md>
              <v-row>
                <v-col md="6" cols="12">
                  <v-autocomplete
                    :items="remarks"
                    label="原因"
                    v-model="disposalline.remarkId"
                    item-title="remarkName"
                    item-value="remarkId"
                    :rules="[value => !!value || '必要!!請選擇']"
                    required
                  ></v-autocomplete>
                </v-col>
                <v-col
                  md="6"
                  cols="12"
                  v-for="(item, index) in product"
                  :key="index"
                >
                  <v-text-field
                    v-model="item.productName"
                    label="品種"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.gradeName"
                    label="定長別"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.workDate"
                    label="開機日期"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.twisterNO"
                    label="Twister NO"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.spindleNO"
                    label="Spindle NO"
                    readonly
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-container>
          </v-card-text>
        </v-card>
      </v-form>
    </v-dialog>

    <!-- 確認對話框 -->
    <v-dialog v-model="dialog" max-width="400">
      <v-card>
        <v-card-title>{{ dialogTitle }}</v-card-title>
        <v-card-text>{{ dialogText }}</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey" variant="text" @click="onCancel">取消</v-btn>
          <v-btn color="error" variant="text" @click="onConfirm">確認</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 訊息提示 -->
    <v-snackbar
      v-if="loading === false"
      :top="true"
      :right="true"
      :timeout="5000"
      :color="mode"
      v-model="snackbar"
    >
      <div class="text-center">
        {{ notice }}
      </div>
      <template v-slot:actions>
        <v-btn dark text @click="closeSnackbar">Close</v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDisposalsStore } from '@/stores/disposals'
import { useProductsStore } from '@/stores/products'
import { useAppStore } from '@/stores/app'

const route = useRoute()
const router = useRouter()

// Store 實例
const disposalsStore = useDisposalsStore()
const productsStore = useProductsStore()
const appStore = useAppStore()

// 響應式數據
const validForm = ref()
const validDetail = ref()
const qrCodeInput = ref()
const formValid = ref(false)
const detailValid = ref(false)

// Vue2 風格的數據結構
const title = ref("")
const type = ref("DISPOSAL")
const disposalId = ref(0)
const disposallineId = ref(null)
const categoryId = ref("1000204")
const remarkId = ref(0)
const selectedDisposalline = ref(null)
const isYarnQRcode = ref(null)
const query = ref("")
const classDateMenu = ref(false)
const addProductModal = ref(false)
const dialog = ref(false)
const dialogTitle = ref("Disposal Yarn(明細)刪除確認")
const dialogText = ref("刪除該筆記錄?")
const isSaving = ref(false)  // 防止重複儲存
const lastSaveTime = ref(0)  // 記錄上次儲存時間，防止短時間內重複儲存

// 明細分頁狀態
const detailPagination = ref({
  page: 1,
  limit: 10,
  totalItems: 0,
  totalPages: 0,
  hasNextPage: false,
  hasPrevPage: false
})

// 表格標題
const headers = ref([
  { title: "序號", key: "countdown", align: "start" as const },
  { title: "品種", key: "productName", align: "start" as const },
  { title: "確認", key: "categoryName" },
  { title: "原因", key: "remarkName" },
  { title: "開機日期", key: "workDate", align: "start" as const },
  { title: "Twister NO", key: "twisterNO" },
  { title: "Spindle NO", key: "spindleNO" },
  { title: "", key: "actions", sortable: false }
])

const searchFilter = ref({ contain: { codeName: "" } })

// 計算屬性 - 從 store 獲取數據
const employees = computed(() => disposalsStore.employees)

// 使用本地響應式數據來避免直接修改 store 的計算屬性
const disposal = ref({} as any)

// 監聽 store 的變化並同步到本地
const storeDisposal = computed(() => disposalsStore.currentDisposal)
const isUpdatingFromStore = ref(false)

watch(storeDisposal, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0 && !isUpdatingFromStore.value) {
    // 創建深拷貝以避免響應性問題
    disposal.value = JSON.parse(JSON.stringify(newValue))
    // 計算個數（明細檔數量）
    if (disposal.value.disposallines) {
      disposal.value.quantity = disposal.value.disposallines.length
    }
  }
}, { immediate: true, deep: true })

const categories = computed(() => disposalsStore.categories)
const remarks = computed(() => disposalsStore.remarks)
const disposallinesWithIndex = computed(() => {
  if (disposal.value && disposal.value.disposallines) {
    return disposal.value.disposallines.map((item, index) => ({
      ...item,
      countdown: index + 1
    }))
  }
  return []
})
const disposalline = ref({
  disposalId: 0,
  remarkId: null,
  categoryId: null
})
const product = computed(() => productsStore.products)
const loading = computed(() => appStore.loading)
const mode = computed(() => appStore.mode)
const snackbar = computed(() => appStore.snackbar)
const notice = computed(() => appStore.notice)

// 方法
const save = async () => {
  const currentTime = Date.now()

  // 如果正在儲存中，直接返回
  if (isSaving.value) {
    console.log('正在儲存中，忽略重複點擊')
    appStore.sendErrorNotice("正在儲存中，請稍候...")
    return
  }

  // 防止短時間內重複點擊（1秒內）
  if (currentTime - lastSaveTime.value < 1000) {
    console.log('短時間內重複點擊，忽略')
    appStore.sendErrorNotice("請勿重複點擊儲存按鈕")
    return
  }

  // 先驗證表單
  const { valid } = await validForm.value.validate()

  if (!valid) {
    appStore.sendErrorNotice("請填寫所有必填欄位!")
    return
  }

  // 檢查必填欄位
  if (!disposal.value.shiftName || !disposal.value.employId || !disposal.value.groupName) {
    appStore.sendErrorNotice("請填寫所有必填欄位!")
    return
  }

  // 設置儲存中狀態和時間戳
  isSaving.value = true
  lastSaveTime.value = currentTime
  console.log('開始儲存，設置 isSaving = true')

  // 準備儲存資料，不自動填入預設值
  const disposalData = {
    ...disposal.value,
    typeName: disposal.value.typeName || "DISPOSAL",
    classDate: disposal.value.classDate || new Date().toISOString().slice(0, 10)
  }

  console.log('準備保存的資料:', disposalData)

  // 設置標誌防止 watch 覆蓋用戶變更
  isUpdatingFromStore.value = true

  try {
    if (!disposalData.id || disposalData.id <= 0) {
      // 新增模式
      console.log('執行新增模式儲存')
      await disposalsStore.saveDisposal(disposalData)

      // 儲存成功後，更新本地的 disposal 資料，包含新的 ID
      const savedDisposal = disposalsStore.currentDisposal
      if (savedDisposal && savedDisposal.id) {
        disposal.value = { ...disposal.value, id: savedDisposal.id }
        console.log('新增成功，更新本地 ID:', savedDisposal.id)
      }

      saveRoute()
    } else {
      // 編輯模式
      console.log('執行編輯模式儲存，ID:', disposalData.id)
      await disposalsStore.saveDisposal(disposalData)
      appStore.sendSuccessNotice("保存成功!")
    }
  } catch (error: any) {
    console.error("Error:", error.message)
    appStore.sendErrorNotice("保存失敗: " + error.message)
  } finally {
    // 重置標誌
    console.log('儲存完成，設置 isSaving = false')
    isSaving.value = false
    setTimeout(() => {
      isUpdatingFromStore.value = false
    }, 100)
  }
}

const saveRoute = () => {
  disposalId.value = disposalsStore.disposalId
  router.push(`/disposal/${disposalId.value}`)
}

const getDisposalById = () => {
  disposalsStore.getDisposalById(route.params.id as string)
}

const getProduct = async () => {
  try {
    const codeName = searchFilter.value.contain.codeName
    const trimmedCodeName = codeName.trim()

    if (!trimmedCodeName) {
      appStore.sendErrorNotice("請輸入Cap QRcode!")
      appStore.closeNoticeWithDelay(5000)
      return ""
    }

    isYarnQRcode.value = /^\d{14}[a-zA-Z0-9\s.]*$/.test(trimmedCodeName)

    if (!isYarnQRcode.value) {
      appStore.sendErrorNotice("無效 Cap QRcode!")
      appStore.closeNoticeWithDelay(5000)
      return ""
    }

    if (isYarnQRcode.value) {
      query.value = trimmedCodeName.slice(0, 14)

      try {
        const isDuplicate = await disposalsStore.getDuplicateDisposallineByCode(query.value)
        if (isDuplicate) {
          appStore.sendErrorNotice("重複 Cap QRcode!")
          appStore.closeNoticeWithDelay(5000)
          return ""
        }
      } catch (error) {
        console.error('檢查重複 QRCode 失敗:', error)
        // 如果檢查重複失敗，繼續執行（可能是網路問題）
      }

      try {
        await productsStore.getProductByQRCode(query.value)

        if (!product.value || product.value.length === 0) {
          appStore.sendErrorNotice("查無資料!")
          appStore.closeNoticeWithDelay(5000)
          return ""
        } else {
          return product.value
        }
      } catch (error) {
        console.error('獲取產品資料失敗:', error)
        if (error.code === 'ECONNABORTED') {
          appStore.sendErrorNotice("網路連線超時，請稍後再試!")
        } else if (error.response?.status === 404) {
          appStore.sendErrorNotice("查無資料!")
        } else {
          appStore.sendErrorNotice("獲取產品資料失敗，請稍後再試!")
        }
        appStore.closeNoticeWithDelay(5000)
        return ""
      }
    }
  } catch (error) {
    console.error('getProduct 執行失敗:', error)
    appStore.sendErrorNotice("系統錯誤，請稍後再試!")
    appStore.closeNoticeWithDelay(5000)
    return ""
  }
}

const cancel = () => {
  router.push({ name: "disposals" })
}

const remove = (item: any) => {
  selectedDisposalline.value = item
  dialog.value = true
}

const onConfirm = async () => {
  try {
    await disposalsStore.deleteDisposalline(selectedDisposalline.value)

    // 確保本地狀態能夠正確更新
    isUpdatingFromStore.value = false

    // 手動觸發本地狀態更新
    const updatedDisposal = disposalsStore.currentDisposal
    if (updatedDisposal) {
      disposal.value = { ...updatedDisposal }
      disposal.value.quantity = updatedDisposal.disposallines ? updatedDisposal.disposallines.length : 0
      console.log('明細刪除成功，更新本地狀態:', disposal.value.quantity)
    }

    selectedDisposalline.value = null
    dialog.value = false
    appStore.sendSuccessNotice("明細刪除成功!")
  } catch (error) {
    console.error('刪除明細失敗:', error)
    appStore.sendErrorNotice("刪除明細失敗: " + error.message)
  }
}

const onCancel = () => {
  selectedDisposalline.value = null
  dialog.value = false
}

const addProduct = async () => {
  // 如果是新增模式且還沒有 ID，先保存主檔
  if (!disposal.value.id || disposal.value.id <= 0) {
    // 先驗證表單
    const { valid } = await validForm.value.validate()

    if (!valid) {
      appStore.sendErrorNotice("請先填寫所有必填欄位!")
      return
    }

    try {
      const disposalData = {
        ...disposal.value,
        shiftName: disposal.value.shiftName,
        employId: disposal.value.employId,
        groupName: disposal.value.groupName,
        typeName: disposal.value.typeName || "DISPOSAL",
        classDate: disposal.value.classDate || new Date().toISOString().slice(0, 10)
      }

      await disposalsStore.saveDisposal(disposalData)
      // 更新路由到編輯模式
      const newId = disposalsStore.disposalId
      router.replace(`/disposal/${newId}`)
    } catch (error) {
      console.error("保存主檔失敗:", error)
      appStore.sendErrorNotice("請先完成主檔資料並保存")
      return
    }
  }

  addProductModal.value = true
  query.value = ""
  searchFilter.value.contain.codeName = ""
  disposalId.value = disposal.value.id

  // 重置 disposalline 表單
  disposalline.value = {
    disposalId: disposal.value.id,
    remarkId: null,
    categoryId: null
  }

  productsStore.resetState()

  nextTick(() => {
    if (validDetail.value) {
      validDetail.value.validate()
    }
    nextTick(() => {
      if (qrCodeInput.value) {
        qrCodeInput.value.focus()
      }
    })
  })
}

const saveDisposalline = async () => {
  const addProduct = product.value[0]

  // 根據後端 downgradelines model 的需求構建資料
  const newDisposalline = {
    downgradeId: disposal.value.id,
    categoryId: categoryId.value,
    remarkId: disposalline.value.remarkId,
    // 從產品資料中取得必要欄位
    furnaceName: addProduct.furnaceName || '',
    productName: addProduct.productName || '',
    gradeName: addProduct.gradeName || '',
    workDate: addProduct.workDate || '',
    twisterNO: addProduct.twisterNO || '',
    spindleNO: addProduct.spindleNO || '',
    codeName: addProduct.codeName || searchFilter.value.contain.codeName,
    m_product_id: addProduct.m_product_id || null,
    m_twqrcodeline_id: addProduct.m_twqrcodeline_id || null
  }

  console.log('準備傳送的明細資料:', newDisposalline)

  try {
    await disposalsStore.addDisposallineToDisposal(newDisposalline)

    // 確保本地狀態能夠正確更新
    isUpdatingFromStore.value = false

    // 手動觸發本地狀態更新
    const updatedDisposal = disposalsStore.currentDisposal
    if (updatedDisposal && updatedDisposal.disposallines) {
      disposal.value = { ...updatedDisposal }
      disposal.value.quantity = updatedDisposal.disposallines.length
      console.log('明細新增成功，更新本地狀態:', disposal.value.disposallines.length)
    }

    disposallineId.value = null
    // 不關閉新增視窗，保持開啟狀態讓用戶可以繼續新增
    // addProductModal.value = false
    resetForm()
    // 只顯示一個成功訊息，移除重複的訊息
    appStore.sendSuccessNotice("明細新增成功!")
  } catch (error: any) {
    console.error('保存明細失敗:', error)
    appStore.sendErrorNotice("保存明細失敗: " + error.message)
  }
}

const resetForm = () => {
  searchFilter.value.contain.codeName = ""
  disposalline.value = {
    disposalId: disposal.value.id,
    remarkId: null,
    categoryId: null
  }
  productsStore.resetState()
  nextTick(() => {
    if (qrCodeInput.value) {
      qrCodeInput.value.focus()
    }
  })
}

const cancelAddProduct = () => {
  addProductModal.value = false
  query.value = ""
  searchFilter.value.contain.codeName = ""
  productsStore.resetState()
  // 不需要重置 disposalsStore 或重新載入數據，保持當前狀態
}

const closeSnackbar = () => {
  appStore.closeNotice()
}

// 處理明細分頁變更
const updateDetailOptions = (options: any) => {
  console.log('明細分頁選項變更:', options)

  // 更新分頁狀態
  if (detailPagination.value.page !== options.page || detailPagination.value.limit !== options.itemsPerPage) {
    detailPagination.value.page = options.page
    detailPagination.value.limit = options.itemsPerPage
    // 由於明細資料是本地的，不需要重新載入
  }
}

// 生命週期
onMounted(async () => {
  // 載入基礎數據
  await disposalsStore.getEmployees()
  await disposalsStore.getCategories()
  await disposalsStore.getRemarks()

  window.scrollTo(0, 0)

  if (route.params.id) {
    title.value = "Disposal Yarn(明細)"
    disposalsStore.resetState()
    await disposalsStore.getDisposalById(route.params.id as string)

  } else {
    title.value = "Disposal Yarn(新增)"
    // 新增模式：確保完全重置狀態
    disposalsStore.resetState()

    // 設置新的空白資料，不設定預設值
    const newDisposal = {
      id: null,
      typeName: type.value,
      classDate: new Date().toISOString().slice(0, 10),
      shiftName: null,  // 不設定預設值
      employId: null,
      groupName: null,  // 不設定預設值
      quantity: 0,
      disposallines: []
    }

    // 重置本地狀態
    disposal.value = { ...newDisposal }
    disposalsStore.setDisposal(newDisposal)
    disposalsStore.setDisposallines([])

    nextTick(() => {
      if (validForm.value) {
        // 先重置驗證，然後觸發驗證以顯示必填提示
        validForm.value.resetValidation()
        setTimeout(() => {
          validForm.value.validate()
        }, 100)
      }
    })
  }
})
</script>

<style scoped>
/* 自定義分頁按鈕顏色 */
:deep(.colored-pagination .v-data-table-footer .v-btn) {
  color: rgb(33, 150, 243) !important; /* 藍色 */
}

:deep(.colored-pagination .v-data-table-footer .v-btn:hover) {
  background-color: rgba(33, 150, 243, 0.1) !important;
}

:deep(.colored-pagination .v-data-table-footer .v-btn.v-btn--active) {
  background-color: rgb(33, 150, 243) !important;
  color: white !important;
}

:deep(.colored-pagination .v-data-table-footer .v-btn.v-btn--disabled) {
  color: rgba(0, 0, 0, 0.26) !important;
}

/* 分頁選擇器樣式 */
:deep(.colored-pagination .v-data-table-footer .v-select) {
  color: rgb(33, 150, 243) !important;
}

:deep(.colored-pagination .v-data-table-footer .v-select .v-field__input) {
  color: rgb(33, 150, 243) !important;
}
</style>
