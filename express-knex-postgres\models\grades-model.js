const db = require("../config/dbConfig.js");
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET ALL GRADES
const find = () => {
  return db("aits_grades");
};

// GET SPECIFIC GRADE BY ID
const findById = id => {
  return db("aits_grades").where("id", id);

  //SQL RAW METHOD
  // return db.raw(`SELECT * FROM grades
  //                  WHERE id = ${id}`);
};

// ADD A GRADE
const addGrade = grade => {
  return db("aits_grades").insert(grade, "id");
};

// UPDATE GRADE
const updateGrade = (id, post) => {
  return db("aits_grades")
    .where("id", id)
    .update(post);
};

// REMOVE GRADE
const removeGrade = id => {
  return db("aits_grades")
    .where("id", id)
    .del();
};

module.exports = {
  find,
  findById,
  addGrade,
  updateGrade,
  removeGrade
};
