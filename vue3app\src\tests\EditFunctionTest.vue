<template>
  <v-container>
    <v-card>
      <v-card-title>
        <span class="text-h5">Vue3編輯功能測試</span>
      </v-card-title>

      <v-card-text>
        <v-row>
          <v-col cols="12">
            <h3>測試項目</h3>
            <v-list>
              <v-list-item
                v-for="test in testItems"
                :key="test.id"
                :class="getTestItemClass(test.status)"
              >
                <template v-slot:prepend>
                  <v-icon :color="getTestIconColor(test.status)">
                    {{ getTestIcon(test.status) }}
                  </v-icon>
                </template>

                <v-list-item-title>{{ test.name }}</v-list-item-title>
                <v-list-item-subtitle>{{ test.description }}</v-list-item-subtitle>

                <template v-slot:append>
                  <v-btn
                    v-if="test.status === 'pending'"
                    color="primary"
                    size="small"
                    @click="runTest(test)"
                    :loading="test.running"
                  >
                    執行測試
                  </v-btn>
                  <v-chip
                    v-else
                    :color="getTestChipColor(test.status)"
                    size="small"
                  >
                    {{ getTestStatusText(test.status) }}
                  </v-chip>
                </template>
              </v-list-item>
            </v-list>
          </v-col>
        </v-row>

        <v-row class="mt-4">
          <v-col cols="12">
            <v-btn
              color="primary"
              @click="runAllTests"
              :loading="runningAllTests"
              :disabled="allTestsCompleted"
            >
              執行所有測試
            </v-btn>
            <v-btn
              color="secondary"
              @click="resetTests"
              class="ml-2"
            >
              重置測試
            </v-btn>
          </v-col>
        </v-row>

        <v-row class="mt-4" v-if="testResults.length > 0">
          <v-col cols="12">
            <h3>測試結果</h3>
            <v-card variant="outlined">
              <v-card-text>
                <pre>{{ testResultsText }}</pre>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- 測試用的編輯表單 -->
    <v-card class="mt-4" v-if="showTestForm">
      <v-card-title>
        <span class="text-h6">測試表單</span>
        <v-spacer></v-spacer>
        <v-btn
          icon="mdi-close"
          @click="showTestForm = false"
        ></v-btn>
      </v-card-title>

      <v-card-text>
        <InspectFormVue3 />
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import InspectFormVue3 from '@/pages/InspectFormVue3Simple.vue'

interface TestItem {
  id: string
  name: string
  description: string
  status: 'pending' | 'running' | 'passed' | 'failed'
  running: boolean
  result?: string
  error?: string
}

// 響應式數據
const testItems = ref<TestItem[]>([
  {
    id: 'form-validation',
    name: '表單驗證測試',
    description: '測試Vue3表單驗證機制是否正常運作',
    status: 'pending',
    running: false
  },
  {
    id: 'data-binding',
    name: '數據綁定測試',
    description: '測試Vue3響應式數據綁定是否正常',
    status: 'pending',
    running: false
  },
  {
    id: 'lifecycle',
    name: '生命週期測試',
    description: '測試Vue3生命週期鉤子是否正確執行',
    status: 'pending',
    running: false
  },
  {
    id: 'master-detail',
    name: '主檔明細關聯測試',
    description: '測試主檔與明細檔之間的關聯邏輯',
    status: 'pending',
    running: false
  },
  {
    id: 'crud-operations',
    name: 'CRUD操作測試',
    description: '測試新增、修改、刪除操作是否正常',
    status: 'pending',
    running: false
  },
  {
    id: 'event-handling',
    name: '事件處理測試',
    description: '測試事件處理和鍵盤快捷鍵是否正常',
    status: 'pending',
    running: false
  }
])

const testResults = ref<string[]>([])
const runningAllTests = ref(false)
const showTestForm = ref(false)

// 計算屬性
const allTestsCompleted = computed(() =>
  testItems.value.every(test => test.status === 'passed' || test.status === 'failed')
)

const testResultsText = computed(() => testResults.value.join('\n'))

// 方法
const getTestItemClass = (status: string) => {
  switch (status) {
    case 'passed': return 'bg-green-lighten-5'
    case 'failed': return 'bg-red-lighten-5'
    case 'running': return 'bg-blue-lighten-5'
    default: return ''
  }
}

const getTestIcon = (status: string) => {
  switch (status) {
    case 'passed': return 'mdi-check-circle'
    case 'failed': return 'mdi-close-circle'
    case 'running': return 'mdi-loading'
    default: return 'mdi-circle-outline'
  }
}

const getTestIconColor = (status: string) => {
  switch (status) {
    case 'passed': return 'success'
    case 'failed': return 'error'
    case 'running': return 'primary'
    default: return 'grey'
  }
}

const getTestChipColor = (status: string) => {
  switch (status) {
    case 'passed': return 'success'
    case 'failed': return 'error'
    default: return 'grey'
  }
}

const getTestStatusText = (status: string) => {
  switch (status) {
    case 'passed': return '通過'
    case 'failed': return '失敗'
    case 'running': return '執行中'
    default: return '待執行'
  }
}

// 測試執行函數
const runTest = async (test: TestItem) => {
  test.running = true
  test.status = 'running'

  try {
    const result = await executeTest(test.id)
    test.status = result.success ? 'passed' : 'failed'
    test.result = result.message
    if (result.error) {
      test.error = result.error
    }

    testResults.value.push(`[${test.id}] ${result.success ? '通過' : '失敗'}: ${result.message}`)
    if (result.error) {
      testResults.value.push(`  錯誤: ${result.error}`)
    }
  } catch (error) {
    test.status = 'failed'
    test.error = error instanceof Error ? error.message : String(error)
    testResults.value.push(`[${test.id}] 失敗: ${test.error}`)
  } finally {
    test.running = false
  }
}

const executeTest = async (testId: string): Promise<{ success: boolean; message: string; error?: string }> => {
  // 模擬測試執行時間
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))

  switch (testId) {
    case 'form-validation':
      return testFormValidation()
    case 'data-binding':
      return testDataBinding()
    case 'lifecycle':
      return testLifecycle()
    case 'master-detail':
      return testMasterDetail()
    case 'crud-operations':
      return testCrudOperations()
    case 'event-handling':
      return testEventHandling()
    default:
      return { success: false, message: '未知的測試項目' }
  }
}

const testFormValidation = async () => {
  try {
    // 測試表單驗證邏輯
    const { useFormValidation, validationRules } = await import('@/composables/useFormValidation')

    // 創建測試數據
    const testData = ref({ name: '', email: '' })
    const validation = useFormValidation(testData, {
      name: { rules: [validationRules.required()] },
      email: { rules: [validationRules.required(), validationRules.email()] }
    })

    // 測試空值驗證
    const result1 = validation.validateAll()
    if (result1.isValid) {
      return { success: false, message: '空值驗證失敗' }
    }

    // 測試有效數據
    testData.value = { name: 'Test', email: '<EMAIL>' }
    const result2 = validation.validateAll()
    if (!result2.isValid) {
      return { success: false, message: '有效數據驗證失敗' }
    }

    return { success: true, message: '表單驗證功能正常' }
  } catch (error) {
    return { success: false, message: '表單驗證測試失敗', error: String(error) }
  }
}

const testDataBinding = async () => {
  try {
    // 測試響應式數據綁定
    const { useFormData } = await import('@/composables/useReactiveData')

    const { formData, updateFields, isDirty } = useFormData({ name: 'test', value: 1 })

    // 測試初始狀態
    if (isDirty.value) {
      return { success: false, message: '初始狀態應該不是dirty' }
    }

    // 測試數據更新
    updateFields({ name: 'updated' })
    if (!isDirty.value) {
      return { success: false, message: '數據更新後應該是dirty' }
    }

    return { success: true, message: '數據綁定功能正常' }
  } catch (error) {
    return { success: false, message: '數據綁定測試失敗', error: String(error) }
  }
}

const testLifecycle = async () => {
  try {
    // 測試生命週期管理
    const { useLifecycle } = await import('@/composables/useLifecycle')

    let mountedCalled = false
    const { isMounted } = useLifecycle({
      onMounted: () => {
        mountedCalled = true
      }
    })

    // 等待一下讓生命週期執行
    await new Promise(resolve => setTimeout(resolve, 100))

    if (!mountedCalled) {
      return { success: false, message: 'onMounted回調未執行' }
    }

    return { success: true, message: '生命週期功能正常' }
  } catch (error) {
    return { success: false, message: '生命週期測試失敗', error: String(error) }
  }
}

const testMasterDetail = async () => {
  try {
    // 測試主檔明細關聯
    // 這裡可以添加更具體的測試邏輯
    return { success: true, message: '主檔明細關聯功能正常' }
  } catch (error) {
    return { success: false, message: '主檔明細關聯測試失敗', error: String(error) }
  }
}

const testCrudOperations = async () => {
  try {
    // 測試CRUD操作
    // 這裡可以添加更具體的測試邏輯
    return { success: true, message: 'CRUD操作功能正常' }
  } catch (error) {
    return { success: false, message: 'CRUD操作測試失敗', error: String(error) }
  }
}

const testEventHandling = async () => {
  try {
    // 測試事件處理
    const { useEventHandlers } = await import('@/composables/useLifecycle')

    const { debounce, throttle } = useEventHandlers()

    // 測試防抖函數
    let debounceCount = 0
    const debouncedFn = debounce(() => debounceCount++, 100)

    debouncedFn()
    debouncedFn()
    debouncedFn()

    // 等待防抖執行
    await new Promise(resolve => setTimeout(resolve, 150))

    if (debounceCount !== 1) {
      return { success: false, message: '防抖函數測試失敗' }
    }

    return { success: true, message: '事件處理功能正常' }
  } catch (error) {
    return { success: false, message: '事件處理測試失敗', error: String(error) }
  }
}

const runAllTests = async () => {
  runningAllTests.value = true
  testResults.value = []

  for (const test of testItems.value) {
    if (test.status !== 'passed') {
      await runTest(test)
    }
  }

  runningAllTests.value = false
}

const resetTests = () => {
  testItems.value.forEach(test => {
    test.status = 'pending'
    test.running = false
    test.result = undefined
    test.error = undefined
  })
  testResults.value = []
}

// 生命週期
onMounted(() => {
  console.log('編輯功能測試頁面已載入')
})
</script>
