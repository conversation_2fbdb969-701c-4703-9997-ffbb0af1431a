<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <span class="text-h4">
              <v-icon class="mr-2">mdi-clipboard-check</v-icon>
              {{ isEditMode ? '編輯品檢記錄' : '新增品檢記錄' }}
            </span>
          </v-card-title>

          <v-card-text>
            <v-alert type="info" class="mb-4" v-if="!isEditMode">
              <strong>📝 新增品檢記錄</strong>
              請填寫品檢記錄的基本資訊。
            </v-alert>

            <v-alert type="warning" class="mb-4" v-if="isEditMode">
              <strong>✏️ 編輯品檢記錄</strong>
              正在編輯ID為 {{ route.params.id }} 的品檢記錄。
            </v-alert>

            <v-form ref="form" v-model="formValid">
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.code"
                    label="代碼"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.typeName"
                    :items="typeOptions"
                    label="類型"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.classDate"
                    label="班別日期"
                    type="date"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.quantity"
                    label="數量"
                    type="number"
                    :rules="[rules.required, rules.number]"
                    outlined
                    dense
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.shiftName"
                    :items="shiftOptions"
                    label="班別"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.groupName"
                    :items="groupOptions"
                    label="組別"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.employeeId"
                    :items="employeeOptions"
                    item-title="name"
                    item-value="id"
                    label="員工"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>
              </v-row>
            </v-form>

            <v-divider class="my-4" />

            <!-- 明細檔表格 -->
            <v-row>
              <v-col cols="12">
                <h3>
                  <v-icon class="mr-2">mdi-format-list-bulleted</v-icon>
                  品檢明細
                </h3>

                <v-card variant="outlined" class="mt-3">
                  <v-card-title>
                    <v-spacer />
                    <v-btn
                      color="primary"
                      @click="addDetailItem"
                      prepend-icon="mdi-plus"
                      size="small"
                    >
                      新增明細
                    </v-btn>
                  </v-card-title>

                  <v-card-text>
                    <v-data-table
                      :headers="detailHeaders"
                      :items="detailItems"
                      :items-per-page="5"
                      class="elevation-1"
                    >
                      <template v-slot:item.actions="{ item }">
                        <v-btn
                          icon="mdi-pencil"
                          size="small"
                          color="primary"
                          @click="editDetailItem(item)"
                          class="mr-2"
                        />
                        <v-btn
                          icon="mdi-delete"
                          size="small"
                          color="error"
                          @click="deleteDetailItem(item.id)"
                        />
                      </template>

                      <template v-slot:no-data>
                        <v-alert type="info" class="ma-4">
                          尚無明細資料，請點擊「新增明細」按鈕添加。
                        </v-alert>
                      </template>
                    </v-data-table>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>

            <v-divider class="my-4" />

            <v-row>
              <v-col cols="12">
                <h3>測試狀態</h3>
                <v-list>
                  <v-list-item>
                    <template v-slot:prepend>
                      <v-icon color="success">mdi-check-circle</v-icon>
                    </template>
                    <v-list-item-title>Vue3 Composition API</v-list-item-title>
                    <v-list-item-subtitle>響應式數據和計算屬性正常運作</v-list-item-subtitle>
                  </v-list-item>

                  <v-list-item>
                    <template v-slot:prepend>
                      <v-icon color="success">mdi-check-circle</v-icon>
                    </template>
                    <v-list-item-title>表單驗證</v-list-item-title>
                    <v-list-item-subtitle>表單驗證規則正常運作</v-list-item-subtitle>
                  </v-list-item>

                  <v-list-item>
                    <template v-slot:prepend>
                      <v-icon color="success">mdi-check-circle</v-icon>
                    </template>
                    <v-list-item-title>路由參數</v-list-item-title>
                    <v-list-item-subtitle>
                      當前路由: {{ $route.path }}
                      {{ $route.params.id ? `(編輯模式 - ID: ${$route.params.id})` : '(新增模式)' }}
                    </v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>

            <v-row class="mt-4">
              <v-col cols="12" md="6">
                <h3>主檔數據 (即時更新)</h3>
                <v-card variant="outlined">
                  <v-card-text>
                    <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
                  </v-card-text>
                </v-card>
              </v-col>

              <v-col cols="12" md="6">
                <h3>明細檔數據 (即時更新)</h3>
                <v-card variant="outlined">
                  <v-card-text>
                    <pre>{{ JSON.stringify(detailItems, null, 2) }}</pre>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>

          <v-card-actions>
            <v-btn
              color="primary"
              @click="saveForm"
              :disabled="!formValid"
              prepend-icon="mdi-content-save"
            >
              {{ isEditMode ? '更新' : '儲存' }}
            </v-btn>

            <v-btn
              color="secondary"
              @click="resetForm"
              prepend-icon="mdi-refresh"
            >
              重設
            </v-btn>

            <v-btn
              color="error"
              @click="goBack"
              prepend-icon="mdi-arrow-left"
            >
              返回
            </v-btn>

            <v-spacer />

            <v-chip color="info">
              表單狀態: {{ formValid ? '有效' : '無效' }}
            </v-chip>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <!-- 明細編輯對話框 -->
    <v-dialog v-model="detailDialog" max-width="600px">
      <v-card>
        <v-card-title>
          <span class="text-h5">{{ editingDetailId ? '編輯' : '新增' }}明細項目</span>
        </v-card-title>

        <v-card-text>
          <v-form ref="detailForm" v-model="detailFormValid">
            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model="detailData.codeName"
                  label="產品代碼"
                  :rules="[rules.required]"
                  outlined
                  dense
                />
              </v-col>

              <v-col cols="12">
                <v-select
                  v-model="detailData.remarkName"
                  :items="remarkOptions"
                  label="異常原因"
                  :rules="[rules.required]"
                  outlined
                  dense
                />
              </v-col>

              <v-col cols="12">
                <v-text-field
                  v-model="detailData.quantity"
                  label="數量"
                  type="number"
                  :rules="[rules.required, rules.number]"
                  outlined
                  dense
                />
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>

        <v-card-actions>
          <v-spacer />
          <v-btn
            color="grey"
            @click="closeDetailDialog"
          >
            取消
          </v-btn>
          <v-btn
            color="primary"
            @click="saveDetailItem"
            :disabled="!detailFormValid"
          >
            {{ editingDetailId ? '更新' : '新增' }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 成功訊息 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          關閉
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 響應式數據
const form = ref()
const formValid = ref(false)

const formData = ref({
  id: 0,
  code: '',
  typeName: 'YARN',
  classDate: new Date().toISOString().slice(0, 10),
  quantity: 0,
  shiftName: '',
  groupName: '',
  employeeId: 0
})

const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

// 明細檔數據
const detailItems = ref([])
const detailDialog = ref(false)
const detailForm = ref()
const detailFormValid = ref(false)
const editingDetailId = ref(null)

const detailData = ref({
  codeName: '',
  remarkName: '',
  quantity: 1
})

// 選項數據
const typeOptions = ['YARN', 'CAKE', 'PACK']
const shiftOptions = ['早班', '中班', '晚班']
const groupOptions = ['A組', 'B組', 'C組', 'D組']
const employeeOptions = ref([
  { id: 1, name: '張三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' }
])

const remarkOptions = [
  '品質不良',
  '尺寸不符',
  '顏色異常',
  '包裝破損',
  '其他'
]

// 明細檔表格標題
const detailHeaders = [
  { title: 'ID', key: 'id', align: 'start' },
  { title: '產品代碼', key: 'codeName', align: 'start' },
  { title: '異常原因', key: 'remarkName', align: 'start' },
  { title: '數量', key: 'quantity', align: 'end' },
  { title: '操作', key: 'actions', align: 'center', sortable: false }
]

// 驗證規則
const rules = {
  required: (value: any) => !!value || '此欄位為必填',
  number: (value: any) => !isNaN(Number(value)) || '請輸入有效數字'
}

// 計算屬性
const isEditMode = computed(() => !!route.params.id)

// 方法
const saveForm = async () => {
  try {
    // 模擬保存操作
    await new Promise(resolve => setTimeout(resolve, 1000))

    showMessage(
      isEditMode.value ? '更新成功！' : '儲存成功！',
      'success'
    )

    // 如果是新增模式，可以導航到編輯模式
    if (!isEditMode.value) {
      const newId = Math.floor(Math.random() * 1000) + 1
      router.push(`/test-vue3/inspect/${newId}`)
    }
  } catch (error) {
    showMessage('操作失敗，請重試', 'error')
  }
}

const resetForm = () => {
  formData.value = {
    code: '',
    typeName: 'YARN',
    classDate: new Date().toISOString().slice(0, 10),
    quantity: 0
  }
  form.value?.resetValidation()
  showMessage('表單已重設', 'info')
}

const goBack = () => {
  router.push('/vue3-test')
}

// 明細檔操作方法
const addDetailItem = () => {
  editingDetailId.value = null
  detailData.value = {
    codeName: '',
    remarkName: '',
    quantity: 1
  }
  detailDialog.value = true
}

const editDetailItem = (item: any) => {
  editingDetailId.value = item.id
  detailData.value = { ...item }
  detailDialog.value = true
}

const saveDetailItem = () => {
  // 簡化驗證：只檢查必填欄位
  if (!detailData.value.codeName) {
    showMessage('請輸入產品代碼', 'error')
    return
  }

  console.log('保存明細項目:', detailData.value)
  console.log('編輯ID:', editingDetailId.value)
  console.log('當前明細列表:', detailItems.value)

  if (editingDetailId.value) {
    // 編輯模式
    const index = detailItems.value.findIndex((item: any) => item.id === editingDetailId.value)
    console.log('找到編輯項目索引:', index)
    if (index > -1) {
      detailItems.value[index] = { ...detailData.value, id: editingDetailId.value }
      showMessage('明細項目已更新', 'success')
    }
  } else {
    // 新增模式
    const newDetail = { ...detailData.value, id: Date.now() }
    console.log('新增明細項目:', newDetail)
    detailItems.value.push(newDetail)
    showMessage('明細項目已新增', 'success')
  }

  console.log('更新後明細列表:', detailItems.value)
  closeDetailDialog()
}

const closeDetailDialog = () => {
  detailDialog.value = false
  editingDetailId.value = null
  detailForm.value?.resetValidation()
}

const deleteDetailItem = (id: number) => {
  const index = detailItems.value.findIndex((item: any) => item.id === id)
  if (index > -1) {
    detailItems.value.splice(index, 1)
    showMessage('已刪除明細項目', 'warning')
  }
}

const showMessage = (message: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}

// 生命週期
onMounted(() => {
  if (isEditMode.value) {
    // 模擬載入編輯數據
    formData.value = {
      id: Number(route.params.id),
      code: `TEST-${route.params.id}`,
      typeName: 'YARN',
      classDate: new Date().toISOString().slice(0, 10),
      quantity: 100,
      shiftName: '早班',
      groupName: 'A組',
      employeeId: 1
    }

    // 模擬載入明細檔數據
    detailItems.value = [
      { id: 1, codeName: '12345678901234', remarkName: '品質不良', quantity: 2 },
      { id: 2, codeName: '56789012345678', remarkName: '尺寸不符', quantity: 3 }
    ]

    showMessage(`載入編輯數據 (ID: ${route.params.id})`, 'info')
  } else {
    showMessage('新增模式已準備就緒', 'info')
  }
})
</script>

<style scoped>
.v-card {
  margin-bottom: 16px;
}

pre {
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
