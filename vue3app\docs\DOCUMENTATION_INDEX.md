# 📚 AITS Vue3 文檔索引

> 完整的 AITS Vue3 應用系統文檔導航

## 📋 文檔概覽

本文檔索引提供了 AITS Vue3 應用系統的完整文檔導航，幫助開發者快速找到所需的資訊。

## 🎯 核心文檔

### 1. 主要文檔

| 文檔名稱 | 描述 | 狀態 |
|---------|------|------|
| [README.md](../README.md) | 專案主要說明文檔 | ✅ 已整合 |
| [DOCUMENTATION_INDEX.md](DOCUMENTATION_INDEX.md) | 文檔索引導航 | ✅ 完成 |
| [INTEGRATED_DOCUMENTATION.md](INTEGRATED_DOCUMENTATION.md) | 完整整合文檔 | ✅ 完成 |
| [MASTER_DOCUMENTATION.md](MASTER_DOCUMENTATION.md) | 主文檔總覽 | ✅ 完成 |

### 2. 技術規範文檔

| 文檔名稱 | 描述 | 狀態 |
|---------|------|------|
| [DevelopmentStandards.md](DevelopmentStandards.md) | 開發規範指南 | ✅ 完成 |
| [Vue3FormStandard.md](Vue3FormStandard.md) | Vue3 表單開發標準 | ✅ 完成 |

### 3. 功能文檔

| 文檔名稱 | 描述 | 狀態 |
|---------|------|------|
| [AndroidLayoutConfigSystem.md](AndroidLayoutConfigSystem.md) | Android 布局配置系統 | ✅ 完成 |

## 🚀 快速導航

### 新手入門

1. **開始使用**: [README.md](../README.md) - 了解專案概覽和快速開始
2. **完整文檔**: [INTEGRATED_DOCUMENTATION.md](INTEGRATED_DOCUMENTATION.md) - 一站式完整資訊
3. **開發規範**: [DevelopmentStandards.md](DevelopmentStandards.md) - 了解開發標準

### 開發者指南

1. **Vue3 表單標準**: [Vue3FormStandard.md](Vue3FormStandard.md)
2. **主文檔總覽**: [MASTER_DOCUMENTATION.md](MASTER_DOCUMENTATION.md)
3. **Android 布局**: [AndroidLayoutConfigSystem.md](AndroidLayoutConfigSystem.md)

### 文檔導航

1. **文檔索引**: [DOCUMENTATION_INDEX.md](DOCUMENTATION_INDEX.md) - 本文檔
2. **整合文檔**: [INTEGRATED_DOCUMENTATION.md](INTEGRATED_DOCUMENTATION.md) - 完整技術資訊
3. **主文檔**: [MASTER_DOCUMENTATION.md](MASTER_DOCUMENTATION.md) - 快速參考

## 📊 文檔統計

- **總文檔數**: 7 個 (清理後)
- **主要文檔**: 4 個 (README + 3個整合文檔)
- **技術規範**: 2 個 (開發規範 + Vue3表單標準)
- **功能文檔**: 1 個 (Android布局配置)
- **清理移除**: 18 個重複/過時文檔

## 🔍 文檔搜索指南

### 按主題搜索

- **Vue3 相關**: 查看 Vue3FormStandard.md 和 INTEGRATED_DOCUMENTATION.md
- **開發規範**: 查看 DevelopmentStandards.md
- **Android 布局**: 查看 AndroidLayoutConfigSystem.md
- **完整資訊**: 查看 INTEGRATED_DOCUMENTATION.md
- **快速參考**: 查看 MASTER_DOCUMENTATION.md

### 按用途搜索

- **新手入門**: README.md → INTEGRATED_DOCUMENTATION.md
- **開發參考**: DevelopmentStandards.md → Vue3FormStandard.md
- **專案總覽**: MASTER_DOCUMENTATION.md
- **文檔導航**: DOCUMENTATION_INDEX.md (本文檔)

## 📝 文檔維護

### 更新頻率

- **主要文檔**: 每次重大更新時更新
- **技術規範**: 每月檢查一次
- **測試文檔**: 每次測試流程變更時更新
- **狀態文檔**: 每週更新一次

### 維護責任

- **技術文檔**: 開發團隊負責
- **測試文檔**: QA 團隊負責
- **業務文檔**: 產品團隊負責
- **整合文檔**: 技術主管負責

## 🎯 使用建議

### 對於新開發者

1. 先閱讀 [README.md](../README.md) 了解專案概覽
2. 查看 [INTEGRATED_DOCUMENTATION.md](INTEGRATED_DOCUMENTATION.md) 獲取完整技術資訊
3. 學習 [DevelopmentStandards.md](DevelopmentStandards.md) 開發規範
4. 參考 [Vue3FormStandard.md](Vue3FormStandard.md) 進行開發

### 對於專案管理者

1. 查看 [MASTER_DOCUMENTATION.md](MASTER_DOCUMENTATION.md) 了解專案狀態
2. 使用 [DOCUMENTATION_INDEX.md](DOCUMENTATION_INDEX.md) 進行文檔導航
3. 參考 [INTEGRATED_DOCUMENTATION.md](INTEGRATED_DOCUMENTATION.md) 了解技術細節

### 對於系統維護者

1. 使用 [DevelopmentStandards.md](DevelopmentStandards.md) 維護代碼品質
2. 參考 [AndroidLayoutConfigSystem.md](AndroidLayoutConfigSystem.md) 進行布局配置
3. 定期更新 [MASTER_DOCUMENTATION.md](MASTER_DOCUMENTATION.md) 專案狀態

---

**文檔索引版本**: 1.0
**最後更新**: 2025-06-30
**維護者**: AITS 文檔團隊
