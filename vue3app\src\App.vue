<template>
  <v-app id="vcrm">
    <template v-if="!signedIn">
      <router-view></router-view>
    </template>
    <template v-else>
      <NavigationDrawer
        v-model="drawer"
        :user="user"
        :user-menus="userMenus"
        :active-menu-item="activeMenuItem"
        :is-mobile="isMobile"
        :mini="mini"
        @toggle-mini="toggleMini"
        @navigate="handleNavigation"
        @user-action="handleUserActions"
      />

      <v-app-bar :height="isMobile ? 56 : 64" elevation="4">
        <v-app-bar-nav-icon
          @click="isMobile ? toggleDrawer() : toggleMini()"
          :icon="isMobile ? 'mdi-menu' : (mini ? 'mdi-menu-open' : 'mdi-menu')"
        ></v-app-bar-nav-icon>

        <v-app-bar-title class="text-h6">
          <span v-if="!isMobile || !mini">PowerAI</span>
        </v-app-bar-title>

        <v-spacer></v-spacer>

        <!-- Mobile-optimized actions -->
        <template v-if="isMobile">
          <v-menu location="bottom end">
            <template v-slot:activator="{ props }">
              <v-btn icon="mdi-dots-vertical" v-bind="props"></v-btn>
            </template>
            <v-list>
              <v-list-item
                v-for="item in userMenus"
                :key="item.title"
                :value="item.title"
                @click="handleUserActions(item)"
              >
                <template v-slot:prepend>
                  <v-icon :icon="item.icon"></v-icon>
                </template>
                <v-list-item-title>{{ item.title }}</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </template>

        <!-- Desktop actions -->
        <template v-else>
          <v-btn variant="text" href="" target="_blank">
            <span class="ml-2">PowerAI</span>
          </v-btn>
        </template>
      </v-app-bar>

      <v-main>
        <v-container
          :fluid="true"
          :class="{ 'pa-2': isMobile, 'pa-4': !isMobile }"
        >
          <router-view></router-view>
        </v-container>
      </v-main>

      <v-footer
        app
        class="d-flex justify-center text-center"
        :height="isMobile ? 48 : 64"
        v-if="!isMobile"
      >
        <span>&copy; PowerAI 2023</span>
      </v-footer>
    </template>

    <!-- Snackbar for notifications -->
    <v-snackbar
      v-model="appStore.snackbar"
      :color="appStore.mode"
      timeout="3000"
      top
    >
      {{ appStore.notice }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="appStore.closeNotice()"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </v-app>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from './stores/user'
import { useAppStore } from './stores/app'
import { useDisplay } from 'vuetify'
import { NavigationDrawer } from '@/components/Navigation'

interface AppMenu {
  icon: string
  title: string
  vertical?: string
  link: string
}

const router = useRouter()
const userStore = useUserStore()
const appStore = useAppStore()
const { mobile, smAndDown, mdAndUp } = useDisplay()

// Reactive state
const mini = ref(false)
const drawer = ref(true)
const menuItem = ref('')

// Mobile detection
const isMobile = computed(() => mobile.value || smAndDown.value)
const isTablet = computed(() => !mobile.value && !mdAndUp.value)
const isDesktop = computed(() => mdAndUp.value)

// Computed properties
const signedIn = computed(() => {
  const isLoggedIn = userStore.isLoggedIn
  console.log('App.vue signedIn computed:', isLoggedIn)
  return isLoggedIn
})
const user = computed(() => userStore.userInfo)
const activeMenuItem = computed(() => menuItem.value)

// 導航項目現在在 NavigationDrawer 組件中管理

const userMenus: AppMenu[] = [
  {
    icon: "mdi-logout",
    title: "Logout",
    link: "login"
  },
  {
    icon: "mdi-key",
    title: "Change Password",
    link: "changepassword"
  }
]

// Methods
function handleNavigation(item: AppMenu) {
  console.log('handleNavigation called with:', item)
  console.log('Current route:', router.currentRoute.value.name)
  menuItem.value = item.title
  console.log('Navigating to route:', item.link)

  // Close drawer on mobile after navigation
  if (isMobile.value) {
    drawer.value = false
  }

  // 使用路徑進行導航
  router.push('/' + item.link).then(() => {
    console.log('Navigation successful to:', item.link)
    console.log('New route:', router.currentRoute.value.name)
  }).catch((error) => {
    console.error('Navigation failed:', error)
    console.error('Error details:', error)
  })
}

function toggleMini() {
  if (!isMobile.value) {
    mini.value = !mini.value
  }
}

function toggleDrawer() {
  drawer.value = !drawer.value
}

async function handleUserActions(item: AppMenu) {
  menuItem.value = item.title
  if (item.title === "Logout") {
    await userStore.logout()
  }

  // Close drawer on mobile after action
  if (isMobile.value) {
    drawer.value = false
  }

  router.push('/' + item.link)
}

// Handle screen size changes
function handleResize() {
  if (isMobile.value) {
    mini.value = false
    drawer.value = false
  } else {
    drawer.value = true
  }
}

// Lifecycle
onMounted(() => {
  // Initialize drawer state based on screen size
  handleResize()

  // Add resize listener
  window.addEventListener('resize', handleResize)

  router.beforeEach((to, from, next) => {
    if (to.name !== "error") {
      menuItem.value = to.name as string
    }
    next()
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.v-list-item--active {
  color: rgb(var(--v-theme-primary)) !important;
}

/* Android-specific layout adjustments */
.v-navigation-drawer {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.v-app-bar {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Mobile-optimized navigation */
@media (max-width: 959px) {
  .v-navigation-drawer {
    box-shadow: 0 8px 10px -5px rgba(0, 0, 0, 0.2),
                0 16px 24px 2px rgba(0, 0, 0, 0.14),
                0 6px 30px 5px rgba(0, 0, 0, 0.12) !important;
  }
}

/* Touch-friendly list items */
.v-list-item {
  border-radius: 8px !important;
  margin: 2px 8px !important;
}

.v-list-item:hover {
  background-color: rgba(var(--v-theme-primary), 0.08) !important;
}

/* App bar title responsive behavior */
.v-app-bar-title {
  font-weight: 500 !important;
}

@media (max-width: 599px) {
  .v-app-bar-title {
    font-size: 18px !important;
  }
}
</style>

<style>
/* Import Android-specific styles */
@import '@/assets/android-styles.css';

/* Global Android optimizations */
#vcrm {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Ensure proper touch behavior */
* {
  touch-action: manipulation;
}

/* Scrollable areas */
.v-main {
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
</style>
