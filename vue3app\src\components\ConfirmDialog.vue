<template>
  <v-dialog :model-value="dialog" @update:model-value="$emit('update:dialog', $event)" persistent max-width="320">
    <v-card>
      <v-card-title>{{ dialogTitle }}</v-card-title>
      <v-card-text>{{ dialogText }}</v-card-text>
      <v-card-actions>
        <v-btn
          color="green-darken-1"
          variant="text"
          @click="$emit('onConfirm')"
          >Confirm</v-btn
        >
        <v-btn
          color="orange-darken-1"
          variant="text"
          @click="$emit('onCancel')"
          >Cancel</v-btn
        >
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
interface Props {
  dialogText: string
  dialogTitle: string
  dialog: boolean
}

defineProps<Props>()
defineEmits<{
  'update:dialog': [value: boolean]
  'onConfirm': []
  'onCancel': []
}>()
</script>
