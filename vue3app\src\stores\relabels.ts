import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getData, putData, postData, deleteData } from '@/utils/backend-api'
import type { Employee, Relabel, Entity, Product, Category, Remark, Relabelline, Pagination } from '@/types'

export const useRelabelsStore = defineStore('relabels', () => {
  // State
  const items = ref<Entity[]>([])
  const pagination = ref<Pagination>({
    page: 1,
    rowsPerPage: 10,
    sortBy: [],
    descending: [],
    search: '',
    totalItems: 0,
    pages: 0
  })
  const loading = ref(false)
  const employee = ref('')
  const relabelId = ref<number | null>(null)
  const relabel = ref<Relabel>({} as Relabel)
  const relabellines = ref<Relabelline[]>([])
  const product = ref<Product>({} as Product)
  const employees = ref<Employee[]>([])
  const categories = ref<Category[]>([])
  const remarks = ref<Remark[]>([])

  // Getters
  const isLoading = computed(() => loading.value)
  const currentRelabel = computed(() => relabel.value)
  const currentRelabellines = computed(() => relabellines.value)

  // Actions
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setRelabel = (value: Relabel) => {
    // 如果是部分更新，合併現有資料
    if (relabel.value && Object.keys(relabel.value).length > 0) {
      relabel.value = { ...relabel.value, ...value }
    } else {
      relabel.value = value
    }
    // 計算 quantity（明細檔數量）
    if (relabel.value.relabellines && Array.isArray(relabel.value.relabellines)) {
      relabel.value.quantity = relabel.value.relabellines.length
    } else {
      relabel.value.quantity = 0
    }
  }

  const setRelabellines = (value: Relabelline[]) => {
    relabellines.value = value
  }

  const setEmployees = (value: Employee[]) => {
    employees.value = value
  }

  const setCategories = (value: Category[]) => {
    categories.value = value
  }

  const setRemarks = (value: Remark[]) => {
    remarks.value = value
  }

  // API Actions
  const getEmployees = async () => {
    try {
      setLoading(true)
      const res = await getData("employees/tw")
      if (res.data) {
        // 先去重，再處理數據
        const uniqueData = res.data.filter((employee: any, index: number, self: any[]) =>
          index === self.findIndex((e: any) => e.employId === employee.employId)
        )

        const employeeList = uniqueData.map((c: any, index: number) => {
          return {
            // 確保每個項目都有唯一的 key，使用 employId 作為主鍵
            employId: c.employId,
            employNO: c.employNO,
            userName: c.userName,
            // 根據 Vue2 的邏輯，顯示格式為 employNO + userName
            employName: c.employNO + " " + c.userName,
            // 使用 employId 作為 value，與後端 API 一致
            value: c.employId,
            // 為 Vuetify 提供唯一的 key
            key: `employee_${c.employId}_${index}`,
            // 其他可能需要的欄位
            ...c
          }
        })

        console.log('Store: 員工數據處理完成:', employeeList)
        setEmployees(employeeList)
      }
    } catch (error) {
      console.error('獲取員工列表失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const loadRelabels = async (type: string, page: number = 1, limit: number = 10) => {
    try {
      setLoading(true)
      const response = await getData(`relabels/${type}?page=${page}&limit=${limit}`)

      if (response.data) {
        // 檢查是否為新的分頁格式
        if (response.data.data && response.data.pagination) {
          items.value = response.data.data
          pagination.value = {
            ...pagination.value,
            ...response.data.pagination
          }
        } else if (Array.isArray(response.data)) {
          items.value = response.data
          pagination.value.totalItems = response.data.length
        } else {
          items.value = []
          pagination.value.totalItems = 0
        }
      } else {
        items.value = []
        pagination.value.totalItems = 0
      }
      return items.value
    } catch (error) {
      console.error('API Error:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const searchRelabels = async (searchQuery: string) => {
    try {
      setLoading(true)
      const res = await getData("relabels" + searchQuery)
      const relabels = res.data
      relabels.forEach((item: any) => {
        item.quantity = item.relabellines?.length || 0
      })
      items.value = relabels
      pagination.value.totalItems = relabels.length
    } catch (error) {
      console.error('搜尋重貼標列表失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const quickSearch = async (headers: any[], qsFilter: string) => {
    try {
      setLoading(true)
      const res = await getData("relabels")
      const relabels = res.data.filter((r: any) =>
        headers.some((header: any) => {
          const val = r[header.value]
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter.toLowerCase())) ||
            false
          )
        })
      )
      relabels.forEach((item: any) => {
        item.quantity = item.relabellines?.length || 0
      })
      items.value = relabels
      pagination.value.totalItems = relabels.length
    } catch (error) {
      console.error('快速搜尋失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const getCategories = async () => {
    try {
      const res = await getData("categories")
      if (res.data) {
        setCategories(res.data)
      }
    } catch (error) {
      console.error('獲取分類列表失敗:', error)
      throw error
    }
  }

  const getRemarks = async () => {
    try {
      const res = await getData("remarks")
      if (res.data) {
        setRemarks(res.data)
      }
    } catch (error) {
      console.error('獲取備註列表失敗:', error)
      throw error
    }
  }

  const getRelabelById = async (id: string | number) => {
    try {
      setLoading(true)
      console.log('Store: 開始獲取重貼標記錄，ID:', id)
      const res = await getData(`relabels/${id}`)
      console.log('Store: API 響應:', res)

      if (res.data && Array.isArray(res.data) && res.data.length > 0) {
        // 後端返回的是數組，取第一個元素
        const relabelData = res.data[0]
        console.log('Store: 設置主檔數據:', relabelData)
        setRelabel(relabelData)
        relabelId.value = Number(id)

        // 同時獲取明細檔
        if (relabelData.relabellines && Array.isArray(relabelData.relabellines)) {
          console.log('Store: 設置明細檔數據:', relabelData.relabellines)
          setRelabellines(relabelData.relabellines)
        } else {
          console.log('Store: 沒有明細檔數據')
          setRelabellines([])
        }
      } else {
        console.log('Store: API 響應格式不正確或無數據')
        setRelabel({} as Relabel)
        setRelabellines([])
      }
      return res.data
    } catch (error) {
      console.error('獲取重貼標記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const saveRelabel = async (data: Relabel) => {
    try {
      setLoading(true)
      let res

      if (data.id && data.id > 0) {
        // 更新現有記錄 - 使用 PUT 方法
        console.log('Store: 更新現有記錄，ID:', data.id)
        res = await putData(`relabels/${data.id}`, data)
        if (res.data) {
          // PUT 請求通常返回更新後的單個對象
          const updatedRelabel = Array.isArray(res.data) ? res.data[0] : res.data
          setRelabel(updatedRelabel)
          relabelId.value = updatedRelabel.id
        }
      } else {
        // 創建新記錄 - 使用 POST 方法
        console.log('Store: 創建新記錄')
        res = await postData('relabels/', data)
        if (res.data && Array.isArray(res.data) && res.data.length > 0) {
          // 後端返回數組，取第一個元素
          const newRelabel = res.data[0]
          setRelabel(newRelabel)
          relabelId.value = newRelabel.id
        }
      }

      return res.data
    } catch (error) {
      console.error('保存重貼標記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const updateRelabel = async (data: Relabel) => {
    return await saveRelabel(data)
  }

  const deleteRelabel = async (id: number) => {
    try {
      setLoading(true)
      await deleteData(`relabels/${id}`)

      // 如果刪除的是當前記錄，清空狀態
      if (relabelId.value === id) {
        relabel.value = {} as Relabel
        relabellines.value = []
        relabelId.value = null
      }
    } catch (error) {
      console.error('刪除重貼標記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 明細檔操作
  const addRelabellineToRelabel = async (data: Relabelline) => {
    try {
      const res = await postData('relabellines/', data)
      if (res.data) {
        // 重新載入明細檔
        if (relabelId.value) {
          await getRelabelById(relabelId.value)
        }
      }
      return res.data
    } catch (error) {
      console.error('新增明細記錄失敗:', error)
      throw error
    }
  }

  const updateRelabelline = async (data: Relabelline) => {
    try {
      const res = await putData(`relabellines/${data.id}`, data)
      if (res.data) {
        // 重新載入明細檔
        if (relabelId.value) {
          await getRelabelById(relabelId.value)
        }
      }
      return res.data
    } catch (error) {
      console.error('更新明細記錄失敗:', error)
      throw error
    }
  }

  const deleteRelabelline = async (data: Relabelline) => {
    try {
      await deleteData(`relabellines/${data.id}`)

      // 重新載入明細檔
      if (relabelId.value) {
        await getRelabelById(relabelId.value)
      }
    } catch (error) {
      console.error('刪除明細記錄失敗:', error)
      throw error
    }
  }

  // 檢查重複的 QR Code
  const getDuplicateRelabellineByCode = async (code: string): Promise<boolean> => {
    try {
      setLoading(true)
      if (code) {
        const res = await getData(`relabellines/duplicate/${code}`)
        const data = res.data
        if (data !== undefined && data !== null && Array.isArray(data) && data.length > 0) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    } catch (error: any) {
      // 404 錯誤表示沒有重複，這是正常情況
      if (error?.response?.status === 404) {
        console.log('沒有重複的 QR Code，可以繼續')
        return false
      }
      console.error('檢查重複 QR Code 失敗:', error)
      return false
    } finally {
      setLoading(false)
    }
  }

  // 清空明細檔
  const clearRelabelline = () => {
    setLoading(true)
    setRelabellines([])
    setLoading(false)
  }

  // 重置狀態
  const resetState = () => {
    items.value = []
    relabel.value = {} as Relabel
    relabellines.value = []
    relabelId.value = null
    employee.value = ''
    loading.value = false
  }

  return {
    // State
    items,
    pagination,
    loading,
    employee,
    relabelId,
    relabel,
    relabellines,
    product,
    employees,
    categories,
    remarks,

    // Getters
    isLoading,
    currentRelabel,
    currentRelabellines,

    // Actions
    setLoading,
    setRelabel,
    setRelabellines,
    setEmployees,
    setCategories,
    setRemarks,
    getEmployees,
    getCategories,
    getRemarks,
    loadRelabels,
    searchRelabels,
    quickSearch,
    getRelabelById,
    saveRelabel,
    updateRelabel,
    deleteRelabel,
    addRelabellineToRelabel,
    updateRelabelline,
    deleteRelabelline,
    getDuplicateRelabellineByCode,
    clearRelabelline,
    resetState
  }
})
