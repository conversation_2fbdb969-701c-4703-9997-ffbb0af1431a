import { describe, it, expect } from 'vitest'
import {
  createValidationRules,
  shiftValidation,
  groupValidation,
  inspectTypeValidation,
  employeeIdValidation,
  quantityValidation,
  dateRangeValidation,
  validateInspectForm,
  validateInspectlineForm,
  isValidShift,
  isValidGroup,
  isValidInspectType,
  sanitizeInspectData,
  sanitizeInspectlineData
} from '@/utils/validation'

describe('validation utils', () => {
  describe('createValidationRules', () => {
    const rules = createValidationRules()

    describe('required 規則', () => {
      it('應該驗證必填欄位', () => {
        const requiredRule = rules.required[0]

        expect(requiredRule('')).toBe('此欄位為必填')
        expect(requiredRule('   ')).toBe('此欄位為必填')
        expect(requiredRule(null)).toBe('此欄位為必填')
        expect(requiredRule(undefined)).toBe('此欄位為必填')
        expect(requiredRule([])).toBe('此欄位為必填')
        expect(requiredRule('有值')).toBe(true)
        expect(requiredRule(['有值'])).toBe(true)
      })
    })

    describe('number 規則', () => {
      it('應該驗證數字格式', () => {
        const numberRule = rules.number[0]

        expect(numberRule('123')).toBe(true)
        expect(numberRule('123.45')).toBe(true)
        expect(numberRule('-123')).toBe(true)
        expect(numberRule('0')).toBe(true)
        expect(numberRule('')).toBe(true) // 允許空值
        expect(numberRule(null)).toBe(true)
        expect(numberRule('abc')).toBe('請輸入有效數字')
        expect(numberRule('12a')).toBe('請輸入有效數字')
      })
    })

    describe('email 規則', () => {
      it('應該驗證電子郵件格式', () => {
        const emailRule = rules.email![0]

        expect(emailRule('<EMAIL>')).toBe(true)
        expect(emailRule('<EMAIL>')).toBe(true)
        expect(emailRule('')).toBe(true) // 允許空值
        expect(emailRule('invalid-email')).toBe('請輸入有效的電子郵件地址')
        expect(emailRule('@domain.com')).toBe('請輸入有效的電子郵件地址')
        expect(emailRule('test@')).toBe('請輸入有效的電子郵件地址')
      })
    })

    describe('date 規則', () => {
      it('應該驗證日期格式', () => {
        const dateRule = rules.date![0]

        expect(dateRule('2023-12-01')).toBe(true)
        expect(dateRule('2023/12/01')).toBe(true)
        expect(dateRule('')).toBe(true) // 允許空值
        expect(dateRule('invalid-date')).toBe('請輸入有效日期')
        expect(dateRule('2023-13-01')).toBe('請輸入有效日期')
      })
    })

    describe('positive 規則', () => {
      it('應該驗證正數', () => {
        const positiveRule = rules.positive![0]

        expect(positiveRule('1')).toBe(true)
        expect(positiveRule('123.45')).toBe(true)
        expect(positiveRule('')).toBe(true) // 允許空值
        expect(positiveRule('0')).toBe('數值必須大於 0')
        expect(positiveRule('-1')).toBe('數值必須大於 0')
        expect(positiveRule('abc')).toBe('請輸入有效數字')
      })
    })

    describe('integer 規則', () => {
      it('應該驗證整數', () => {
        const integerRule = rules.integer![0]

        expect(integerRule('123')).toBe(true)
        expect(integerRule('-123')).toBe(true)
        expect(integerRule('0')).toBe(true)
        expect(integerRule('')).toBe(true) // 允許空值
        expect(integerRule('123.45')).toBe('請輸入整數')
        expect(integerRule('abc')).toBe('請輸入有效數字')
      })
    })
  })

  describe('特定欄位驗證', () => {
    describe('shiftValidation', () => {
      it('應該驗證勤別', () => {
        const rule = shiftValidation[0]

        expect(rule('1')).toBe(true)
        expect(rule('2')).toBe(true)
        expect(rule('3')).toBe(true)
        expect(rule('')).toBe('請選擇勤別')
        expect(rule('4')).toBe('請選擇有效的勤別')
        expect(rule('A')).toBe('請選擇有效的勤別')
      })
    })

    describe('groupValidation', () => {
      it('應該驗證組別', () => {
        const rule = groupValidation[0]

        expect(rule('A')).toBe(true)
        expect(rule('B')).toBe(true)
        expect(rule('C')).toBe(true)
        expect(rule('D')).toBe(true)
        expect(rule('')).toBe('請選擇組別')
        expect(rule('E')).toBe('請選擇有效的組別')
        expect(rule('1')).toBe('請選擇有效的組別')
      })
    })

    describe('inspectTypeValidation', () => {
      it('應該驗證檢驗類型', () => {
        const rule = inspectTypeValidation[0]

        expect(rule('YARN')).toBe(true)
        expect(rule('CAKE')).toBe(true)
        expect(rule('PACK')).toBe(true)
        expect(rule('')).toBe('請選擇類型')
        expect(rule('OTHER')).toBe('請選擇有效的類型')
      })
    })

    describe('employeeIdValidation', () => {
      it('應該驗證員工ID', () => {
        const rule = employeeIdValidation[0]

        expect(rule(1)).toBe(true)
        expect(rule(123)).toBe(true)
        expect(rule('123')).toBe(true)
        expect(rule(0)).toBe('請選擇人員')
        expect(rule('')).toBe('請選擇人員')
        expect(rule(-1)).toBe('請選擇有效的人員')
        expect(rule('abc')).toBe('請選擇有效的人員')
      })
    })

    describe('quantityValidation', () => {
      it('應該驗證數量', () => {
        const rule = quantityValidation[0]

        expect(rule(1)).toBe(true)
        expect(rule(0)).toBe(true)
        expect(rule('123')).toBe(true)
        expect(rule('')).toBe(true) // 允許空值
        expect(rule(-1)).toBe('數量不能為負數')
        expect(rule(1.5)).toBe('數量必須為整數')
        expect(rule('abc')).toBe('請輸入有效數字')
      })
    })
  })

  describe('dateRangeValidation', () => {
    it('應該驗證日期範圍', () => {
      expect(dateRangeValidation('2023-01-01', '2023-12-31')).toBe(true)
      expect(dateRangeValidation('2023-12-31', '2023-01-01')).toBe('開始日期不能晚於結束日期')
      expect(dateRangeValidation('', '')).toBe(true)
      expect(dateRangeValidation('invalid', '2023-12-31')).toBe('請輸入有效日期')
      expect(dateRangeValidation('2023-01-01', 'invalid')).toBe('請輸入有效日期')
    })
  })

  describe('validateInspectForm', () => {
    it('應該驗證完整的檢驗表單', () => {
      const validData = {
        classDate: '2023-12-01',
        shiftName: '1',
        employId: 123,
        groupName: 'A',
        typeName: 'YARN',
        quantity: 10
      }

      const errors = validateInspectForm(validData)
      expect(Object.keys(errors)).toHaveLength(0)
    })

    it('應該檢測必填欄位錯誤', () => {
      const invalidData = {
        classDate: '',
        shiftName: '',
        employId: 0,
        groupName: '',
        typeName: '',
        quantity: ''
      }

      const errors = validateInspectForm(invalidData)
      expect(errors.classDate).toBeDefined()
      expect(errors.shiftName).toBeDefined()
      expect(errors.employId).toBeDefined()
      expect(errors.groupName).toBeDefined()
      expect(errors.typeName).toBeDefined()
    })

    it('應該檢測特定欄位格式錯誤', () => {
      const invalidData = {
        classDate: '2023-12-01',
        shiftName: '4', // 無效勤別
        employId: 123,
        groupName: 'E', // 無效組別
        typeName: 'OTHER', // 無效類型
        quantity: -1 // 無效數量
      }

      const errors = validateInspectForm(invalidData)
      expect(errors.shiftName).toContain('請選擇有效的勤別')
      expect(errors.groupName).toContain('請選擇有效的組別')
      expect(errors.typeName).toContain('請選擇有效的類型')
      expect(errors.quantity).toContain('數量不能為負數')
    })
  })

  describe('validateInspectlineForm', () => {
    it('應該驗證完整的明細表單', () => {
      const validData = {
        codeName: 'TEST001',
        remarkName: '測試異常',
        quantity: 5
      }

      const errors = validateInspectlineForm(validData)
      expect(Object.keys(errors)).toHaveLength(0)
    })

    it('應該檢測必填欄位錯誤', () => {
      const invalidData = {
        codeName: '',
        remarkName: '',
        quantity: ''
      }

      const errors = validateInspectlineForm(invalidData)
      expect(errors.codeName).toBeDefined()
      expect(errors.remarkName).toBeDefined()
      expect(errors.quantity).toBeDefined()
    })
  })

  describe('類型守衛函數', () => {
    describe('isValidShift', () => {
      it('應該正確識別有效勤別', () => {
        expect(isValidShift('1')).toBe(true)
        expect(isValidShift('2')).toBe(true)
        expect(isValidShift('3')).toBe(true)
        expect(isValidShift('4')).toBe(false)
        expect(isValidShift(1)).toBe(false)
        expect(isValidShift('')).toBe(false)
      })
    })

    describe('isValidGroup', () => {
      it('應該正確識別有效組別', () => {
        expect(isValidGroup('A')).toBe(true)
        expect(isValidGroup('B')).toBe(true)
        expect(isValidGroup('C')).toBe(true)
        expect(isValidGroup('D')).toBe(true)
        expect(isValidGroup('E')).toBe(false)
        expect(isValidGroup('a')).toBe(false)
        expect(isValidGroup('')).toBe(false)
      })
    })

    describe('isValidInspectType', () => {
      it('應該正確識別有效檢驗類型', () => {
        expect(isValidInspectType('YARN')).toBe(true)
        expect(isValidInspectType('CAKE')).toBe(true)
        expect(isValidInspectType('PACK')).toBe(true)
        expect(isValidInspectType('OTHER')).toBe(false)
        expect(isValidInspectType('yarn')).toBe(false)
        expect(isValidInspectType('')).toBe(false)
      })
    })
  })

  describe('數據清理函數', () => {
    describe('sanitizeInspectData', () => {
      it('應該正確清理檢驗數據', () => {
        const dirtyData = {
          id: '123',
          employId: '456',
          quantity: '10',
          classDate: '2023-12-01T10:30:00Z',
          shiftName: '1',
          groupName: 'A',
          typeName: 'YARN',
          extraField: 'should be kept'
        }

        const cleaned = sanitizeInspectData(dirtyData)

        expect(cleaned.id).toBe(123)
        expect(cleaned.employId).toBe(456)
        expect(cleaned.quantity).toBe(10)
        expect(cleaned.classDate).toBe('2023-12-01')
        expect(cleaned.shiftName).toBe('1')
        expect(cleaned.groupName).toBe('A')
        expect(cleaned.typeName).toBe('YARN')
        expect(cleaned.extraField).toBe('should be kept')
      })

      it('應該處理無效數據', () => {
        const invalidData = {
          id: 'invalid',
          employId: 'invalid',
          quantity: 'invalid',
          classDate: '',
          shiftName: 'invalid',
          groupName: 'invalid',
          typeName: 'invalid'
        }

        const cleaned = sanitizeInspectData(invalidData)

        expect(cleaned.id).toBeNull()
        expect(cleaned.employId).toBe(0)
        expect(cleaned.quantity).toBe(0)
        expect(cleaned.classDate).toBe('')
        expect(cleaned.shiftName).toBe('')
        expect(cleaned.groupName).toBe('')
        expect(cleaned.typeName).toBe('YARN') // 默認值
      })
    })

    describe('sanitizeInspectlineData', () => {
      it('應該正確清理明細數據', () => {
        const dirtyData = {
          id: '123',
          inspectId: '456',
          productId: '789',
          remarkId: '101',
          quantity: '5',
          codeName: '  TEST001  ',
          remarkName: '  測試異常  '
        }

        const cleaned = sanitizeInspectlineData(dirtyData)

        expect(cleaned.id).toBe(123)
        expect(cleaned.inspectId).toBe(456)
        expect(cleaned.productId).toBe(789)
        expect(cleaned.remarkId).toBe(101)
        expect(cleaned.quantity).toBe(5)
        expect(cleaned.codeName).toBe('TEST001')
        expect(cleaned.remarkName).toBe('測試異常')
      })

      it('應該處理無效數據', () => {
        const invalidData = {
          id: 'invalid',
          inspectId: 'invalid',
          quantity: 'invalid',
          codeName: '',
          remarkName: ''
        }

        const cleaned = sanitizeInspectlineData(invalidData)

        expect(cleaned.id).toBeNull()
        expect(cleaned.inspectId).toBe(0)
        expect(cleaned.quantity).toBe(1) // 默認值
        expect(cleaned.codeName).toBe('')
        expect(cleaned.remarkName).toBe('')
      })
    })
  })
})
