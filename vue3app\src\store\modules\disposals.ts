import { Component, Vue } from 'vue-facing-decorator'
import { createApp } from 'vue'
import { getData, putData, postData, deleteData } from '@/utils/backend-api'
import { Employee, Disposal, Entity, Product, Category, Disposalline, Pagination, TableHeader } from '@/types'
import { getDefaultPagination, getPagination } from '@/utils/store-util'
import { appModule } from '@/stores/app'
import { get } from 'lodash'

export interface DisposalState {
  items: Entity[]
  pagination: Pagination
  loading: boolean
  employee: string
  disposalId: number | null
  disposal: Disposal
  disposalline: Disposalline[]
  product: Product
  employees: Employee[]
  categories: Category[]
}

@Component({
  name: 'DisposalModule'
})
class DisposalModule extends Vue implements DisposalState {
  items: Entity[] = []
  pagination = getDefaultPagination()
  loading = false
  employee = ""
  disposalId: number | null = null
  disposal = {} as Disposal
  disposalline: Disposalline[] = []
  product = {} as Product
  employees: Employee[] = []
  categories: Category[] = []

  getEmployees = () => {
    getData("employees/tw").then(res => {
      if (res.data) {
        const employees = res.data.map((c: Employee) => {
          c.employName = c.employNO + " " + c.userName
          c.value = c.id
          return c
        })
        this.setEmployees(employees)
      }
    })
  }

  getAllDisposalsByType = async (type: string) => {
    this.setLoading(true)
    try {
      const res = await getData(`disposals/${type}`)
      const disposals = res.data
      this.setDisposal(disposals)
      this.setDataTable(disposals)
    } catch (error) {
      console.error(error)
    } finally {
      this.setLoading(false)
    }
  }

  getDisposalById = (id: string) => {
    if (id) {
      getData("disposals/" + id).then(
        res => {
          const _disposal = res.data
          const disposal = _disposal[0]
          disposal.disposallines = disposal.disposallines?.filter(
            (p: Disposal) => p !== null && p !== undefined
          ) || []
          disposal.quantity = disposal.disposallines?.length || 0
          this.setDisposal(disposal)
          this.setDataTable(disposal.disposallines)
        },
        err => {
          console.log(err)
        }
      )
    } else {
      const disposal = {} as Disposal
      disposal.disposallines = []
      this.setDisposal(disposal)
      this.setLoading(false)
    }
  }

  getProductById = async (id: string) => {
    try {
      this.setLoading(true)
      if (id) {
        const res = await getData("products/" + id)
        const product = res.data
        this.setProduct(product)
      } else {
        this.setProduct({} as Product)
      }
    } catch (err) {
      console.log(err)
    } finally {
      this.setLoading(false)
    }
  }

  getDuplicateDisposallineByCode = async (code: string): Promise<boolean> => {
    try {
      this.setLoading(true)
      if (code) {
        const res = await getData("disposallines/duplicate/" + code)
        const data = res.data
        if (data !== undefined && data !== null) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    } catch (err) {
      console.log(err)
      return false
    } finally {
      this.setLoading(false)
    }
  }

  searchDisposals = (searchQuery: string) => {
    getData("disposals" + searchQuery).then(res => {
      const disposals = res.data
      disposals.forEach((item: Disposal) => {
        item.quantity = item.disposallines?.length || 0
      })
      this.setDataTable(disposals)
      this.setLoading(false)
    })
  }

  quickSearch = (headers: TableHeader[], qsFilter: string): void => {
    getData("disposals").then(res => {
      const disposals = res.data.filter((r: any) =>
        headers.some((header: TableHeader) => {
          const val = get(r, [header.value])
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter)) ||
            false
          )
        })
      )
      disposals.forEach((item: Disposal) => {
        item.quantity = item.disposallines?.length || 0
      })
      this.setDataTable(disposals)
      this.setLoading(false)
    })
  }

  saveDisposal = (disposal: Disposal): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!disposal.id) {
        postData("disposals/", disposal)
          .then(res => {
            const disposal = res.data
            const DisposalId = { id: disposal[0].id }
            const addDisposal = { ...DisposalId, ...this.disposal }
            this.setDisposal(addDisposal)
            if (addDisposal.id !== undefined) {
              this.setDisposalId(addDisposal.id)
            }
            appModule.sendSuccessNotice("New record has been added.")
            appModule.closeNoticeWithDelay(3000)
            resolve()
          })
          .catch(err => {
            console.log(err)
            appModule.sendErrorNotice("Operation failed! Please try again later.")
            appModule.closeNoticeWithDelay(5000)
            reject(err)
          })
      } else {
        putData("disposals/" + disposal.id.toString(), disposal)
          .then(() => {
            appModule.sendSuccessNotice("The record has been updated.")
            appModule.closeNoticeWithDelay(3000)
            resolve()
          })
          .catch(err => {
            console.log(err)
            appModule.sendErrorNotice("Operation failed! Please try again later.")
            appModule.closeNoticeWithDelay(5000)
            reject(err)
          })
      }
    })
  }

  addDisposallineToDisposal = (disposalline: Disposalline) => {
    if (disposalline && this.disposal.id) {
      this.saveDisposalline(disposalline)
      this.getDisposalById(this.disposal.id.toString())
      const newDisposal = this.disposal
      this.setDisposal(newDisposal)
    }
  }

  saveDisposalline = (disposalline: Disposalline) => {
    if (!disposalline.id) {
      postData("disposallines/", disposalline)
        .then(res => {
          const disposalline = res.data
          this.setDisposalline([disposalline])
          appModule.sendSuccessNotice("New record has been added.")
          appModule.closeNoticeWithDelay(3000)
        })
        .catch(err => {
          console.log(err)
          appModule.sendErrorNotice("Operation failed! Please try again later.")
          appModule.closeNoticeWithDelay(5000)
        })
    } else {
      putData("disposals/" + disposalline.id.toString(), disposalline)
        .then(res => {
          const disposal = res.data
          this.setDisposal(disposal)
          appModule.sendSuccessNotice("The record has been updated.")
          appModule.closeNoticeWithDelay(3000)
        })
        .catch(err => {
          console.log(err)
          appModule.sendErrorNotice("Operation failed! Please try again later.")
          appModule.closeNoticeWithDelay(5000)
        })
    }
  }

  deleteDisposal = async (id: number) => {
    try {
      await deleteData(`disposals/${id.toString()}`)
      appModule.sendSuccessNotice("Operation is done.")
      appModule.closeNoticeWithDelay(3000)
    } catch (error) {
      console.error(error)
      appModule.sendErrorNotice("Operation failed! Please try again later.")
      appModule.closeNoticeWithDelay(5000)
    } finally {
      this.setLoading(false)
    }
  }

  deleteDisposalline = (disposalline: Disposalline) => {
    if (disposalline && disposalline.id && this.disposal.id) {
      const disposalId = this.disposal.id
      const disposallineId = disposalline.id
      const { disposallines } = this.disposal
      if (disposallines) {
        disposallines.splice(
          disposallines.findIndex((p: Disposalline) => p.id === disposalline.id),
          1
        )
        this.setDisposalline(disposallines)
        deleteData(`disposallines/${disposallineId.toString()}`)
          .then(() => {
            this.getDisposalById(disposalId.toString())
            const newDisposal = this.disposal
            this.setDisposal(newDisposal)
            appModule.sendSuccessNotice("Operation is done.")
            appModule.closeNoticeWithDelay(3000)
          })
          .catch(err => {
            console.log(err)
            appModule.sendErrorNotice("Operation failed! Please try again later.")
            appModule.closeNoticeWithDelay(5000)
          })
      }
    }
  }

  clearDisposalline = () => {
    this.setLoading(true)
    const disposalline: Disposalline[] = []
    this.setDisposalline(disposalline)
    this.setLoading(false)
  }

  setDataTable = (items: Disposal[]) => {
    const pagination = getPagination(items)
    this.setPagination(pagination)
    this.setItems(items)
  }

  setEmployees = (employees: Employee[]) => {
    this.employees = employees
  }

  setCategories = (categories: Category[]) => {
    this.categories = categories
  }

  setDisposalId = (id: number | null) => {
    this.disposalId = id
  }

  setDisposal = (disposal: Disposal) => {
    this.disposal = disposal
  }

  setDisposalline = (disposalline: Disposalline[]) => {
    this.disposalline = disposalline
  }

  setProduct = (product: Product) => {
    this.product = product
  }

  setItems = (disposals: Disposal[]) => {
    this.items = disposals
  }

  setPagination = (pagination: Pagination) => {
    this.pagination = pagination
  }

  setLoading = (loading: boolean) => {
    this.loading = loading
  }
}

// Create and export a singleton instance
const app = createApp(DisposalModule)
const vm = app.mount(document.createElement('div'))
export const disposalModule = vm as InstanceType<typeof DisposalModule>
