const router = require("express").Router();

const remarksDB = require("../models/remarks-model.js");

// GET ALL REMARKS
router.get("/", async (req, res) => {
  try {
    const remarks = await remarksDB.find();
    res.status(200).json(remarks);
  } catch (err) {
    res.status(500).json({ err: err });
  }
});

// GET REMARK BY ID
router.get("/:id", async (req, res) => {
  const remarkId = req.params.id;
  try {
    const remark = await remarksDB.findById(remarkId);
    if (!remark) {
      res
        .status(404)
        .json({ err: "The specified id does not exist" });
    } else {
      res.status(200).json(remark);
    }
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// INSERT REMARK INTO DB
router.post("/", async (req, res) => {
  const newRemark = req.body;
  if (!newRemark.firstname) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const remark = await remarksDB.addRemark(newRemark);
      res.status(201).json(remark);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

router.put("/:id", async (req, res) => {
  const remarkId = req.params.id;
  const newChanges = req.body;
  if (!newChanges.firstname) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const addChanges = await remarksDB.updateRemark(remarkId, newChanges);
      res.status(200).json(addChanges);
    } catch (err) {
      res.status(500).json({err: err.message });
    }
  }
});

router.delete("/:id", async (req, res) => {
  const remarkId = req.params.id;
  try {
    const deleting = await remarksDB.removeRemark(remarkId);
    res.status(204).json(deleting);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;
