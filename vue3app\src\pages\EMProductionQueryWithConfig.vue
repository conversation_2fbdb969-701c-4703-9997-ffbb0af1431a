<template>
  <AndroidContainer 
    :android-options="androidOptions"
    :show-device-info-panel="true"
    :show-config-info="true"
  >
    <!-- 標題區域 -->
    <v-row>
      <v-col cols="12">
        <v-card :class="cardClass">
          <v-card-title :class="{ 'text-h5': isMobile, 'text-h4': !isMobile }">
            <v-icon class="mr-2" :size="isMobile ? 'default' : 'large'">
              mdi-note-multiple-outline
            </v-icon>
            EM即時生產查詢 (配置版)
            <v-spacer />
            <AndroidButton
              color="brown-lighten-1"
              :size="isMobile ? 'default' : 'small'"
              @click="clearData"
              prepend-icon="mdi-refresh"
            >
              <span v-if="isMobile">清除</span>
            </AndroidButton>
          </v-card-title>
          
          <v-card-text :class="containerClass">
            <!-- 說明區域 -->
            <v-alert 
              type="info" 
              :class="{ 'mb-3': isMobile, 'mb-4': !isMobile }"
              :density="isMobile ? 'compact' : 'default'"
            >
              <strong>🔍 EM即時生產查詢 (統一配置版)</strong>
              <br v-if="isMobile">
              使用統一Android Layout配置系統，無需逐一修改表單。
            </v-alert>
            
            <!-- 搜尋區域 -->
            <v-row :dense="isMobile">
              <v-col cols="12">
                <AndroidTextField
                  ref="searchInput"
                  v-model="searchQuery"
                  label="傳票QRCode 或 Cap QRCode"
                  append-icon="mdi-magnify"
                  @keyup.enter="performSearch"
                  @change="performSearch"
                  counter="27"
                  clearable
                  :auto-focus="!isMobile"
                />
              </v-col>
            </v-row>
            
            <!-- 快速操作按鈕 -->
            <v-row v-if="isMobile" :dense="true" class="mt-2">
              <v-col :cols="getResponsiveCols(6, 4, 3)">
                <AndroidButton
                  color="primary"
                  @click="performSearch"
                  :disabled="!searchQuery || loading"
                  :loading="loading"
                  prepend-icon="mdi-magnify"
                >
                  查詢
                </AndroidButton>
              </v-col>
              <v-col :cols="getResponsiveCols(6, 4, 3)">
                <AndroidButton
                  color="secondary"
                  @click="clearData"
                  :disabled="loading"
                  prepend-icon="mdi-refresh"
                >
                  清除
                </AndroidButton>
              </v-col>
            </v-row>
            
            <!-- 載入指示器 -->
            <v-row v-if="loading" :class="{ 'mt-3': isMobile, 'mt-4': !isMobile }">
              <v-col cols="12">
                <v-progress-linear 
                  indeterminate 
                  color="primary" 
                  :height="isMobile ? 4 : 6"
                />
                <p :class="{ 'text-center mt-2': true, 'text-body-2': isMobile }">
                  查詢中...
                </p>
              </v-col>
            </v-row>
            
            <!-- 查詢結果 -->
            <v-row v-if="searchAttempted && !loading" :class="{ 'mt-3': isMobile, 'mt-4': !isMobile }">
              <v-col cols="12">
                <v-card :class="cardClass" variant="outlined">
                  <v-card-title :class="{ 'text-h6': isMobile, 'text-h5': !isMobile }">
                    <v-icon class="mr-2">mdi-table</v-icon>
                    查詢結果
                  </v-card-title>
                  
                  <v-card-text :class="containerClass">
                    <AndroidDataTable
                      :headers="tableHeaders"
                      :items="items"
                      :loading="loading"
                      no-data-text="查無相關資料，請檢查輸入的QRCode是否正確"
                      card-title-key="tracksheetNO"
                      card-chip-key="tracksheetQty"
                      :show-actions="false"
                    >
                      <!-- 自定義時間欄位 -->
                      <template v-slot:item.tracksheetTime="{ item }">
                        <span>{{ formatDateTime(item.tracksheetTime) }}</span>
                      </template>
                      
                      <!-- 自定義數量欄位 -->
                      <template v-slot:item.tracksheetQty="{ item }">
                        <v-chip 
                          :color="getStatusColor(item.tracksheetQty)" 
                          size="small"
                        >
                          {{ item.tracksheetQty }}
                        </v-chip>
                      </template>
                      
                      <!-- 自定義重量欄位 -->
                      <template v-slot:item.tracksheetNetWeight="{ item }">
                        <v-chip color="success" size="small">
                          {{ item.tracksheetNetWeight }} kg
                        </v-chip>
                      </template>
                    </AndroidDataTable>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            
            <!-- 返回按鈕 -->
            <v-row :class="{ 'mt-3': isMobile, 'mt-4': !isMobile }">
              <v-col :cols="getResponsiveCols(12, 6, 4)">
                <AndroidButton
                  color="grey-darken-1"
                  @click="goBack"
                  prepend-icon="mdi-arrow-left"
                >
                  返回測試中心
                </AndroidButton>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 消息提示 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="snackbarConfig.timeout"
      :location="snackbarConfig.location"
      :class="{ 'mb-12': isMobile }"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <AndroidButton
          color="white"
          variant="text"
          @click="snackbar.show = false"
          force-desktop-style
        >
          關閉
        </AndroidButton>
      </template>
    </v-snackbar>
  </AndroidContainer>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  AndroidContainer, 
  AndroidButton, 
  AndroidTextField, 
  AndroidDataTable,
  useAndroidLayout,
  quickConfigs,
  type UseAndroidLayoutOptions 
} from '@/components/AndroidLayout'

const route = useRoute()
const router = useRouter()

// Android Layout配置
const androidOptions: UseAndroidLayoutOptions = {
  config: quickConfigs.development, // 使用開發模式配置
  showDeviceInfo: true,
  enableAutoFocus: true,
  customLoadingTime: {
    mobile: 800,
    desktop: 1200
  }
}

// 使用Android Layout composable
const {
  isMobile,
  cardClass,
  containerClass,
  snackbarConfig,
  getResponsiveCols,
  getOptimizedLoadingTime,
  getStatusColor,
  formatDateTime
} = useAndroidLayout(androidOptions)

// 響應式數據
const searchInput = ref()
const loading = ref(false)
const searchAttempted = ref(false)
const searchQuery = ref('')
const items = ref([])

// QRCode類型識別
const isYarnQRcode = ref(false)
const isCakeTrackSheetNO = ref(false)
const isYarnTrackSheetNO = ref(false)

const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

// 表格標題
const tableHeaders = [
  { title: '傳票號', key: 'tracksheetNO', align: 'start' },
  { title: '時間', key: 'tracksheetTime', align: 'start' },
  { title: '爐號', key: 'furnaceName', align: 'start' },
  { title: '產品名稱', key: 'productName', align: 'start' },
  { title: '數量', key: 'tracksheetQty', align: 'center' },
  { title: '重量(kg)', key: 'tracksheetNetWeight', align: 'center' }
]

// QRCode類型識別函數
const identifyQRCodeType = (query: string) => {
  isYarnQRcode.value = false
  isCakeTrackSheetNO.value = false
  isYarnTrackSheetNO.value = false
  
  if (!query) return
  
  if (/^\d{14}$/.test(query)) {
    isYarnQRcode.value = true
  } else if (query.toUpperCase().startsWith('CK')) {
    isCakeTrackSheetNO.value = true
  } else if (query.toUpperCase().startsWith('YN')) {
    isYarnTrackSheetNO.value = true
  }
}

// 搜尋功能
const performSearch = async () => {
  const query = searchQuery.value?.trim()
  
  if (!query) {
    showMessage('請輸入傳票單號或Cap QRCode', 'error')
    return
  }
  
  loading.value = true
  searchAttempted.value = true
  items.value = []
  
  identifyQRCodeType(query)
  
  try {
    // 使用配置的優化載入時間
    await new Promise(resolve => setTimeout(resolve, getOptimizedLoadingTime()))
    
    // 模擬查詢結果
    if (isYarnQRcode.value) {
      items.value = [
        {
          biName: 'BI-001',
          productName: 'Yarn產品A',
          furnaceName: 'F001',
          tracksheetQty: 75,
          tracksheetTime: new Date().toISOString()
        }
      ]
      showMessage('Yarn QRCode查詢完成', 'success')
    } else if (isCakeTrackSheetNO.value) {
      items.value = [
        {
          tracksheetNO: query,
          furnaceName: 'F001',
          productName: 'Cake產品A',
          tracksheetQty: 50,
          tracksheetNetWeight: 125.5,
          tracksheetTime: new Date().toISOString()
        }
      ]
      showMessage('Cake傳票查詢完成', 'success')
    } else {
      items.value = [
        {
          tracksheetNO: query,
          tracksheetTime: new Date().toISOString(),
          furnaceName: 'F003',
          productName: '一般產品',
          tracksheetQty: 100,
          tracksheetNetWeight: 250.0
        }
      ]
      showMessage('查詢完成', 'success')
    }
  } catch (error) {
    showMessage('查詢失敗，請重試', 'error')
  } finally {
    loading.value = false
  }
}

// 清除數據
const clearData = () => {
  searchQuery.value = ''
  items.value = []
  searchAttempted.value = false
  isYarnQRcode.value = false
  isCakeTrackSheetNO.value = false
  isYarnTrackSheetNO.value = false
  showMessage('數據已清除', 'info')
  
  nextTick(() => {
    if (searchInput.value && !isMobile.value) {
      searchInput.value.focus()
    }
  })
}

// 返回功能
const goBack = () => {
  router.push('/vue3-form-test-center')
}

// 顯示消息
const showMessage = (message: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}

// 生命週期
onMounted(() => {
  showMessage('EM即時生產查詢 (配置版) 已準備就緒', 'info')
})
</script>

<style scoped>
/* 組件特定樣式 */
.v-card {
  transition: all 0.3s ease;
}

.v-card:hover {
  transform: translateY(-1px);
}

/* 響應式間距 */
@media (max-width: 599px) {
  .v-card-title {
    font-size: 18px !important;
    padding: 12px 16px !important;
  }
}
</style>
