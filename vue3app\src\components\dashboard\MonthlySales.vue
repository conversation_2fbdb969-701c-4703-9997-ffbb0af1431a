<template>
  <div :style="styles">
    <canvas ref="chart" :height="height"></canvas>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-facing-decorator'
import { Bar } from 'vue-chartjs'
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js'

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend)

@Component({
  name: 'MonthlySalesChart'
})
export default class MonthlySalesChart extends Vue {
  @Prop({ default: 150 }) height!: number

  private get styles() {
    return {
      "margin-left": '0px',
      "background": '#e8757857'
    }
  }

  mounted() {
    const chart = new ChartJS(this.$refs.chart as HTMLCanvasElement, {
      type: 'bar',
      data: {
        labels: [
          "Jan",
          "Feb",
          "Mar",
          "Apr",
          "May",
          "Jun",
          "Jul",
          "Aug",
          "Sep",
          "Oct",
          "Nov",
          "Dec"
        ],
        datasets: [
          {
            label: "Sales",
            backgroundColor: "#36A2EB",
            data: [40, 39, 10, 40, 39, 80, 40, 20, 12, 11, 25, 36]
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }
}
</script>
