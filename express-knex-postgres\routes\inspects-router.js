const router = require("express").Router();
const moment = require('moment');

const inspectsDB = require("../models/inspects-model.js");
const employeesDB = require("../models/employees-model.js");
const inspectlinesDB = require("../models/inspectlines-model.js");
const categoriesDB = require("../models/categories-model.js");
const remarksDB = require("../models/remarks-model.js");

// GET ALL INSPECTS WITH PAGINATION
router.get("/yarn", async (req, res) => {
  try {
    const urlPath = req.originalUrl.split('/')[3].split('?')[0]; // 移除查詢參數
    const type = urlPath.toUpperCase(); 
    
    if (!type){
      return res.status(400).json({ error: "Type parameter is missing." });
    }

    // 檢查是否有分頁參數
    const hasPageParams = req.query.page || req.query.limit;
    
    if (hasPageParams) {
      // 使用分頁查詢
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const offset = (page - 1) * limit;

      // 獲取總數
      const totalCount = await inspectsDB.countByType(type);
      
      // 獲取分頁資料
      const inspects = await inspectsDB.findByTypeWithPagination(type, limit, offset);
      
      const employees = await employeesDB.find();
      
      // Map employees to inspect
      inspects.forEach(mem => {
        const employee = employees.find(info => info.employId === mem.employId);
        if (employee) {
          mem.employName = `${employee.employNO} ${employee.userName}`;
        }
      });

      // Format dates
      inspects.forEach(mem => {
        mem.classDate = moment(mem.classDate).format("YYYY-M-D");
      });

      // 計算分頁資訊
      const totalPages = Math.ceil(totalCount / limit);
      
      res.status(200).json({
        data: inspects,
        pagination: {
          page: page,
          limit: limit,
          totalItems: totalCount,
          totalPages: totalPages,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      });
    } else {
      // 使用原來的查詢（不分頁）
      const inspects = await inspectsDB.findByType(type);
      const employees = await employeesDB.find();
      
      // Map employees to inspect
      inspects.forEach(mem => {
        const employee = employees.find(info => info.employId === mem.employId);
        if (employee) {
          mem.employName = `${employee.employNO} ${employee.userName}`;
        }
      });

      // Format dates
      inspects.forEach(mem => {
        mem.classDate = moment(mem.classDate).format("YYYY-M-D");
      });

      // 為了兼容性，也返回分頁格式
      res.status(200).json({
        data: inspects,
        pagination: {
          page: 1,
          limit: inspects.length,
          totalItems: inspects.length,
          totalPages: 1,
          hasNextPage: false,
          hasPrevPage: false
        }
      });
    }
  } catch (err) {
    console.error('API Error:', err);
    res.status(500).json({ err: err });
  }
});

// GET INSPECT BY ID
router.get("/:id", async (req, res) => {
  const inspectId = req.params.id;
  
  try {
    const inspect = await inspectsDB.findById(inspectId);
    
    if (!inspect) {
      return res.status(404).json({ err: "The inspect with the specified id does not exist" });
    }

    const inspectlines = await inspectlinesDB.findById(inspect[0].id);
    const employee = await employeesDB.findById(inspect[0].employId);    
    const categories = await categoriesDB.find();
    const remarks = await remarksDB.find();

    // Map employees to inspect
    inspect.forEach(mem => {      
      mem.employName = `${employee[0].employNO} ${employee[0].userName}`;      
    });

    // Format dates
    inspect.forEach(mem => {
      mem.classDate = moment(mem.classDate).format("YYYY-M-D");
    });
    
    inspectlines.forEach(mem => {
      const pDate = moment(mem.productDate);
      const wDate = moment(mem.workDate);
      mem.dryTime = wDate.diff(pDate,'hours');
      mem.productDate = moment(mem.productDate).format("YYYY-M-D HH:mm");
      mem.workDate = moment(mem.workDate).format("YYYY-M-D HH:mm");
    });

    // Map categories to inspectlines
    inspectlines.forEach(mem => {
      const category = categories.find(info => info.categoryId === mem.categoryId);
      if (category) {
        mem.categoryName = category.categoryName;
      }
    });

    // Map remarks to inspectlines
    inspectlines.forEach(mem => {
      const remark = remarks.find(info => info.remarkId === mem.remarkId);
      if (remark) {
        mem.remarkName = remark.remarkName;
      }
    });

    // Assign inspectlines to respective inspect
    inspect.forEach(mem => {
      mem.inspectlines = inspectlines.filter(d => d.inspectId === mem.id);
    });

    res.status(200).json(inspect);
  } catch (err) {
    res.status(500).json({ err: "The inspect information could not be retrieved" });
  }
});

// INSERT INSPECT INTO DB
router.post("/", async (req, res) => {
  const newInspect = req.body; 
  if (!newInspect.shiftName) {
    res.status(404).json({ err: "Please provide the full data" });
  } else {
    try {
      const inspect = await inspectsDB.addInspect(newInspect);
      res.status(201).json(inspect);
    } catch (err) {
      res.status(500).json({ err: "Error in adding inspect" });
    }
  }
});

// UPDATE INSPECT INTO DB
router.put("/:id", async (req, res) => {
  const inspectId = req.params.id;
  const newChanges = req.body;
  if (!newChanges.shiftName) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      await inspectsDB.updateInspect(inspectId, newChanges);
      res.status(200).json({inspectId, newChanges});
    } catch (err) {
      res.status(500).json({ err: "Error in updating inspect" });
    }
  }
});

// REMOVE INSPECT INTO DB
router.delete("/:id", async (req, res) => {
  const inspectId = req.params.id;
  try {
    const deleting =await inspectsDB.removeInspect(inspectId);
    res.status(204).json(deleting);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;
