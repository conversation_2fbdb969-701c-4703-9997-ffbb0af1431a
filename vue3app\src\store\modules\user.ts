import { Component, Vue } from 'vue-facing-decorator'
import { login } from '@/utils/demo-api'

export interface User {
  username: string
  password: string
}

export interface UserInfo {
  id: number
  username: string
  email: string
  roles: string[]
}

@Component({
  name: 'UserStore'
})
export class UserStore extends Vue {
  public token = ''
  private user: User = {} as User
  private userInfo: UserInfo = {} as UserInfo
  private status = ''

  get isLoggedIn(): boolean {
    return !!this.token
  }

  get authStatus(): string {
    return this.status
  }

  async login(user: User) {
    try {
      const resp = await login('auth/login', user)
      const token = resp.data.token
      localStorage.setItem('token', token)
      this.token = token
      this.user = user
      return resp
    } catch (err) {
      localStorage.removeItem('token')
      throw err
    }
  }

  logout() {
    localStorage.removeItem('token')
    this.token = ''
    this.user = {} as User
  }

  setUserInfo(userInfo: UserInfo) {
    this.userInfo = userInfo
  }
}

export const userStore = new UserStore({ name: 'UserStore' }, {})
export const userModule = userStore
