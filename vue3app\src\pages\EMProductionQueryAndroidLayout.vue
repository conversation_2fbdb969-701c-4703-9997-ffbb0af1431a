<template>
  <v-container fluid :class="{ 'pa-2': isMobile, 'pa-4': !isMobile }">
    <!-- 標題區域 - Android優化 -->
    <v-row>
      <v-col cols="12">
        <v-card :elevation="isMobile ? 1 : 2">
          <v-card-title :class="{ 'text-h5': isMobile, 'text-h4': !isMobile }">
            <v-icon class="mr-2" :size="isMobile ? 'default' : 'large'">
              mdi-note-multiple-outline
            </v-icon>
            EM即時生產查詢 (Android)
            <v-spacer />
            <v-btn
              color="brown-lighten-1"
              :size="isMobile ? 'default' : 'small'"
              :icon="!isMobile"
              @click="clearData"
              :class="{ 'mr-2': !isMobile }"
            >
              <v-icon>mdi-refresh</v-icon>
              <span v-if="isMobile" class="ml-2">清除</span>
            </v-btn>
          </v-card-title>

          <v-card-text :class="{ 'pa-3': isMobile, 'pa-4': !isMobile }">
            <!-- 說明區域 - Android優化 -->
            <v-alert
              type="info"
              :class="{ 'mb-3': isMobile, 'mb-4': !isMobile }"
              :density="isMobile ? 'compact' : 'default'"
            >
              <strong>🔍 EM即時生產查詢</strong>
              <br v-if="isMobile">
              請輸入傳票QRCode或Cap QRCode進行查詢。
            </v-alert>

            <!-- 搜尋區域 - Android觸摸優化 -->
            <v-row :dense="isMobile">
              <v-col cols="12">
                <v-text-field
                  ref="searchInput"
                  v-model="searchQuery"
                  label="傳票QRCode 或 Cap QRCode"
                  :append-icon="isMobile ? 'mdi-magnify' : 'mdi-magnify'"
                  @keyup.enter="performSearch"
                  @change="performSearch"
                  counter="27"
                  variant="outlined"
                  :density="isMobile ? 'comfortable' : 'default'"
                  :single-line="!isMobile"
                  hide-details="auto"
                  clearable
                  :autofocus="!isMobile"
                  :style="{ fontSize: isMobile ? '16px' : '14px' }"
                >
                  <template v-slot:append-inner v-if="isMobile">
                    <v-btn
                      icon
                      size="small"
                      @click="performSearch"
                      :disabled="!searchQuery"
                    >
                      <v-icon>mdi-magnify</v-icon>
                    </v-btn>
                  </template>
                </v-text-field>
              </v-col>
            </v-row>

            <!-- 快速操作按鈕 - Android觸摸優化 -->
            <v-row v-if="isMobile" :dense="true" class="mt-2">
              <v-col cols="6">
                <v-btn
                  color="primary"
                  block
                  size="large"
                  @click="performSearch"
                  :disabled="!searchQuery || loading"
                  :loading="loading"
                >
                  <v-icon left>mdi-magnify</v-icon>
                  查詢
                </v-btn>
              </v-col>
              <v-col cols="6">
                <v-btn
                  color="secondary"
                  block
                  size="large"
                  @click="clearData"
                  :disabled="loading"
                >
                  <v-icon left>mdi-refresh</v-icon>
                  清除
                </v-btn>
              </v-col>
            </v-row>

            <!-- 載入指示器 - Android優化 -->
            <v-row v-if="loading" :class="{ 'mt-3': isMobile, 'mt-4': !isMobile }">
              <v-col cols="12">
                <v-progress-linear
                  indeterminate
                  color="primary"
                  :height="isMobile ? 4 : 6"
                />
                <p :class="{ 'text-center mt-2': true, 'text-body-2': isMobile }">
                  查詢中...
                </p>
              </v-col>
            </v-row>

            <!-- 查詢結果 - Android響應式表格 -->
            <v-row v-if="searchAttempted && !loading && items.length > 0" :class="{ 'mt-3': isMobile, 'mt-4': !isMobile }">
              <v-col cols="12">
                <v-card variant="outlined" :elevation="isMobile ? 0 : 1">
                  <v-card-title :class="{ 'text-h6': isMobile, 'text-h5': !isMobile }">
                    <v-icon class="mr-2">mdi-table</v-icon>
                    查詢結果
                  </v-card-title>

                  <v-card-text :class="{ 'pa-2': isMobile, 'pa-4': !isMobile }">
                    <!-- 移動端使用卡片列表 -->
                    <template v-if="isMobile">
                      <v-list>
                        <v-list-item
                          v-for="(item, index) in items"
                          :key="index"
                          :class="{ 'mb-2': true }"
                        >
                          <v-card variant="outlined" class="w-100">
                            <v-card-text class="pa-3">
                              <div class="d-flex justify-space-between align-center mb-2">
                                <span class="text-subtitle2 font-weight-bold">
                                  {{ item.tracksheetNO || item.biName || '項目' }} #{{ index + 1 }}
                                </span>
                                <v-chip
                                  :color="getStatusColor(item)"
                                  size="small"
                                  variant="flat"
                                >
                                  {{ item.tracksheetQty || item.cakeWeight || '數量' }}
                                </v-chip>
                              </div>

                              <v-divider class="mb-2"></v-divider>

                              <div class="text-body-2">
                                <div v-if="item.furnaceName" class="mb-1">
                                  <strong>爐號:</strong> {{ item.furnaceName }}
                                </div>
                                <div v-if="item.productName" class="mb-1">
                                  <strong>產品:</strong> {{ item.productName }}
                                </div>
                                <div v-if="item.tracksheetNetWeight" class="mb-1">
                                  <strong>重量:</strong> {{ item.tracksheetNetWeight }} kg
                                </div>
                                <div v-if="item.tracksheetTime" class="mb-1">
                                  <strong>時間:</strong> {{ formatDateTime(item.tracksheetTime) }}
                                </div>
                              </div>
                            </v-card-text>
                          </v-card>
                        </v-list-item>
                      </v-list>
                    </template>

                    <!-- 桌面端使用數據表格 -->
                    <template v-else>
                      <v-data-table
                        :headers="selectedHeaders"
                        :items="items"
                        :items-per-page="10"
                        class="elevation-1"
                        no-data-text="查無資料"
                        :density="isMobile ? 'compact' : 'default'"
                      >
                        <template v-slot:item.tracksheetTime="{ item }">
                          <span>{{ formatDateTime(item.tracksheetTime) }}</span>
                        </template>

                        <template v-slot:item.tracksheetQty="{ item }">
                          <v-chip color="primary" size="small">
                            {{ item.tracksheetQty }}
                          </v-chip>
                        </template>

                        <template v-slot:item.tracksheetNetWeight="{ item }">
                          <v-chip color="success" size="small">
                            {{ item.tracksheetNetWeight }} kg
                          </v-chip>
                        </template>

                        <template v-slot:no-data>
                          <v-alert type="warning" class="ma-4">
                            查無相關資料，請檢查輸入的QRCode是否正確。
                          </v-alert>
                        </template>
                      </v-data-table>
                    </template>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>

            <!-- 無資料提示 - Android優化 -->
            <v-row v-if="searchAttempted && !loading && items.length === 0" :class="{ 'mt-3': isMobile, 'mt-4': !isMobile }">
              <v-col cols="12">
                <v-alert
                  type="warning"
                  :class="{ 'ma-2': isMobile, 'ma-4': !isMobile }"
                  :density="isMobile ? 'compact' : 'default'"
                >
                  <v-icon class="mr-2">mdi-alert-circle</v-icon>
                  查無相關資料，請檢查輸入的QRCode是否正確。
                </v-alert>
              </v-col>
            </v-row>

            <!-- 返回按鈕 - Android優化 -->
            <v-row :class="{ 'mt-3': isMobile, 'mt-4': !isMobile }">
              <v-col :cols="isMobile ? 12 : 6">
                <v-btn
                  color="grey-darken-1"
                  :block="isMobile"
                  :size="isMobile ? 'large' : 'default'"
                  @click="goBack"
                >
                  <v-icon :left="!isMobile">mdi-arrow-left</v-icon>
                  <span v-if="isMobile" class="ml-2">返回測試中心</span>
                  <span v-else>返回</span>
                </v-btn>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Android設備信息 - 開發模式 -->
    <v-row v-if="showDevInfo" :class="{ 'mt-3': isMobile, 'mt-4': !isMobile }">
      <v-col cols="12">
        <v-card variant="outlined" :elevation="isMobile ? 0 : 1">
          <v-card-title :class="{ 'text-h6': isMobile, 'text-h5': !isMobile }">
            <v-icon class="mr-2">mdi-android</v-icon>
            Android設備信息
          </v-card-title>

          <v-card-text :class="{ 'pa-2': isMobile, 'pa-4': !isMobile }">
            <v-list :density="isMobile ? 'compact' : 'default'">
              <v-list-item>
                <template v-slot:prepend>
                  <v-icon :color="isMobile ? 'success' : 'primary'">
                    {{ isMobile ? 'mdi-cellphone' : 'mdi-desktop-classic' }}
                  </v-icon>
                </template>
                <v-list-item-title>設備類型</v-list-item-title>
                <v-list-item-subtitle>
                  {{ isMobile ? '移動設備' : '桌面設備' }} ({{ currentBreakpoint }})
                </v-list-item-subtitle>
              </v-list-item>

              <v-list-item>
                <template v-slot:prepend>
                  <v-icon color="info">mdi-monitor</v-icon>
                </template>
                <v-list-item-title>螢幕尺寸</v-list-item-title>
                <v-list-item-subtitle>
                  {{ screenWidth }} × {{ screenHeight }}
                </v-list-item-subtitle>
              </v-list-item>

              <v-list-item>
                <template v-slot:prepend>
                  <v-icon :color="touchSupported ? 'success' : 'warning'">
                    {{ touchSupported ? 'mdi-gesture-tap' : 'mdi-mouse' }}
                  </v-icon>
                </template>
                <v-list-item-title>觸摸支援</v-list-item-title>
                <v-list-item-subtitle>
                  {{ touchSupported ? '支援觸摸操作' : '僅支援滑鼠操作' }}
                </v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 浮動操作按鈕 - Android風格 -->
    <v-fab
      v-if="isMobile"
      icon="mdi-information"
      color="primary"
      size="small"
      @click="showDevInfo = !showDevInfo"
      :style="{ bottom: '80px', right: '16px' }"
    />

    <!-- 消息提示 - Android優化 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="3000"
      :location="isMobile ? 'bottom' : 'top'"
      :class="{ 'mb-12': isMobile }"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          :size="isMobile ? 'small' : 'default'"
          @click="snackbar.show = false"
        >
          關閉
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDisplay } from 'vuetify'

const route = useRoute()
const router = useRouter()
const { mobile, smAndDown, mdAndUp, name } = useDisplay()

// 響應式數據
const searchInput = ref()
const loading = ref(false)
const searchAttempted = ref(false)
const searchQuery = ref('')
const items = ref([])
const showDevInfo = ref(false)

// 設備信息
const screenWidth = ref(window.innerWidth)
const screenHeight = ref(window.innerHeight)
const touchSupported = ref('ontouchstart' in window)

// QRCode類型識別
const isYarnQRcode = ref(false)
const isCakeTrackSheetNO = ref(false)
const isYarnTrackSheetNO = ref(false)

const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

// 計算屬性
const isMobile = computed(() => mobile.value || smAndDown.value)
const isTablet = computed(() => !mobile.value && !mdAndUp.value)
const currentBreakpoint = computed(() => name.value)

// 表格標題配置 - 根據設備類型動態調整
const allHeaders = [
  { title: '傳票號', key: 'tracksheetNO', align: 'start' },
  { title: '時間', key: 'tracksheetTime', align: 'start' },
  { title: '爐號', key: 'furnaceName', align: 'start' },
  { title: '產品名稱', key: 'productName', align: 'start' },
  { title: '數量', key: 'tracksheetQty', align: 'center' },
  { title: '重量(kg)', key: 'tracksheetNetWeight', align: 'center' }
]

const mobileHeaders = [
  { title: '傳票號', key: 'tracksheetNO', align: 'start' },
  { title: '產品', key: 'productName', align: 'start' },
  { title: '數量', key: 'tracksheetQty', align: 'center' }
]

const selectedHeaders = computed(() => {
  return isMobile.value ? mobileHeaders : allHeaders
})

// QRCode類型識別函數
const identifyQRCodeType = (query: string) => {
  // 重置所有類型標記
  isYarnQRcode.value = false
  isCakeTrackSheetNO.value = false
  isYarnTrackSheetNO.value = false

  if (!query) return

  // Yarn QRCode: 14位數字
  if (/^\d{14}$/.test(query)) {
    isYarnQRcode.value = true
    return
  }

  // Cake傳票: CK開頭
  if (query.toUpperCase().startsWith('CK')) {
    isCakeTrackSheetNO.value = true
    return
  }

  // Yarn傳票: YN開頭
  if (query.toUpperCase().startsWith('YN')) {
    isYarnTrackSheetNO.value = true
    return
  }
}

// 搜尋功能
const performSearch = async () => {
  const query = searchQuery.value?.trim()

  if (!query) {
    showMessage('請輸入傳票單號或Cap QRCode', 'error')
    return
  }

  loading.value = true
  searchAttempted.value = true
  items.value = []

  // 識別QRCode類型
  identifyQRCodeType(query)

  try {
    // 模擬API查詢 - Android優化的載入時間
    await new Promise(resolve => setTimeout(resolve, isMobile.value ? 1000 : 1500))

    // 模擬不同類型的查詢結果
    if (isYarnQRcode.value) {
      items.value = [
        {
          biName: 'BI-001',
          texName: 'TEX-001',
          batchName: 'LOT20241201',
          dryTime: 24,
          cakeWeight: 2500,
          productName: 'Yarn產品A',
          furnaceName: 'F001'
        }
      ]
      showMessage('Yarn QRCode查詢完成', 'success')
    } else if (isCakeTrackSheetNO.value) {
      items.value = [
        {
          tracksheetNO: query,
          furnaceName: 'F001',
          productName: 'Cake產品A',
          tracksheetQty: 50,
          tracksheetNetWeight: 125.5,
          tracksheetTime: new Date().toISOString()
        }
      ]
      showMessage('Cake傳票查詢完成', 'success')
    } else if (isYarnTrackSheetNO.value) {
      items.value = [
        {
          tracksheetNO: query,
          furnaceName: 'F002',
          productName: 'Yarn產品B',
          tracksheetQty: 80,
          tracksheetTime: new Date().toISOString()
        }
      ]
      showMessage('Yarn傳票查詢完成', 'success')
    } else {
      items.value = [
        {
          tracksheetNO: query,
          tracksheetTime: new Date().toISOString(),
          furnaceName: 'F003',
          productName: '一般產品',
          tracksheetQty: 100,
          tracksheetNetWeight: 250.0
        }
      ]
      showMessage('一般查詢完成', 'success')
    }
  } catch (error) {
    showMessage('查詢失敗，請重試', 'error')
  } finally {
    loading.value = false
  }
}

// 清除數據
const clearData = () => {
  searchQuery.value = ''
  items.value = []
  searchAttempted.value = false
  isYarnQRcode.value = false
  isCakeTrackSheetNO.value = false
  isYarnTrackSheetNO.value = false
  showMessage('數據已清除', 'info')

  // 重新聚焦到搜尋框
  nextTick(() => {
    if (searchInput.value && !isMobile.value) {
      searchInput.value.focus()
    }
  })
}

// 返回功能
const goBack = () => {
  router.push('/vue3-form-test-center')
}

// 格式化日期時間
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return isMobile.value
    ? date.toLocaleDateString('zh-TW')
    : date.toLocaleString('zh-TW')
}

// 獲取狀態顏色
const getStatusColor = (item: any) => {
  if (item.tracksheetQty) {
    return item.tracksheetQty > 50 ? 'success' : 'warning'
  }
  if (item.cakeWeight) {
    return item.cakeWeight > 2000 ? 'success' : 'info'
  }
  return 'primary'
}

// 顯示消息
const showMessage = (message: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}

// 更新螢幕尺寸
const updateScreenSize = () => {
  screenWidth.value = window.innerWidth
  screenHeight.value = window.innerHeight
}

// 生命週期
onMounted(() => {
  window.addEventListener('resize', updateScreenSize)
  showMessage('EM即時生產查詢 (Android版) 已準備就緒', 'info')

  // 非移動端設備聚焦到搜尋框
  if (!isMobile.value) {
    nextTick(() => {
      if (searchInput.value) {
        searchInput.value.focus()
      }
    })
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenSize)
})
</script>

<style scoped>
.v-card {
  margin-bottom: 8px;
}

/* Android特定樣式優化 */
@media (max-width: 599px) {
  .v-card-title {
    font-size: 18px !important;
    padding: 12px 16px !important;
  }

  .v-card-text {
    padding: 8px 16px 16px !important;
  }

  .v-text-field input {
    font-size: 16px !important; /* 防止iOS縮放 */
  }

  .v-btn {
    min-height: 44px !important; /* Android觸摸目標最小尺寸 */
  }
}

/* 觸摸優化 */
.v-btn {
  touch-action: manipulation;
}

/* 列表項目間距優化 */
.v-list-item {
  min-height: 48px !important;
}

/* 浮動按鈕定位 */
.v-fab {
  position: fixed !important;
  z-index: 1005 !important;
}

/* 響應式表格優化 */
@media (max-width: 599px) {
  .v-data-table {
    font-size: 14px !important;
  }

  .v-data-table th,
  .v-data-table td {
    padding: 8px 4px !important;
  }
}

/* 卡片列表優化 */
.w-100 {
  width: 100% !important;
}

/* 安全區域適配 */
@supports (padding: max(0px)) {
  .v-container {
    padding-bottom: max(16px, env(safe-area-inset-bottom)) !important;
  }
}
</style>
