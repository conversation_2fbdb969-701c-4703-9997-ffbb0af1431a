<script lang="ts">
import { Component, Vue, Prop } from 'vue-facing-decorator'
import { h } from 'vue'

@Component({
  name: 'form-control'
})
export default class FormControl extends Vue {
  @Prop({ default: '' }) fieldLabel!: string

  render() {
    return h('div', [
      h('v-text-field', {
        label: this.fieldLabel,
        light: true,
        modelValue: this.searchFilter?.contain?.brand,
        'onUpdate:modelValue': (value: string) => {
          if (this.searchFilter?.contain) {
            this.searchFilter.contain.brand = value
          }
        }
      })
    ])
  }

  private get searchFilter() {
    return {
      contain: {
        brand: ''
      }
    }
  }
}
</script>
