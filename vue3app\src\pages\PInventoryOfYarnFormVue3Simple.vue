<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <span class="text-h4">
              <v-icon class="mr-2">mdi-package-variant</v-icon>
              {{ isEditMode ? '編輯Yarn盤點記錄' : '新增Yarn盤點記錄' }}
            </span>
          </v-card-title>
          
          <v-card-text>
            <v-alert type="info" class="mb-4" v-if="!isEditMode">
              <strong>📦 新增Yarn盤點記錄</strong>
              請填寫Yarn盤點記錄的基本資訊。
            </v-alert>
            
            <v-alert type="warning" class="mb-4" v-if="isEditMode">
              <strong>✏️ 編輯Yarn盤點記錄</strong>
              正在編輯ID為 {{ route.params.id }} 的Yarn盤點記錄。
            </v-alert>
            
            <v-form ref="form" v-model="formValid">
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.id"
                    label="單號"
                    :rules="[rules.required]"
                    outlined
                    dense
                    readonly
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.classDate"
                    label="盤點日期"
                    type="date"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.shiftName"
                    :items="shiftOptions"
                    label="勤別"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.employeeId"
                    :items="employeeOptions"
                    item-title="employName"
                    item-value="id"
                    label="盤點人員"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.groupName"
                    :items="groupOptions"
                    label="組別"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.tracksheetNO"
                    label="傳票單號"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.quantity"
                    label="盤點數量"
                    type="number"
                    :rules="[rules.required, rules.number]"
                    outlined
                    dense
                  />
                </v-col>
              </v-row>
            </v-form>
            
            <v-divider class="my-4" />
            
            <!-- 盤點明細表格 -->
            <v-row>
              <v-col cols="12">
                <h3>
                  <v-icon class="mr-2">mdi-format-list-bulleted</v-icon>
                  Yarn盤點明細
                </h3>
                
                <v-card variant="outlined" class="mt-3">
                  <v-card-title>
                    <v-spacer />
                    <v-btn
                      color="primary"
                      @click="addDetailItem"
                      prepend-icon="mdi-plus"
                      size="small"
                    >
                      新增明細
                    </v-btn>
                  </v-card-title>
                  
                  <v-card-text>
                    <v-data-table
                      :headers="detailHeaders"
                      :items="detailItems"
                      :items-per-page="5"
                      class="elevation-1"
                    >
                      <template v-slot:item.actions="{ item }">
                        <v-btn
                          icon="mdi-pencil"
                          size="small"
                          color="primary"
                          @click="editDetailItem(item)"
                          class="mr-2"
                        />
                        <v-btn
                          icon="mdi-delete"
                          size="small"
                          color="error"
                          @click="deleteDetailItem(item.id)"
                        />
                      </template>
                      
                      <template v-slot:no-data>
                        <v-alert type="info" class="ma-4">
                          尚無盤點明細資料，請點擊「新增明細」按鈕添加。
                        </v-alert>
                      </template>
                    </v-data-table>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            
            <v-divider class="my-4" />
            
            <v-row>
              <v-col cols="12">
                <h3>測試狀態</h3>
                <v-list>
                  <v-list-item>
                    <template v-slot:prepend>
                      <v-icon color="success">mdi-check-circle</v-icon>
                    </template>
                    <v-list-item-title>Vue3 Composition API</v-list-item-title>
                    <v-list-item-subtitle>Yarn盤點表單響應式數據正常運作</v-list-item-subtitle>
                  </v-list-item>
                  
                  <v-list-item>
                    <template v-slot:prepend>
                      <v-icon color="success">mdi-check-circle</v-icon>
                    </template>
                    <v-list-item-title>盤點功能</v-list-item-title>
                    <v-list-item-subtitle>盤點明細管理功能正常運作</v-list-item-subtitle>
                  </v-list-item>
                  
                  <v-list-item>
                    <template v-slot:prepend>
                      <v-icon color="success">mdi-check-circle</v-icon>
                    </template>
                    <v-list-item-title>路由參數</v-list-item-title>
                    <v-list-item-subtitle>
                      當前路由: {{ $route.path }}
                      {{ $route.params.id ? `(編輯模式 - ID: ${$route.params.id})` : '(新增模式)' }}
                    </v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>
            
            <v-row class="mt-4">
              <v-col cols="12" md="6">
                <h3>主檔數據 (即時更新)</h3>
                <v-card variant="outlined">
                  <v-card-text>
                    <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
                  </v-card-text>
                </v-card>
              </v-col>
              
              <v-col cols="12" md="6">
                <h3>盤點明細數據 (即時更新)</h3>
                <v-card variant="outlined">
                  <v-card-text>
                    <pre>{{ JSON.stringify(detailItems, null, 2) }}</pre>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          
          <v-card-actions>
            <v-btn
              color="primary"
              @click="saveForm"
              :disabled="!formValid"
              prepend-icon="mdi-content-save"
            >
              {{ isEditMode ? '更新' : '儲存' }}
            </v-btn>
            
            <v-btn
              color="secondary"
              @click="resetForm"
              prepend-icon="mdi-refresh"
            >
              重設
            </v-btn>
            
            <v-btn
              color="error"
              @click="goBack"
              prepend-icon="mdi-arrow-left"
            >
              返回
            </v-btn>
            
            <v-spacer />
            
            <v-chip color="info">
              表單狀態: {{ formValid ? '有效' : '無效' }}
            </v-chip>
            
            <v-chip color="warning" class="ml-2">
              明細數量: {{ detailItems.length }}
            </v-chip>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>
    
    <!-- 明細編輯對話框 -->
    <v-dialog v-model="detailDialog" max-width="800px">
      <v-card>
        <v-card-title>
          <span class="text-h5">{{ editingDetailId ? '編輯' : '新增' }}盤點明細</span>
        </v-card-title>
        
        <v-card-text>
          <v-form ref="detailForm" v-model="detailFormValid">
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="detailData.tracksheetNO"
                  label="傳票單號"
                  :rules="[rules.required]"
                  outlined
                  dense
                />
              </v-col>
              
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="detailData.furnaceName"
                  label="爐別"
                  outlined
                  dense
                />
              </v-col>
              
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="detailData.productName"
                  label="品種"
                  outlined
                  dense
                />
              </v-col>
              
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="detailData.pinventoryT1Qty"
                  label="盤點T1個數"
                  type="number"
                  outlined
                  dense
                />
              </v-col>
              
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="detailData.pinventoryT2Qty"
                  label="盤點T2個數"
                  type="number"
                  outlined
                  dense
                />
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="grey"
            @click="closeDetailDialog"
          >
            取消
          </v-btn>
          <v-btn
            color="primary"
            @click="saveDetailItem"
            :disabled="!detailFormValid"
          >
            {{ editingDetailId ? '更新' : '新增' }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 成功訊息 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          關閉
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 響應式數據
const form = ref()
const detailForm = ref()
const formValid = ref(false)
const detailFormValid = ref(false)

const formData = ref({
  id: 0,
  classDate: new Date().toISOString().slice(0, 10),
  shiftName: '',
  employeeId: 0,
  groupName: '',
  tracksheetNO: '',
  quantity: 0
})

// 盤點明細數據
const detailItems = ref([])
const detailDialog = ref(false)
const editingDetailId = ref(null)

const detailData = ref({
  tracksheetNO: '',
  furnaceName: '',
  productName: '',
  pinventoryT1Qty: 0,
  pinventoryT2Qty: 0
})

const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

// 選項數據
const shiftOptions = ['早班', '中班', '晚班']
const groupOptions = ['A組', 'B組', 'C組', 'D組']

const employeeOptions = ref([
  { id: 1, employName: '張三' },
  { id: 2, employName: '李四' },
  { id: 3, employName: '王五' }
])

// 盤點明細表格標題
const detailHeaders = [
  { title: 'ID', key: 'id', align: 'start' },
  { title: '傳票單號', key: 'tracksheetNO', align: 'start' },
  { title: '爐別', key: 'furnaceName', align: 'start' },
  { title: '品種', key: 'productName', align: 'start' },
  { title: '盤點T1個數', key: 'pinventoryT1Qty', align: 'end' },
  { title: '盤點T2個數', key: 'pinventoryT2Qty', align: 'end' },
  { title: '操作', key: 'actions', align: 'center', sortable: false }
]

// 驗證規則
const rules = {
  required: (value: any) => !!value || '此欄位為必填',
  number: (value: any) => !isNaN(Number(value)) || '請輸入有效數字'
}

// 計算屬性
const isEditMode = computed(() => !!route.params.id)

// 方法
const saveForm = async () => {
  try {
    // 模擬保存操作
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    showMessage(
      isEditMode.value ? 'Yarn盤點記錄更新成功！' : 'Yarn盤點記錄儲存成功！',
      'success'
    )
    
    if (!isEditMode.value) {
      const newId = Math.floor(Math.random() * 1000) + 1
      router.push(`/test-vue3/pinventory-yarn/${newId}`)
    }
  } catch (error) {
    showMessage('操作失敗，請重試', 'error')
  }
}

const resetForm = () => {
  formData.value = {
    id: 0,
    classDate: new Date().toISOString().slice(0, 10),
    shiftName: '',
    employeeId: 0,
    groupName: '',
    tracksheetNO: '',
    quantity: 0
  }
  detailItems.value = []
  form.value?.resetValidation()
  showMessage('表單已重設', 'info')
}

const goBack = () => {
  router.push('/vue3-form-test-center')
}

// 盤點明細操作方法
const addDetailItem = () => {
  editingDetailId.value = null
  detailData.value = {
    tracksheetNO: '',
    furnaceName: '',
    productName: '',
    pinventoryT1Qty: 0,
    pinventoryT2Qty: 0
  }
  detailDialog.value = true
}

const editDetailItem = (item: any) => {
  editingDetailId.value = item.id
  detailData.value = { ...item }
  detailDialog.value = true
}

const saveDetailItem = () => {
  // 簡化驗證：只檢查必填欄位
  if (!detailData.value.tracksheetNO) {
    showMessage('請輸入傳票單號', 'error')
    return
  }
  
  console.log('保存盤點明細:', detailData.value)
  console.log('編輯ID:', editingDetailId.value)
  console.log('當前明細列表:', detailItems.value)
  
  if (editingDetailId.value) {
    // 編輯模式
    const index = detailItems.value.findIndex((item: any) => item.id === editingDetailId.value)
    console.log('找到編輯項目索引:', index)
    if (index > -1) {
      detailItems.value[index] = { ...detailData.value, id: editingDetailId.value }
      showMessage('盤點明細已更新', 'success')
    }
  } else {
    // 新增模式
    const newDetail = { ...detailData.value, id: Date.now() }
    console.log('新增盤點明細:', newDetail)
    detailItems.value.push(newDetail)
    showMessage('盤點明細已新增', 'success')
  }
  
  console.log('更新後明細列表:', detailItems.value)
  closeDetailDialog()
}

const closeDetailDialog = () => {
  detailDialog.value = false
  editingDetailId.value = null
  detailForm.value?.resetValidation()
}

const deleteDetailItem = (id: number) => {
  const index = detailItems.value.findIndex((item: any) => item.id === id)
  if (index > -1) {
    detailItems.value.splice(index, 1)
    showMessage('已刪除盤點明細', 'warning')
  }
}

const showMessage = (message: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}

// 生命週期
onMounted(() => {
  if (isEditMode.value) {
    // 模擬載入編輯數據
    formData.value = {
      id: Number(route.params.id),
      classDate: new Date().toISOString().slice(0, 10),
      shiftName: '早班',
      employeeId: 1,
      groupName: 'A組',
      tracksheetNO: 'TS202412001',
      quantity: 100
    }
    
    // 模擬載入盤點明細數據
    detailItems.value = [
      { 
        id: 1, 
        tracksheetNO: 'TS202412001', 
        furnaceName: 'F001',
        productName: 'Yarn-A',
        pinventoryT1Qty: 50,
        pinventoryT2Qty: 48
      },
      { 
        id: 2, 
        tracksheetNO: 'TS202412002', 
        furnaceName: 'F002',
        productName: 'Yarn-B',
        pinventoryT1Qty: 30,
        pinventoryT2Qty: 32
      }
    ]
    
    showMessage(`載入Yarn盤點記錄編輯數據 (ID: ${route.params.id})`, 'info')
  } else {
    showMessage('Yarn盤點記錄新增模式已準備就緒', 'info')
  }
})
</script>

<style scoped>
.v-card {
  margin-bottom: 16px;
}

pre {
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
