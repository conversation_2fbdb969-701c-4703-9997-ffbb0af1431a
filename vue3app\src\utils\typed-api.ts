import { getData, postData, putData, deleteData } from './backend-api'
import type { 
  Inspect, 
  Inspectline, 
  Employee, 
  Product, 
  Category, 
  Remark,
  ApiResponse,
  PaginatedResponse,
  InspectFormData,
  InspectlineFormData,
  InspectSearchFilters,
  OperationResult
} from '@/types/inspect'

// 類型安全的 API 客戶端
export class TypedApiClient {
  private baseUrl: string

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl
  }

  // 通用 API 方法
  private async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any
  ): Promise<OperationResult<T>> {
    try {
      let response: any

      switch (method) {
        case 'GET':
          response = await getData(endpoint, data)
          break
        case 'POST':
          response = await postData(endpoint, data)
          break
        case 'PUT':
          response = await putData(endpoint, data)
          break
        case 'DELETE':
          response = await deleteData(endpoint)
          break
      }

      return {
        success: true,
        data: response.data,
        message: response.message
      }
    } catch (error: any) {
      console.error(`API ${method} ${endpoint} 失敗:`, error)
      return {
        success: false,
        error: error.message || '操作失敗',
        details: error
      }
    }
  }

  // 檢驗主檔 API
  async getInspects(filters?: InspectSearchFilters): Promise<OperationResult<Inspect[]>> {
    const endpoint = filters ? 'inspects/search' : 'inspects/yarn'
    return this.request<Inspect[]>('GET', endpoint, filters)
  }

  async getInspectById(id: number): Promise<OperationResult<Inspect>> {
    const result = await this.request<Inspect[]>('GET', `inspects/${id}`)
    
    if (result.success && Array.isArray(result.data) && result.data.length > 0) {
      return {
        success: true,
        data: result.data[0],
        message: result.message
      }
    }
    
    return result.success 
      ? { success: false, error: '找不到指定的檢驗記錄' }
      : result as OperationResult<Inspect>
  }

  async createInspect(data: InspectFormData): Promise<OperationResult<Inspect>> {
    return this.request<Inspect>('POST', 'inspects', data)
  }

  async updateInspect(id: number, data: InspectFormData): Promise<OperationResult<Inspect>> {
    return this.request<Inspect>('PUT', `inspects/${id}`, data)
  }

  async deleteInspect(id: number): Promise<OperationResult<void>> {
    return this.request<void>('DELETE', `inspects/${id}`)
  }

  // 檢驗明細檔 API
  async getInspectlines(inspectId: number): Promise<OperationResult<Inspectline[]>> {
    return this.request<Inspectline[]>('GET', `inspects/${inspectId}/lines`)
  }

  async getInspectlineById(id: number): Promise<OperationResult<Inspectline>> {
    return this.request<Inspectline>('GET', `inspectlines/${id}`)
  }

  async createInspectline(data: InspectlineFormData): Promise<OperationResult<Inspectline>> {
    return this.request<Inspectline>('POST', 'inspectlines', data)
  }

  async updateInspectline(id: number, data: InspectlineFormData): Promise<OperationResult<Inspectline>> {
    return this.request<Inspectline>('PUT', `inspectlines/${id}`, data)
  }

  async deleteInspectline(id: number): Promise<OperationResult<void>> {
    return this.request<void>('DELETE', `inspectlines/${id}`)
  }

  // 員工 API
  async getEmployees(): Promise<OperationResult<Employee[]>> {
    return this.request<Employee[]>('GET', 'employees/qi')
  }

  async getEmployeeById(id: number): Promise<OperationResult<Employee>> {
    return this.request<Employee>('GET', `employees/${id}`)
  }

  // 產品 API
  async getProducts(): Promise<OperationResult<Product[]>> {
    return this.request<Product[]>('GET', 'products')
  }

  async getProductById(id: number): Promise<OperationResult<Product>> {
    return this.request<Product>('GET', `products/${id}`)
  }

  // 分類 API
  async getCategories(): Promise<OperationResult<Category[]>> {
    return this.request<Category[]>('GET', 'categories')
  }

  async getCategoryById(id: number): Promise<OperationResult<Category>> {
    return this.request<Category>('GET', `categories/${id}`)
  }

  // 備註 API
  async getRemarks(): Promise<OperationResult<Remark[]>> {
    return this.request<Remark[]>('GET', 'remarks')
  }

  async getRemarkById(id: number): Promise<OperationResult<Remark>> {
    return this.request<Remark>('GET', `remarks/${id}`)
  }

  // 批量操作
  async batchDeleteInspects(ids: number[]): Promise<OperationResult<void>> {
    const results = await Promise.allSettled(
      ids.map(id => this.deleteInspect(id))
    )

    const failures = results.filter(result => 
      result.status === 'rejected' || 
      (result.status === 'fulfilled' && !result.value.success)
    )

    if (failures.length === 0) {
      return {
        success: true,
        data: undefined,
        message: `成功刪除 ${ids.length} 筆記錄`
      }
    } else {
      return {
        success: false,
        error: `刪除失敗，${failures.length} 筆記錄無法刪除`,
        details: failures
      }
    }
  }

  async batchDeleteInspectlines(ids: number[]): Promise<OperationResult<void>> {
    const results = await Promise.allSettled(
      ids.map(id => this.deleteInspectline(id))
    )

    const failures = results.filter(result => 
      result.status === 'rejected' || 
      (result.status === 'fulfilled' && !result.value.success)
    )

    if (failures.length === 0) {
      return {
        success: true,
        data: undefined,
        message: `成功刪除 ${ids.length} 筆明細記錄`
      }
    } else {
      return {
        success: false,
        error: `刪除失敗，${failures.length} 筆明細記錄無法刪除`,
        details: failures
      }
    }
  }

  // 統計 API
  async getInspectStats(filters?: InspectSearchFilters): Promise<OperationResult<{
    total: number
    byType: Record<string, number>
    byGroup: Record<string, number>
    byShift: Record<string, number>
    totalQuantity: number
  }>> {
    return this.request('GET', 'inspects/stats', filters)
  }

  // 導出 API
  async exportInspects(filters?: InspectSearchFilters, format: 'csv' | 'excel' = 'excel'): Promise<OperationResult<Blob>> {
    try {
      const endpoint = `inspects/export?format=${format}`
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(filters || {})
      })

      if (!response.ok) {
        throw new Error(`導出失敗: ${response.statusText}`)
      }

      const blob = await response.blob()
      return {
        success: true,
        data: blob,
        message: '導出成功'
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || '導出失敗',
        details: error
      }
    }
  }
}

// 創建全局 API 客戶端實例
export const apiClient = new TypedApiClient()

// 便捷函數
export const inspectApi = {
  list: (filters?: InspectSearchFilters) => apiClient.getInspects(filters),
  get: (id: number) => apiClient.getInspectById(id),
  create: (data: InspectFormData) => apiClient.createInspect(data),
  update: (id: number, data: InspectFormData) => apiClient.updateInspect(id, data),
  delete: (id: number) => apiClient.deleteInspect(id),
  batchDelete: (ids: number[]) => apiClient.batchDeleteInspects(ids),
  stats: (filters?: InspectSearchFilters) => apiClient.getInspectStats(filters),
  export: (filters?: InspectSearchFilters, format?: 'csv' | 'excel') => 
    apiClient.exportInspects(filters, format)
}

export const inspectlineApi = {
  list: (inspectId: number) => apiClient.getInspectlines(inspectId),
  get: (id: number) => apiClient.getInspectlineById(id),
  create: (data: InspectlineFormData) => apiClient.createInspectline(data),
  update: (id: number, data: InspectlineFormData) => apiClient.updateInspectline(id, data),
  delete: (id: number) => apiClient.deleteInspectline(id),
  batchDelete: (ids: number[]) => apiClient.batchDeleteInspectlines(ids)
}

export const masterDataApi = {
  employees: () => apiClient.getEmployees(),
  products: () => apiClient.getProducts(),
  categories: () => apiClient.getCategories(),
  remarks: () => apiClient.getRemarks()
}
