import { getData } from "@/utils/backend-api";
import { Entity, Tracksheet } from "@/types";
import { getDefaultPagination, getPagination } from "@/utils/store-util";
import { appModule } from "./app";
import { get } from "lodash";
import {
  VuexModule,
  Module,
  Mutation,
  Action,
  getModule
} from "vuex-module-decorators";
import store from "@/store";

export interface TracksheetState {
  items: Entity[];
  pagination: Pagination;
  loading: boolean;
  tracksheet: Tracksheet;
}

@Module({ store, dynamic: true, name: "tracksheets" })
class TracksheetModule extends VuexModule implements TracksheetState {
  public items: Entity[] = [];
  public pagination = getDefaultPagination();
  public loading = false;
  public tracksheet = {} as Tracksheet;

  @Action
  async getTracksheetByCode(tracks: string[]) {
    this.setLoading(true);
    const type= tracks[0].toString();
    const id= tracks[1].toString();
    const trackId = tracks[2].toString();
    try {
      if (id) {
        const res = await getData(`tracksheets/${type}/${id}`);
        const tracksheet = res.data;
        if (type==="CAKE"){
          tracksheet[0].tracksheetNO = trackId;
        }
        this.setDataTable(tracksheet);
        this.setTracksheet(tracksheet);
      } else {
        this.setTracksheet({} as Tracksheet);
      }
    } catch (err) {
      console.log(err);
      appModule.sendErrorNotice("Operation failed! Please try again later.");
      appModule.closeNoticeWithDelay(5000);
    } finally {
      this.setLoading(false);
    }
  }

  @Action
  clearTracksheets() {
    this.setLoading(true);
      const tracksheets = [];
      const tracksheet = {} as Tracksheet;
      this.setDataTable(tracksheets);
      this.setTracksheet(tracksheet);
      this.setLoading(false);
  }

  @Action
  searchTracksheets(searchQuery: string) {
    getData("tracksheets" + searchQuery).then(res => {
      const tracksheets = res.data;
      this.setDataTable(tracksheets);
      this.setLoading(false);

    });
  }

  @Action
  quickSearch(headers: TableHeader[], qsFilter: SeachQuery): void {
    getData("tracksheets").then(res => {
      const tracksheets = res.data.filter((r: TODO) =>
        headers.some((header: TODO) => {
          const val = get(r, [header.value]);
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter)) ||
            false
          );
        })
      );
      this.setDataTable(tracksheets);
      this.setLoading(false);
    });
  }

  @Action
  setDataTable(items: Tracksheet[]) {
    const pagination = getPagination(items);
    this.setPagination(pagination);
    this.setItems(items);
  }

  @Mutation
  setItems(tracksheets: Tracksheet[]) {
    this.items = tracksheets;
  }

  @Mutation
  setPagination(pagination: Pagination) {
    this.pagination = pagination;
  }

  @Mutation
  setLoading(loading: boolean) {
    this.loading = loading;
  }

  @Mutation
  setTracksheet(tracksheet: Tracksheet) {
    this.tracksheet = tracksheet;
  }
}

export const tracksheetModule = getModule(TracksheetModule);
