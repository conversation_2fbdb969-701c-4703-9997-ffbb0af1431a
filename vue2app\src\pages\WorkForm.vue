<template>
  <v-container fluid>
    <v-flex xs12>
      <v-card class="grey lighten-4 elevation-0">
        <v-form ref="validForm" v-model="formValid" lazy-validation>
          <v-card-title class="title">
            {{ title }}
            <v-spacer></v-spacer>
            <v-btn
              elevation="4"
              fab
              small
              dark
              class="grey mr-2"
              @click.native="cancel()"
            >
              <v-icon dark="">mdi-close-circle-outline</v-icon>
            </v-btn>
            <v-btn
              elevation="4"
              fab
              small
              dark
              class="purple mr-2"
              :disabled="!formValid"
              @click.native="save()"
            >
              <v-icon>mdi-content-save-all</v-icon>
            </v-btn>
            <v-btn
              elevation="4"
              fab
              small
              dark
              class="blue"
              :disabled="!formValid"
              @click.native="addTrack()"
            >
              <v-icon>mdi-plus</v-icon>
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-container fluid grid-list-md>
              <v-layout row wrap>
                <v-flex md4 xs12>
                  <v-text-field
                    name="id"
                    label="單號"
                    type="number"
                    hint="WorkID is required"
                    value="Input text"
                    v-model="work.id"
                    class="input-group--focused"
                    readonly
                  ></v-text-field>
                </v-flex>
                <v-flex md4 xs12>
                  <v-menu
                    :close-on-content-click="false"
                    v-model="workDateMenu"
                    transition="v-scale-transition"
                    offset-y
                    :nudge-left="40"
                    max-width="290px"
                  >
                    <template v-slot:activator="{ on }">
                      <v-text-field
                        v-on="on"
                        label="日期"
                        v-model="work.workDate"
                        prepend-icon="mdi-calendar"
                        readonly
                      ></v-text-field>
                    </template>
                    <v-date-picker v-model="work.workDate" no-title scrollable>
                    </v-date-picker>
                  </v-menu>
                </v-flex>
                <v-flex md4 xs12>
                  <v-radio-group
                    name="shiftName"
                    label="勤別"
                    v-model="work.shiftName"
                    :rules="[value => !!value || '必要!!請選擇']"
                    required
                    row
                  >
                    <v-radio label="I" value="1"></v-radio>
                    <v-radio label="II" value="2"></v-radio>
                    <v-radio label="III" value="3"></v-radio>
                  </v-radio-group>
                </v-flex>
                <v-flex md4 xs12>
                  <v-radio-group
                    name="groupName"
                    label="組別"
                    v-model="work.groupName"
                    :rules="[value => !!value || '必要!!請選擇']"
                    required
                    row
                  >
                    <v-radio label="A" value="A"></v-radio>
                    <v-radio label="B" value="B"></v-radio>
                    <v-radio label="C" value="C"></v-radio>
                    <v-radio label="D" value="D"></v-radio>
                  </v-radio-group>
                </v-flex>
                <v-flex md4 xs12>
                  <v-text-field
                    name="quantity"
                    label="個數"
                    type="number"
                    v-model="work.quantity"
                    class="input-group--focused"
                    readonly
                  ></v-text-field>
                </v-flex>

                <v-flex xs12>
                  <v-card>
                    <Table
                      v-if="loading === false"
                      :headers="headers"
                      :items="work.worklines"
                      :pagination="pagination"
                      :setSearch="true"
                      :setEdit="false"
                      :setRemove="true"
                      :disableSort="false"
                      @remove="remove"
                    ></Table>
                  </v-card>
                </v-flex>
              </v-layout>
            </v-container>
          </v-card-text>
        </v-form>
      </v-card>
    </v-flex>

    <v-layout row justify-center>
      <v-dialog v-model="addTrackModal" width="700" persistent>
        <v-form ref="validDetail" v-model="detailValid" lazy-validation>
          <v-card>
            <v-card-title
              >捻線開機明細(新增)
              <v-spacer></v-spacer>
              <v-card-actions>
                <v-btn
                  class="green lighten-1"
                  text="text"
                  :disabled="!detailValid"
                  @click.native="saveWorkline"
                  >Confirm</v-btn
                >
                <v-btn
                  class="orange lighten-1"
                  text="text"
                  @click.native="cancelAddTrack"
                  >Cancel</v-btn
                >
              </v-card-actions>
            </v-card-title>
            <v-card-text>
              <v-text-field
                v-model="searchFilter.contain.twisterNO"
                append-icon="mdi-magnify"
                label="輸入捻線機號"
                @change="getTrack"
                counter="3"
                :rules="[value => !!value || '必要!!請選擇']"
                required
              ></v-text-field>
              <v-container fluid grid-list-md>
               <v-layout row wrap>
                 <v-flex md6 xs12>
                    <v-menu
                      :close-on-content-click="false"
                      v-model="worklineDateMenu"
                      transition="v-scale-transition"
                      offset-y
                      :nudge-left="40"
                      max-width="290px"
                    >
                      <template v-slot:activator="{ on }">
                        <v-text-field
                          v-bind:items="workline"
                          v-on="on"
                          label="開機日期"
                          v-model="workline.worklineDate"
                          :rules="[value => !!value || '必要!!請選擇']"
                          required
                          prepend-icon="mdi-calendar"
                        ></v-text-field>
                      </template>
                      <v-date-picker
                        v-model="workline.worklineDate"
                        no-title
                        scrollable
                      >
                      </v-date-picker>
                    </v-menu>
                  </v-flex>
                  <v-flex md6 xs12>
                    <v-menu
                      :close-on-content-click="false"
                      v-model="worklineTimeMenu"
                      transition="v-scale-transition"
                      offset-y
                      :nudge-left="40"
                      max-width="290px"
                    >
                      <template v-slot:activator="{ on }">
                        <v-text-field
                          v-bind:items="workline"
                          v-on="on"
                          label="開機時間"
                          v-model="workline.worklineTime"
                          :rules="[value => !!value || '必要!!請選擇']"
                          required
                          prepend-icon="mdi-clock"
                        ></v-text-field>
                      </template>
                      <v-time-picker
                        v-model="workline.worklineTime"
                        scrollable
                      >
                      </v-time-picker>
                    </v-menu>
                  </v-flex>
                  <v-flex md6 xs12>
                    <v-autocomplete
                      v-bind:items="employees"
                      label="人員"
                      item-text="employName"
                      item-value="employId"
                      v-model="workline.employId"
                      :rules="[value => !!value || '必要!!請選擇']"
                      required
                    ></v-autocomplete>
                  </v-flex>
                  <v-flex md6 xs12 v-for="(item, index) in track" :key="index">
                    <v-text-field
                      v-model="item.furnaceName"
                      label="爐別"
                      readonly
                    ></v-text-field>
                    <v-text-field
                      v-model="item.productName"
                      label="品種"
                      readonly
                    ></v-text-field>
                    <v-text-field
                      v-model="item.trackTime"
                      label="掃碼日期時間"
                      readonly
                    ></v-text-field>
                    <v-text-field
                      label="掃瞄T1個數"
                      value="item.trackT1Qty"
                      v-model="item.trackT1Qty"
                      readonly
                    ></v-text-field>
                    <v-text-field
                      label="掃瞄T2個數"
                      value="item.trackT2Qty"
                      v-model="item.trackT2Qty"
                      readonly
                    ></v-text-field>
                    <v-text-field
                      label="開機T1個數"
                      value="item.workT1Qty"
                      v-model="item.workT1Qty"
                    ></v-text-field>
                    <v-text-field
                      label="開機T2個數"
                      value="item.workT2Qty"
                      v-model="item.workT2Qty"
                    ></v-text-field>
                  </v-flex>
                </v-layout>
              </v-container>
            </v-card-text>
          </v-card>
        </v-form>
      </v-dialog>
    </v-layout>
    <confirm-dialog
      :dialog="dialog"
      :dialogTitle="dialogTitle"
      :dialogText="dialogText"
      @onConfirm="onConfirm"
      @onCancel="onCancel"
    ></confirm-dialog>
    <v-snackbar
      v-if="loading === false"
      :right="true"
      :timeout="2000"
      :color="mode"
      v-model="snackbar"
    >
      {{ notice }}
      <v-btn dark text @click.native="closeSnackbar">Close</v-btn>
    </v-snackbar>
  </v-container>
</template>
<script lang="ts">
import Vue from "vue";
import Table from "@/components/Table.vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
import { Component } from "vue-property-decorator";
import { trackModule } from "@/store/modules/tracks";
import { workModule } from "@/store/modules/works";
import { appModule } from "@/store/modules/app";
import { buildSearchFilters, buildJsonServerQuery, getISOClassDate } from "@/utils/app-util";

@Component({
  components: {
    Table,
    ConfirmDialog
  }
})
export default class WorkForm extends Vue {
  private modalTitle = "Add Track";
  private modalText = "Select the category and track from the list";
  private addTrackModal = false;
  private dialog = false;
  private dialogTitle = "Track Delete Dialog";
  private dialogText = "Do you want to delete this track?";
  private workDateMenu = false;
  private worklineDateMenu = false;
  private worklineTimeMenu = false;
  private errors = [];
  private formValid = false;
  private detailValid = false;
  private title = "";
  private workId = null;
  private worklineId = null;
  private categoryId = 0;
  private remarkId = 0;
  private color = "";
  private selectedWorkline: null;
  private query = "";
  private jsonQuery = "";

  search = "";
  headers = [
    { text: "開機日期時間", left: true, value: "worklineTime" },
    { text: "開機人員", left: true, value: "employName" },
    { text: "捻線機號", left: true, value: "twisterNO" },
    { text: "爐別", left: true, value: "furnaceName" },
    { text: "品種", left: true, value: "productName" },
    { text: "掃瞄T1個數", value: "trackT1Qty" },
    { text: "掃瞄T2個數", value: "trackT2Qty" },
    { text: "開機T1個數", value: "workT1Qty" },
    { text: "開機T2個數", value: "workT2Qty" },
    { text: "", value: "actions", sortable: false }
  ];

  searchFilter = { contain: { twisterNO: "" } };

  get employees() {
    //console.log(workModule.employees);
    return workModule.employees;
  }

  get work() {
    return workModule.work;
  }

  get workline() {
    //console.log("get workline is ", workModule.workline);
    return workModule.workline;
  }

  get track() {
    //console.log("get track is ", trackModule.track);
    return trackModule.track;
  }

  get loading() {
    return appModule.loading;
  }

  get mode() {
    return appModule.mode;
  }

  get snackbar() {
    return appModule.snackbar;
  }

  get notice() {
    return appModule.notice;
  }

  save() {
    //console.log("save work is ", this.work);
    workModule.saveWork(this.work);
  }

  getWorkById() {
    workModule.getWorkById(this.$route.params.id);
    //console.log(this.work);
  }

  getTrack() {
    buildSearchFilters(this.searchFilter);
    this.jsonQuery = buildJsonServerQuery(this.searchFilter);
    this.query = this.searchFilter.contain.twisterNO;
    if (this.query != "") {
      //console.log("this.query is ", this.query);
      trackModule.getTrackById(this.query);
      this.query = "";
      //console.log("trackModule.track is ", trackModule.track);
      //console.log("trackModule.items is ", trackModule.items);
      return trackModule.track;
    }
    return "";
  }

  cancel() {
    this.$router.push({ name: "works" });
  }

  remove(item) {
    //console.log("remove.item is", item);
    this.selectedWorkline = item;
    this.dialog = true;
  }

  onConfirm() {
    //console.log("onConfirm deleteWorklineis", this.selectedWorkline);
    workModule.deleteWorkline(this.selectedWorkline);
    this.selectedWorkline = null;
    this.getWorkById();
    this.dialog = false;
  }

  onCancel() {
    this.selectedWorkline = null;
    this.dialog = false;
  }

  addTrack() {
    this.addTrackModal = true;
    this.query = "";
    this.searchFilter.contain.twisterNO = "";
    this.workId = this.work.id;
    //console.log("addTrack workId is", this.work.id);
    trackModule.clearTracks();
    (this.$refs.validDetail as Vue & { validate: () => boolean }).validate();
  }

  saveWorkline() {
    //console.log("saveWorkline this.track[0] is ", this.track[0]);
    const WorkId = { workId: this.work.id };
    const addTrack = this.track[0];
    const addWorkline = this.workline;
    const newWorkline = { ...WorkId, ...addTrack, ...addWorkline};
    //console.log("saveWorkline workId is ", WorkId);
    //console.log("saveWorkline addTrack is ", addTrack);
    //console.log("saveWorkline addWorkline is ", addWorkline);
    //console.log("saveWorkline newWorkline is ", newWorkline);
    workModule.addWorklineToWork(newWorkline);
    this.worklineId = null;
    this.getWorkById();
    this.addTrackModal = false;
  }

  cancelAddTrack() {
    this.addTrackModal = false;
    this.query = "";
    this.searchFilter.contain.twisterNO = "";
    workModule.clearWorkline();
    trackModule.clearTracks();
  }

  closeSnackbar() {
    appModule.closeNotice();
  }

  get pagination() {
    return workModule.pagination;
  }

  created() {
    this.getWorkById();
    //console.log("WorkForm created is ", this.work);
    workModule.getEmployees();
  }

  mounted() {
    if (this.$route.params.id) {
      this.title = "捻線開機";
      this.$nextTick(() => {
        // this.shippedDate = this.work.shippedDate;
        // this.workDate = this.work.workDate;
      });
    } else {
      this.title = "捻線開機(新增)";
      const toDate = getISOClassDate();
      this.work.workDate = toDate.slice(0, 10);
      (this.$refs.validForm as Vue & { validate: () => boolean }).validate();
    }
  }
}
</script>
