import Vue from "vue";
import Vuex from "vuex";
// import createPersistedState from "vuex-persistedstate";

import { UserState } from "./modules/user";
import { InspectState } from "./modules/inspects";
import { WorkState } from "./modules/works";
import { DowngradeState } from "./modules/downgrades";
import { RelabelState } from "./modules/relabels";
import { QICountState } from "./modules/icounts";
import { PInventoryState } from "./modules/pinventories";
import { EmployeeState } from "./modules/employees";
import { ProductState } from "./modules/products";
import { TrackState } from "./modules/tracks";
import { TracksheetState } from "./modules/tracksheets";
import { AppState } from "./modules/app";

Vue.use(Vuex);

interface RootState {
  appState: AppState;
  userState: UserState;
  inspectState: InspectState;
  workState: WorkState;
  icountState: QICountState;
  pinventoryState: PInventoryState;
  employeeState: EmployeeState;
  downgradeState: DowngradeState;
  relabelState: RelabelState;
  productState: ProductState;
  trackState: TrackState;
  tracksheetState: TracksheetState;
}

export default new Vuex.Store<RootState>({
  // plugins: [createPersistedState({ storage: window.sessionStorage })] // !debug ? [createPersistedState({ storage: window.sessionStorage })] : [],
});
