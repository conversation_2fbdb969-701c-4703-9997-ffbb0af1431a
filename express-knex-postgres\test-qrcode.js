const db = require("./config/dbConfig.js");

// 測試 QRCode 查詢
async function testQRCode() {
  const qrCode = "20210903100003";
  
  console.log(`測試 QRCode: ${qrCode}`);
  console.log("=".repeat(50));
  
  try {
    // 1. 檢查 m_twqrcode 表中是否有此 QRCode
    console.log("1. 檢查 m_twqrcode 表...");
    const qrcodeRecords = await db("m_twqrcode")
      .select("*")
      .where("m_twqrcode_id", qrCode)
      .limit(5);
    
    console.log(`找到 ${qrcodeRecords.length} 筆 m_twqrcode 記錄:`);
    qrcodeRecords.forEach((record, index) => {
      console.log(`  ${index + 1}. ID: ${record.m_twqrcode_id}, 機台: ${record.mach_tmis_no}, 日期: ${record.class_date}`);
    });
    
    // 2. 檢查 m_twqrcodeline 表
    console.log("\n2. 檢查 m_twqrcodeline 表...");
    const qrcodelineRecords = await db("m_twqrcodeline")
      .select("*")
      .where("m_twqrcode_id", qrCode)
      .limit(5);
    
    console.log(`找到 ${qrcodelineRecords.length} 筆 m_twqrcodeline 記錄:`);
    qrcodelineRecords.forEach((record, index) => {
      console.log(`  ${index + 1}. QRCode ID: ${record.m_twqrcode_id}, Product ID: ${record.m_product_id}`);
    });
    
    // 3. 檢查完整的關聯查詢
    console.log("\n3. 執行完整關聯查詢...");
    const fullQuery = await db("m_twqrcode")
      .select({
        twisterNO: "m_twqrcode.mach_tmis_no",
        productName: "rv_product_class.name",
        trackTime: "m_twqrcode.created",
        gradeName: "rv_product_class.prod_class",
        categoryName: "rv_product_class.category_name",
        furnaceName: "m_twqrcode.furnace_name",
        bushingNO: "rv_product_class.bushing_no",
        cakeWeight: "rv_product_class.cake_weight"
      })
      .count({ trackT1Qty: 'm_twqrcode.mach_tmis_no' })
      .innerJoin('m_twqrcodeline', 'm_twqrcode.m_twqrcode_id', 'm_twqrcodeline.m_twqrcode_id')
      .innerJoin('rv_product_class', 'm_twqrcodeline.m_product_id', 'rv_product_class.m_product_id')
      .groupBy('m_twqrcode.m_twqrcode_id', 'm_twqrcode.mach_tmis_no', 'm_twqrcodeline.m_product_id', 'rv_product_class.name', 'rv_product_class.prod_class')
      .where("m_twqrcode.m_twqrcode_id", qrCode)
      .limit(5);
    
    console.log(`完整查詢結果 ${fullQuery.length} 筆:`);
    fullQuery.forEach((record, index) => {
      console.log(`  ${index + 1}. 產品: ${record.productName}, 等級: ${record.gradeName}, 機台: ${record.twisterNO}`);
    });
    
    // 4. 檢查是否有類似的 QRCode
    console.log("\n4. 檢查類似的 QRCode...");
    const similarQRCodes = await db("m_twqrcode")
      .select("m_twqrcode_id", "mach_tmis_no", "class_date")
      .where("m_twqrcode_id", "like", "202109%")
      .limit(10);
    
    console.log(`找到 ${similarQRCodes.length} 筆類似的 QRCode:`);
    similarQRCodes.forEach((record, index) => {
      console.log(`  ${index + 1}. ${record.m_twqrcode_id} - 機台: ${record.mach_tmis_no}, 日期: ${record.class_date}`);
    });
    
  } catch (error) {
    console.error("查詢錯誤:", error);
  } finally {
    process.exit(0);
  }
}

testQRCode();
