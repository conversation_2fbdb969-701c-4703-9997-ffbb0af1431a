import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { useInspectDetail } from '@/composables/useInspectDetail'
import { useInspectsStore } from '@/stores/inspects'

// Mock stores
vi.mock('@/stores/inspects')

describe('useInspectDetail', () => {
  let pinia: any
  let mockInspectsStore: any

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)

    mockInspectsStore = {
      currentInspectlines: [],
      deleteInspectline: vi.fn(),
      updateInspectline: vi.fn(),
      addInspectlineToInspect: vi.fn(),
      getInspectById: vi.fn()
    }

    vi.mocked(useInspectsStore).mockReturnValue(mockInspectsStore)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('初始化', () => {
    it('應該正確初始化明細數據', () => {
      const options = {
        inspectId: 123,
        onDetailSaved: vi.fn(),
        onDetailDeleted: vi.fn(),
        onError: vi.fn()
      }

      const {
        detailItems,
        detailDialog,
        detailFormValid,
        editingDetailId,
        detailData,
        detailHeaders
      } = useInspectDetail(options)

      expect(detailItems.value).toEqual([])
      expect(detailDialog.value).toBe(false)
      expect(detailFormValid.value).toBe(false)
      expect(editingDetailId.value).toBeNull()
      expect(detailData.value).toEqual({
        codeName: '',
        remarkName: '',
        quantity: 1
      })
      expect(detailHeaders).toBeDefined()
      expect(detailHeaders.length).toBeGreaterThan(0)
    })
  })

  describe('對話框操作', () => {
    it('應該正確打開新增對話框', () => {
      const options = { inspectId: 123 }
      const { openDetailDialog, detailDialog, editingDetailId, detailData } = useInspectDetail(options)

      openDetailDialog()

      expect(detailDialog.value).toBe(true)
      expect(editingDetailId.value).toBeNull()
      expect(detailData.value).toEqual({
        codeName: '',
        remarkName: '',
        quantity: 1
      })
    })

    it('應該正確打開編輯對話框', () => {
      const options = { inspectId: 123 }
      const mockItem = {
        id: 456,
        codeName: 'TEST001',
        remarkName: '測試異常',
        quantity: 5
      }

      const { editDetailItem, detailDialog, editingDetailId, detailData } = useInspectDetail(options)

      editDetailItem(mockItem)

      expect(detailDialog.value).toBe(true)
      expect(editingDetailId.value).toBe(456)
      expect(detailData.value).toEqual(mockItem)
    })

    it('應該正確關閉對話框', () => {
      const options = { inspectId: 123 }
      const { closeDetailDialog, detailDialog } = useInspectDetail(options)

      // 先打開對話框
      const { openDetailDialog } = useInspectDetail(options)
      openDetailDialog()

      closeDetailDialog()

      expect(detailDialog.value).toBe(false)
    })
  })

  describe('明細操作', () => {
    it('應該正確保存新增明細', async () => {
      const onDetailSaved = vi.fn()
      const options = {
        inspectId: 123,
        onDetailSaved
      }

      mockInspectsStore.addInspectlineToInspect.mockResolvedValue()
      mockInspectsStore.getInspectById.mockResolvedValue()

      const { detailData, saveDetailItem, editingDetailId } = useInspectDetail(options)

      detailData.value = {
        codeName: 'TEST001',
        remarkName: '測試異常',
        quantity: 3
      }
      editingDetailId.value = null

      await saveDetailItem()

      expect(mockInspectsStore.addInspectlineToInspect).toHaveBeenCalledWith({
        codeName: 'TEST001',
        remarkName: '測試異常',
        quantity: 3,
        inspectId: 123
      })
      expect(onDetailSaved).toHaveBeenCalled()
    })

    it('應該正確保存編輯明細', async () => {
      const onDetailSaved = vi.fn()
      const options = {
        inspectId: 123,
        onDetailSaved
      }

      mockInspectsStore.updateInspectline.mockResolvedValue()
      mockInspectsStore.getInspectById.mockResolvedValue()

      const { detailData, saveDetailItem, editingDetailId } = useInspectDetail(options)

      detailData.value = {
        codeName: 'TEST002',
        remarkName: '更新異常',
        quantity: 5
      }
      editingDetailId.value = 456

      await saveDetailItem()

      expect(mockInspectsStore.updateInspectline).toHaveBeenCalledWith({
        codeName: 'TEST002',
        remarkName: '更新異常',
        quantity: 5,
        id: 456,
        inspectId: 123
      })
      expect(onDetailSaved).toHaveBeenCalled()
    })

    it('應該在沒有 inspectId 時顯示警告', async () => {
      const options = { inspectId: null }
      const { saveDetailItem, showMessage } = useInspectDetail(options)

      await saveDetailItem()

      // 這裡我們無法直接測試 showMessage 的調用，但可以測試沒有調用 store 方法
      expect(mockInspectsStore.addInspectlineToInspect).not.toHaveBeenCalled()
      expect(mockInspectsStore.updateInspectline).not.toHaveBeenCalled()
    })

    it('應該正確刪除明細', async () => {
      const onDetailDeleted = vi.fn()
      const options = {
        inspectId: 123,
        onDetailDeleted
      }

      const mockItem = {
        id: 456,
        codeName: 'TEST001',
        remarkName: '測試異常',
        quantity: 3
      }

      // Mock window.confirm
      global.confirm = vi.fn().mockReturnValue(true)
      mockInspectsStore.deleteInspectline.mockResolvedValue()
      mockInspectsStore.getInspectById.mockResolvedValue()

      const { deleteDetailItem } = useInspectDetail(options)

      await deleteDetailItem(mockItem)

      expect(global.confirm).toHaveBeenCalledWith('確定要刪除此明細記錄嗎？')
      expect(mockInspectsStore.deleteInspectline).toHaveBeenCalledWith(mockItem)
      expect(onDetailDeleted).toHaveBeenCalledWith(mockItem)
    })

    it('應該在用戶取消時不刪除明細', async () => {
      const options = { inspectId: 123 }
      const mockItem = { id: 456 }

      // Mock window.confirm 返回 false
      global.confirm = vi.fn().mockReturnValue(false)

      const { deleteDetailItem } = useInspectDetail(options)

      await deleteDetailItem(mockItem)

      expect(mockInspectsStore.deleteInspectline).not.toHaveBeenCalled()
    })
  })

  describe('數據載入', () => {
    it('應該正確載入明細數據', async () => {
      const mockInspectlines = [
        { id: 1, codeName: 'TEST001', remarkName: '異常1', quantity: 2 },
        { id: 2, codeName: 'TEST002', remarkName: '異常2', quantity: 3 }
      ]

      mockInspectsStore.currentInspectlines = mockInspectlines
      mockInspectsStore.getInspectById.mockResolvedValue()

      const options = { inspectId: 123 }
      const { loadDetailData, detailItems } = useInspectDetail(options)

      await loadDetailData()

      expect(mockInspectsStore.getInspectById).toHaveBeenCalledWith('123')
      expect(detailItems.value).toEqual(mockInspectlines)
    })

    it('應該在沒有 inspectId 時不載入數據', async () => {
      const options = { inspectId: null }
      const { loadDetailData } = useInspectDetail(options)

      await loadDetailData()

      expect(mockInspectsStore.getInspectById).not.toHaveBeenCalled()
    })
  })

  describe('批量操作', () => {
    it('應該正確執行批量刪除', async () => {
      const selectedItems = [
        { id: 1, codeName: 'TEST001' },
        { id: 2, codeName: 'TEST002' }
      ]

      global.confirm = vi.fn().mockReturnValue(true)
      mockInspectsStore.deleteInspectline.mockResolvedValue()
      mockInspectsStore.getInspectById.mockResolvedValue()

      const options = { inspectId: 123 }
      const { deleteSelectedItems } = useInspectDetail(options)

      await deleteSelectedItems(selectedItems)

      expect(mockInspectsStore.deleteInspectline).toHaveBeenCalledTimes(2)
      expect(mockInspectsStore.deleteInspectline).toHaveBeenCalledWith(selectedItems[0])
      expect(mockInspectsStore.deleteInspectline).toHaveBeenCalledWith(selectedItems[1])
    })

    it('應該在沒有選擇項目時顯示警告', async () => {
      const options = { inspectId: 123 }
      const { deleteSelectedItems } = useInspectDetail(options)

      await deleteSelectedItems([])

      expect(mockInspectsStore.deleteInspectline).not.toHaveBeenCalled()
    })
  })

  describe('驗證', () => {
    it('應該正確驗證明細數據', () => {
      const options = { inspectId: 123 }
      const { validateDetailData, detailData } = useInspectDetail(options)

      // 測試有效數據
      detailData.value = {
        codeName: 'TEST001',
        remarkName: '測試異常',
        quantity: 5
      }

      expect(validateDetailData()).toBe(true)

      // 測試無效數據 - 缺少 codeName
      detailData.value.codeName = ''
      expect(validateDetailData()).toBe(false)

      // 測試無效數據 - 缺少 remarkName
      detailData.value.codeName = 'TEST001'
      detailData.value.remarkName = ''
      expect(validateDetailData()).toBe(false)

      // 測試無效數據 - 無效數量
      detailData.value.remarkName = '測試異常'
      detailData.value.quantity = 0
      expect(validateDetailData()).toBe(false)
    })
  })

  describe('錯誤處理', () => {
    it('應該處理保存錯誤', async () => {
      const onError = vi.fn()
      const options = {
        inspectId: 123,
        onError
      }

      const mockError = new Error('保存失敗')
      mockInspectsStore.addInspectlineToInspect.mockRejectedValue(mockError)

      const { saveDetailItem, detailData } = useInspectDetail(options)

      detailData.value = {
        codeName: 'TEST001',
        remarkName: '測試異常',
        quantity: 3
      }

      await saveDetailItem()

      expect(onError).toHaveBeenCalledWith(mockError, 'save')
    })

    it('應該處理刪除錯誤', async () => {
      const onError = vi.fn()
      const options = {
        inspectId: 123,
        onError
      }

      const mockError = new Error('刪除失敗')
      global.confirm = vi.fn().mockReturnValue(true)
      mockInspectsStore.deleteInspectline.mockRejectedValue(mockError)

      const { deleteDetailItem } = useInspectDetail(options)

      await deleteDetailItem({ id: 456 })

      expect(onError).toHaveBeenCalledWith(mockError, 'delete')
    })
  })

  describe('計算屬性', () => {
    it('應該正確計算 hasDetails', () => {
      const options = { inspectId: 123 }
      const { hasDetails, detailItems } = useInspectDetail(options)

      expect(hasDetails.value).toBe(false)

      detailItems.value = [{ id: 1, codeName: 'TEST001' }]
      expect(hasDetails.value).toBe(true)
    })

    it('應該正確計算 isEditing', () => {
      const options = { inspectId: 123 }
      const { isEditing, editingDetailId } = useInspectDetail(options)

      expect(isEditing.value).toBe(false)

      editingDetailId.value = 456
      expect(isEditing.value).toBe(true)
    })
  })
})
