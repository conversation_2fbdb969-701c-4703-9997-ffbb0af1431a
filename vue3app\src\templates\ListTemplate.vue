<template>
  <v-container fluid>
    <v-card>
      <v-card-title>
        <span class="title">{{ title }}</span>
        <v-spacer></v-spacer>
        <v-btn
          elevation="4"
          color="green"
          size="small"
          icon
          class="mr-2"
          @click="add"
        >
          <v-icon>mdi-plus</v-icon>
        </v-btn>
        <v-btn
          elevation="4"
          color="brown-lighten-1"
          size="small"
          icon
          class="mr-2"
          @click="reloadData"
        >
          <v-icon>mdi-refresh</v-icon>
        </v-btn>
      </v-card-title>
      
      <!-- Loading indicator -->
      <v-card-text v-if="loading">
        <v-progress-linear indeterminate></v-progress-linear>
        <p class="text-center mt-2">載入中...</p>
      </v-card-text>
      
      <!-- Results table -->
      <v-card-text v-if="!loading && items.length > 0">
        <v-data-table
          :headers="headers"
          :items="items"
          :items-per-page="10"
          class="elevation-1"
        >
          <template v-slot:item.actions="{ item }">
            <v-btn
              size="small"
              color="primary"
              variant="text"
              @click="edit(item)"
            >
              編輯
            </v-btn>
            <v-btn
              size="small"
              color="error"
              variant="text"
              @click="remove(item)"
            >
              刪除
            </v-btn>
          </template>
        </v-data-table>
      </v-card-text>
      
      <!-- No results message -->
      <v-card-text v-if="!loading && items.length === 0">
        <v-alert type="info" variant="tonal">
          沒有找到相關資料
        </v-alert>
      </v-card-text>
    </v-card>

    <!-- Delete confirmation dialog -->
    <v-dialog v-model="dialog" max-width="400">
      <v-card>
        <v-card-title>{{ dialogTitle }}</v-card-title>
        <v-card-text>刪除該筆記錄?</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey" variant="text" @click="onCancel">取消</v-btn>
          <v-btn color="error" variant="text" @click="onConfirm">確認</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Snackbar for notifications -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
      location="top end"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn variant="text" @click="snackbar.show = false">Close</v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getData, deleteData } from '@/utils/backend-api'

interface Props {
  title: string
  apiEndpoint: string
  editRoute: string
  newRoute: string
  dialogTitle: string
  headers: Array<{
    title: string
    key: string
    align?: string
    sortable?: boolean
  }>
}

const props = defineProps<Props>()

interface ListItem {
  id: number
  [key: string]: any
}

// Reactive state
const router = useRouter()
const loading = ref(false)
const items = ref<ListItem[]>([])
const dialog = ref(false)
const itemId = ref(-1)

const snackbar = ref({
  show: false,
  message: "",
  color: "error"
})

// Methods
const showError = (message: string) => {
  snackbar.value = {
    show: true,
    message,
    color: "error"
  }
}

const showSuccess = (message: string) => {
  snackbar.value = {
    show: true,
    message,
    color: "success"
  }
}

const edit = (item: ListItem) => {
  router.push(`${props.editRoute}/${item.id}`)
}

const add = () => {
  router.push(props.newRoute)
}

const remove = (item: ListItem) => {
  itemId.value = item.id
  dialog.value = true
}

const onConfirm = async () => {
  try {
    await deleteData(`${props.apiEndpoint}/${itemId.value}`)
    dialog.value = false
    showSuccess("刪除成功")
    await loadData()
  } catch (error) {
    console.error('Delete error:', error)
    showError("刪除失敗，請稍後再試")
  }
}

const onCancel = () => {
  itemId.value = -1
  dialog.value = false
}

const loadData = async () => {
  loading.value = true
  try {
    const response = await getData(props.apiEndpoint)
    if (response.data && Array.isArray(response.data)) {
      items.value = response.data
    } else {
      items.value = []
    }
  } catch (error) {
    console.error('API Error:', error)
    showError("載入資料失敗，請稍後再試")
    items.value = []
  } finally {
    loading.value = false
  }
}

const reloadData = () => {
  loadData()
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>
