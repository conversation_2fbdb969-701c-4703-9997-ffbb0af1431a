<template>
  <div>
    <v-btn
      v-if="updateSearchPanel"
      elevation="4"
      color="blue-grey"
      size="small"
      icon
      class="mr-2"
      @click="updateSearchPanel"
    >
      <v-icon>mdi-magnify</v-icon>
    </v-btn>
    <v-btn
      v-if="reloadData"
      elevation="4"
      color="brown-lighten-1"
      size="small"
      icon
      class="mr-2"
      @click="reloadData"
    >
      <v-icon>mdi-refresh</v-icon>
    </v-btn>
    <!---
      <v-btn elevation="4" class="teal darken-2  mr-2" fab small dark @click="print">
        <v-icon>mdi-printer</v-icon>
      </v-btn>
    -->
    <v-btn
      v-if="add"
      elevation="4"
      color="deep-orange-darken-3"
      size="small"
      icon
      @click="add"
    >
      <v-icon>mdi-plus</v-icon>
    </v-btn>
  </div>
</template>

<script setup lang="ts">
interface Props {
  reloadData?: () => void
  updateSearchPanel?: () => void
  add?: () => void
}

defineProps<Props>()

const print = (): void => {
  window.print()
}
</script>
