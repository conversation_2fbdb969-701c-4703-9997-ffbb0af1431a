import { createRouter, createWebHistory, RouteRecordRaw, RouteLocationNormalized } from 'vue-router'
import { useUserStore } from '../stores/user'

// Lazy load components
const Dashboard = () => import('../pages/Dashboard.vue')
const Login = () => import('../pages/Login.vue')
const ErrorPage = () => import('../components/404.vue')
const TestPage = () => import('../pages/TestPage.vue')
const InspectList = () => import('../pages/InspectList.vue')
const InspectForm = () => import('../pages/InspectForm.vue')
const ICountList = () => import('../pages/ICountList.vue')
const ICountForm = () => import('../pages/ICountForm.vue')
const PInventoryOfCakeList = () => import('../pages/PInventoryOfCakeList.vue')
const PInventoryOfCakeForm = () => import('../pages/PInventoryOfCakeForm.vue')
const PInventoryOfYarnList = () => import('../pages/PInventoryOfYarnList.vue')
const PInventoryOfYarnForm = () => import('../pages/PInventoryOfYarnForm.vue')
const PInventoryOfPackList = () => import('../pages/PInventoryOfPackList.vue')
const PInventoryOfPackForm = () => import('../pages/PInventoryOfPackForm.vue')
const RelabelList = () => import('../pages/RelabelList.vue')
const RelabelForm = () => import('../pages/RelabelForm.vue')
const TrackList = () => import('../pages/TrackList.vue')
const TrackForm = () => import('../pages/TrackForm.vue')
const TracksheetList = () => import('../pages/TracksheetList.vue')
const ProductList = () => import('../pages/ProductList.vue')
const WorkList = () => import('../pages/WorkList.vue')
const WorkForm = () => import('../pages/WorkForm.vue')
const DowngradeList = () => import('../pages/DowngradeList.vue')
const DowngradeForm = () => import('../pages/DowngradeForm.vue')
const DisposalList = () => import('../pages/DisposalList.vue')
const DisposalForm = () => import('../pages/DisposalForm.vue')

// Vue3 測試組件
const InspectFormVue3Simple = () => import('../pages/InspectFormVue3Simple.vue')
const DowngradeFormVue3 = () => import('../pages/DowngradeFormVue3.vue')
const DowngradeFormVue3Simple = () => import('../pages/DowngradeFormVue3Simple.vue')
const DowngradeTestPlaceholder = () => import('../pages/DowngradeTestPlaceholder.vue')
const Vue3FormTestCenter = () => import('../pages/Vue3FormTestCenter.vue')
const PInventoryOfYarnFormVue3Simple = () => import('../pages/PInventoryOfYarnFormVue3Simple.vue')
const DisposalFormVue3Simple = () => import('../pages/DisposalFormVue3Simple.vue')
const RelabelFormVue3Simple = () => import('../pages/RelabelFormVue3Simple.vue')
const ICountFormVue3Simple = () => import('../pages/ICountFormVue3Simple.vue')
const PInventoryOfCakeFormVue3Simple = () => import('../pages/PInventoryOfCakeFormVue3Simple.vue')
const PInventoryOfPackFormVue3Simple = () => import('../pages/PInventoryOfPackFormVue3Simple.vue')
const EMProductionQueryVue3Simple = () => import('../pages/EMProductionQueryVue3Simple.vue')
const EMProductionQueryAndroidLayout = () => import('../pages/EMProductionQueryAndroidLayout.vue')
const EMProductionQueryWithConfig = () => import('../pages/EMProductionQueryWithConfig.vue')
const NavigationDemo = () => import('../pages/NavigationDemo.vue')
const EditFunctionTest = () => import('../tests/EditFunctionTest.vue')
const TestPlaceholder = () => import('../pages/TestPlaceholder.vue')
const Vue3TestPage = () => import('../pages/Vue3TestPage.vue')
const Vue3TestPageSimple = () => import('../pages/Vue3TestPageSimple.vue')
const AndroidLayoutTest = () => import('../pages/AndroidLayoutTest.vue')
const SimpleVue3Test = () => import('../pages/SimpleVue3Test.vue')

const authGuard = async (to: RouteLocationNormalized) => {
  const userStore = useUserStore()
  if (!userStore.isLoggedIn) {
    return {
      path: '/login',
      query: { redirect: to.fullPath }
    }
  }
}

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/test',
    name: 'test',
    component: TestPage,
    beforeEnter: authGuard
  },
  {
    path: '/login',
    name: 'login',
    component: Login
  },
  {
    path: '/inspects',
    name: 'inspects',
    component: InspectList,
    beforeEnter: authGuard
  },
  {
    path: '/inspect/:id',
    name: 'inspect-edit',
    component: InspectForm,
    beforeEnter: authGuard
  },
  {
    path: '/inspects/new',
    name: 'inspect-new',
    component: InspectForm,
    beforeEnter: authGuard
  },
  {
    path: '/newinspect',
    name: 'newinspect',
    component: InspectForm,
    beforeEnter: authGuard
  },
  {
    path: '/icounts',
    name: 'icounts',
    component: ICountList,
    beforeEnter: authGuard
  },
  {
    path: '/icount/:id',
    name: 'icount-edit',
    component: ICountForm,
    beforeEnter: authGuard
  },
  {
    path: '/icounts/new',
    name: 'icount-new',
    component: ICountForm,
    beforeEnter: authGuard
  },
  {
    path: '/newicount',
    name: 'newicount',
    component: ICountForm,
    beforeEnter: authGuard
  },
  {
    path: '/pinventoriesofcake',
    name: 'pinventoriesofcake',
    component: PInventoryOfCakeList,
    beforeEnter: authGuard
  },
  {
    path: '/pinventoryofcake/:id',
    name: 'pinventoryofcake-edit',
    component: PInventoryOfCakeForm,
    beforeEnter: authGuard
  },
  {
    path: '/pinventoriesofcake/new',
    name: 'pinventoryofcake-new',
    component: PInventoryOfCakeForm,
    beforeEnter: authGuard
  },
  {
    path: '/newpinventoryofcake',
    name: 'newpinventoryofcake',
    component: PInventoryOfCakeForm,
    beforeEnter: authGuard
  },
  {
    path: '/pinventoriesofyarn',
    name: 'pinventoriesofyarn',
    component: PInventoryOfYarnList,
    beforeEnter: authGuard
  },
  {
    path: '/pinventoryofyarn/:id',
    name: 'pinventoryofyarn-edit',
    component: PInventoryOfYarnForm,
    beforeEnter: authGuard
  },
  {
    path: '/pinventoriesofyarn/new',
    name: 'pinventoryofyarn-new',
    component: PInventoryOfYarnForm,
    beforeEnter: authGuard
  },
  {
    path: '/pinventoriesofpack',
    name: 'pinventoriesofpack',
    component: PInventoryOfPackList,
    beforeEnter: authGuard
  },
  {
    path: '/pinventoryofpack/:id',
    name: 'pinventoryofpack-edit',
    component: PInventoryOfPackForm,
    beforeEnter: authGuard
  },
  {
    path: '/pinventoriesofpack/new',
    name: 'pinventoryofpack-new',
    component: PInventoryOfPackForm,
    beforeEnter: authGuard
  },
  {
    path: '/relabels',
    name: 'relabels',
    component: RelabelList,
    beforeEnter: authGuard
  },
  {
    path: '/relabel/:id',
    name: 'relabel-edit',
    component: RelabelForm,
    beforeEnter: authGuard
  },
  {
    path: '/relabels/new',
    name: 'relabel-new',
    component: RelabelForm,
    beforeEnter: authGuard
  },
  {
    path: '/newrelabel',
    name: 'newrelabel',
    component: RelabelForm,
    beforeEnter: authGuard
  },
  {
    path: '/tracks',
    name: 'tracks',
    component: TrackList,
    beforeEnter: authGuard
  },
  {
    path: '/tracks/new',
    name: 'track-new',
    component: TrackForm,
    beforeEnter: authGuard
  },
  {
    path: '/tracksheets',
    name: 'tracksheets',
    component: TracksheetList,
    beforeEnter: authGuard
  },
  {
    path: '/products',
    name: 'products',
    component: ProductList,
    beforeEnter: authGuard
  },
  {
    path: '/em-production-query',
    name: 'em-production-query',
    component: () => import('../pages/EMProductionQuery.vue'),
    beforeEnter: authGuard
  },

  {
    path: '/works',
    name: 'works',
    component: WorkList,
    beforeEnter: authGuard
  },
  {
    path: '/work/:id',
    name: 'work-edit',
    component: WorkForm,
    beforeEnter: authGuard
  },
  {
    path: '/works/new',
    name: 'work-new',
    component: WorkForm,
    beforeEnter: authGuard
  },
  {
    path: '/downgrades',
    name: 'downgrades',
    component: DowngradeList,
    beforeEnter: authGuard
  },
  {
    path: '/downgrade/:id',
    name: 'downgrade-edit',
    component: DowngradeForm,
    beforeEnter: authGuard
  },
  {
    path: '/downgrades/new',
    name: 'downgrade-new',
    component: DowngradeForm,
    beforeEnter: authGuard
  },
  {
    path: '/disposals',
    name: 'disposals',
    component: DisposalList,
    beforeEnter: authGuard
  },
  {
    path: '/disposal/:id',
    name: 'disposal-edit',
    component: DisposalForm,
    beforeEnter: authGuard
  },
  {
    path: '/disposals/new',
    name: 'disposal-new',
    component: DisposalForm,
    beforeEnter: authGuard
  },
  {
    path: '/newdisposal',
    name: 'newdisposal',
    component: DisposalForm,
    beforeEnter: authGuard
  },
  // Vue3 測試路由
  {
    path: '/vue3-test',
    name: 'vue3-test',
    component: Vue3TestPageSimple,
    beforeEnter: authGuard
  },
  {
    path: '/vue3-test-full',
    name: 'vue3-test-full',
    component: Vue3TestPage,
    beforeEnter: authGuard
  },
  {
    path: '/simple-vue3-test',
    name: 'simple-vue3-test',
    component: SimpleVue3Test,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/inspect/:id',
    name: 'test-inspect-vue3-edit',
    component: InspectForm,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/inspects/new',
    name: 'test-inspect-vue3-new',
    component: InspectForm,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/functions',
    name: 'test-edit-functions',
    component: TestPlaceholder,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/downgrade/:id',
    name: 'test-downgrade-vue3-edit',
    component: DowngradeForm,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/downgrades/new',
    name: 'test-downgrade-vue3-new',
    component: DowngradeForm,
    beforeEnter: authGuard
  },
  {
    path: '/vue3-form-test-center',
    name: 'vue3-form-test-center',
    component: Vue3FormTestCenter,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/pinventory-yarn/:id',
    name: 'test-pinventory-yarn-vue3-edit',
    component: PInventoryOfYarnFormVue3Simple,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/pinventory-yarn/new',
    name: 'test-pinventory-yarn-vue3-new',
    component: PInventoryOfYarnFormVue3Simple,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/disposal/:id',
    name: 'test-disposal-vue3-edit',
    component: DisposalFormVue3Simple,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/disposals/new',
    name: 'test-disposal-vue3-new',
    component: DisposalFormVue3Simple,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/relabel/:id',
    name: 'test-relabel-vue3-edit',
    component: RelabelFormVue3Simple,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/relabels/new',
    name: 'test-relabel-vue3-new',
    component: RelabelFormVue3Simple,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/icount/:id',
    name: 'test-icount-vue3-edit',
    component: ICountForm,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/icounts/new',
    name: 'test-icount-vue3-new',
    component: ICountForm,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/pinventory-cake/:id',
    name: 'test-pinventory-cake-vue3-edit',
    component: PInventoryOfCakeFormVue3Simple,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/pinventory-cake/new',
    name: 'test-pinventory-cake-vue3-new',
    component: PInventoryOfCakeFormVue3Simple,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/pinventory-pack/:id',
    name: 'test-pinventory-pack-vue3-edit',
    component: PInventoryOfPackFormVue3Simple,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/pinventory-pack/new',
    name: 'test-pinventory-pack-vue3-new',
    component: PInventoryOfPackFormVue3Simple,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/em-production-query',
    name: 'test-em-production-query-vue3',
    component: EMProductionQueryVue3Simple,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/em-production-query-android',
    name: 'test-em-production-query-android',
    component: EMProductionQueryAndroidLayout,
    beforeEnter: authGuard
  },
  {
    path: '/test-vue3/em-production-query-config',
    name: 'test-em-production-query-config',
    component: EMProductionQueryWithConfig,
    beforeEnter: authGuard
  },
  {
    path: '/navigation-demo',
    name: 'navigation-demo',
    component: NavigationDemo,
    beforeEnter: authGuard
  },
  {
    path: '/android-layout-test',
    name: 'android-layout-test',
    component: AndroidLayoutTest,
    beforeEnter: authGuard
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'error',
    component: ErrorPage
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
