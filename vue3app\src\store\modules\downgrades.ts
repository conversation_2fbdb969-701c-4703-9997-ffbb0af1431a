import { Component, Vue } from 'vue-facing-decorator'
import { createApp } from 'vue'
import { getData, putData, postData, deleteData } from '@/utils/backend-api'
import { Employee, Downgrade, Entity, Product, Category, Downgradeline, Pagination, TableHeader } from '@/types'
import { getDefaultPagination, getPagination } from '@/utils/store-util'
import { appModule } from '@/stores/app'
import { get } from 'lodash'

export interface DowngradeState {
  items: Entity[]
  pagination: Pagination
  loading: boolean
  employee: string
  downgradeId: number | null
  downgrade: Downgrade
  downgradeline: Downgradeline[]
  product: Product
  employees: Employee[]
  categories: Category[]
}

@Component({
  name: 'DowngradeModule'
})
class DowngradeModule extends Vue implements DowngradeState {
  items: Entity[] = []
  pagination = getDefaultPagination()
  loading = false
  employee = ""
  downgradeId: number | null = null
  downgrade = {} as Downgrade
  downgradeline: Downgradeline[] = []
  product = {} as Product
  employees: Employee[] = []
  categories: Category[] = []

  getEmployees = () => {
    getData("employees/tw").then(res => {
      if (res.data) {
        const employees = res.data.map((c: Employee) => {
          c.employName = c.employNO + " " + c.userName
          c.value = c.id
          return c
        })
        this.setEmployees(employees)
      }
    })
  }

  getAllDowngradesByType = async (type: string) => {
    this.setLoading(true)
    try {
      const res = await getData(`downgrades/${type}`)
      const downgrades = res.data
      this.setDowngrade(downgrades)
      this.setDataTable(downgrades)
    } catch (error) {
      console.error(error)
    } finally {
      this.setLoading(false)
    }
  }

  getDowngradeById = (id: string) => {
    if (id) {
      getData("downgrades/" + id).then(
        res => {
          const _downgrade = res.data
          const downgrade = _downgrade[0]
          downgrade.downgradelines = downgrade.downgradelines?.filter(
            (p: Downgrade) => p !== null && p !== undefined
          ) || []
          downgrade.quantity = downgrade.downgradelines?.length || 0
          this.setDowngrade(downgrade)
          this.setDataTable(downgrade.downgradelines)
        },
        err => {
          console.log(err)
        }
      )
    } else {
      const downgrade = {} as Downgrade
      downgrade.downgradelines = []
      this.setDowngrade(downgrade)
      this.setLoading(false)
    }
  }

  getProductById = async (id: string) => {
    try {
      this.setLoading(true)
      if (id) {
        const res = await getData("products/" + id)
        const product = res.data
        this.setProduct(product)
      } else {
        this.setProduct({} as Product)
      }
    } catch (err) {
      console.log(err)
    } finally {
      this.setLoading(false)
    }
  }

  getDuplicateDowngradelineByCode = async (code: string): Promise<boolean> => {
    try {
      this.setLoading(true)
      if (code) {
        const res = await getData("downgradelines/duplicate/" + code)
        const data = res.data
        if (data !== undefined && data !== null) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    } catch (err) {
      console.log(err)
      return false
    } finally {
      this.setLoading(false)
    }
  }

  searchDowngrades = (searchQuery: string) => {
    getData("downgrades" + searchQuery).then(res => {
      const downgrades = res.data
      downgrades.forEach((item: Downgrade) => {
        item.quantity = item.downgradelines?.length || 0
      })
      this.setDataTable(downgrades)
      this.setLoading(false)
    })
  }

  quickSearch = (headers: TableHeader[], qsFilter: string): void => {
    getData("downgrades").then(res => {
      const downgrades = res.data.filter((r: any) =>
        headers.some((header: TableHeader) => {
          const val = get(r, [header.value])
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter)) ||
            false
          )
        })
      )
      downgrades.forEach((item: Downgrade) => {
        item.quantity = item.downgradelines?.length || 0
      })
      this.setDataTable(downgrades)
      this.setLoading(false)
    })
  }

  saveDowngrade = (downgrade: Downgrade): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!downgrade.id) {
        postData("downgrades/", downgrade)
          .then(res => {
            const downgrade = res.data
            const DowngradeId = { id: downgrade[0].id }
            const addDowngrade = { ...DowngradeId, ...this.downgrade }
            this.setDowngrade(addDowngrade)
            if (addDowngrade.id !== undefined) {
              this.setDowngradeId(addDowngrade.id)
            }
            appModule.sendSuccessNotice("New record has been added.")
            appModule.closeNoticeWithDelay(3000)
            resolve()
          })
          .catch(err => {
            console.log(err)
            appModule.sendErrorNotice("Operation failed! Please try again later.")
            appModule.closeNoticeWithDelay(5000)
            reject(err)
          })
      } else {
        putData("downgrades/" + downgrade.id.toString(), downgrade)
          .then(() => {
            appModule.sendSuccessNotice("The record has been updated.")
            appModule.closeNoticeWithDelay(3000)
            resolve()
          })
          .catch(err => {
            console.log(err)
            appModule.sendErrorNotice("Operation failed! Please try again later.")
            appModule.closeNoticeWithDelay(5000)
            reject(err)
          })
      }
    })
  }

  addDowngradelineToDowngrade = (downgradeline: Downgradeline) => {
    if (downgradeline && this.downgrade.id) {
      this.saveDowngradeline(downgradeline)
      this.getDowngradeById(this.downgrade.id.toString())
      const newDowngrade = this.downgrade
      this.setDowngrade(newDowngrade)
    }
  }

  saveDowngradeline = (downgradeline: Downgradeline) => {
    if (!downgradeline.id) {
      postData("downgradelines/", downgradeline)
        .then(res => {
          const downgradeline = res.data
          this.setDowngradeline([downgradeline])
          appModule.sendSuccessNotice("New record has been added.")
          appModule.closeNoticeWithDelay(3000)
        })
        .catch(err => {
          console.log(err)
          appModule.sendErrorNotice("Operation failed! Please try again later.")
          appModule.closeNoticeWithDelay(5000)
        })
    } else {
      putData("downgrades/" + downgradeline.id.toString(), downgradeline)
        .then(res => {
          const downgrade = res.data
          this.setDowngrade(downgrade)
          appModule.sendSuccessNotice("The record has been updated.")
          appModule.closeNoticeWithDelay(3000)
        })
        .catch(err => {
          console.log(err)
          appModule.sendErrorNotice("Operation failed! Please try again later.")
          appModule.closeNoticeWithDelay(5000)
        })
    }
  }

  deleteDowngrade = async (id: number) => {
    try {
      await deleteData(`downgrades/${id.toString()}`)
      appModule.sendSuccessNotice("Operation is done.")
      appModule.closeNoticeWithDelay(3000)
    } catch (error) {
      console.error(error)
      appModule.sendErrorNotice("Operation failed! Please try again later.")
      appModule.closeNoticeWithDelay(5000)
    } finally {
      this.setLoading(false)
    }
  }

  deleteDowngradeline = (downgradeline: Downgradeline) => {
    if (downgradeline && downgradeline.id && this.downgrade.id) {
      const downgradeId = this.downgrade.id
      const downgradelineId = downgradeline.id
      const { downgradelines } = this.downgrade
      if (downgradelines) {
        downgradelines.splice(
          downgradelines.findIndex((p: Downgradeline) => p.id === downgradeline.id),
          1
        )
        this.setDowngradeline(downgradelines)
        deleteData(`downgradelines/${downgradelineId.toString()}`)
          .then(() => {
            this.getDowngradeById(downgradeId.toString())
            const newDowngrade = this.downgrade
            this.setDowngrade(newDowngrade)
            appModule.sendSuccessNotice("Operation is done.")
            appModule.closeNoticeWithDelay(3000)
          })
          .catch(err => {
            console.log(err)
            appModule.sendErrorNotice("Operation failed! Please try again later.")
            appModule.closeNoticeWithDelay(5000)
          })
      }
    }
  }

  clearDowngradeline = () => {
    this.setLoading(true)
    const downgradeline: Downgradeline[] = []
    this.setDowngradeline(downgradeline)
    this.setLoading(false)
  }

  setDataTable = (items: Downgrade[]) => {
    const pagination = getPagination(items)
    this.setPagination(pagination)
    this.setItems(items)
  }

  setEmployees = (employees: Employee[]) => {
    this.employees = employees
  }

  setCategories = (categories: Category[]) => {
    this.categories = categories
  }

  setDowngradeId = (id: number | null) => {
    this.downgradeId = id
  }

  setDowngrade = (downgrade: Downgrade) => {
    this.downgrade = downgrade
  }

  setDowngradeline = (downgradeline: Downgradeline[]) => {
    this.downgradeline = downgradeline
  }

  setProduct = (product: Product) => {
    this.product = product
  }

  setItems = (downgrades: Downgrade[]) => {
    this.items = downgrades
  }

  setPagination = (pagination: Pagination) => {
    this.pagination = pagination
  }

  setLoading = (loading: boolean) => {
    this.loading = loading
  }
}

// Create and export a singleton instance
const app = createApp(DowngradeModule)
const vm = app.mount(document.createElement('div'))
export const downgradeModule = vm as InstanceType<typeof DowngradeModule>
