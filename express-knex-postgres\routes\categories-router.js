const router = require("express").Router();

const categoriesDB = require("../models/categories-model.js");

// GET ALL CATEGORIES
router.get("/", async (req, res) => {
  try {
    const categories = await categoriesDB.find();
    res.status(200).json(categories);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET CATEGORY BY ID
router.get("/:id", async (req, res) => {
  const categoryId = req.params.id;
  try {
    const category = await categoriesDB.findById(categoryId);
    if (!category) {
      res.status(404).json({ err: "The the specified id does not exist" });
    } else {
      res.status(200).json(category);
    }
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// INSERT CATEGORY INTO DB
router.post("/", async (req, res) => {
  const newCategory = req.body;
  if (!newCategory.firstname) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const category = await categoriesDB.addCategory(newCategory);
      res.status(201).json(category);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

router.put("/:id", async (req, res) => {
  const categoryId = req.params.id;
  const newChanges = req.body;
  if (!newChanges.firstname) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const addChanges = await categoryDB.updateCategory(categoryId, newChanges);
      res.status(200).json(addChanges);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

router.delete("/:id", async (req, res) => {
  const categoryId = req.params.id;
  try {
    const deleting = await categoriesDB.removeCategory(categoryId);
    res.status(204).json(deleting);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;
