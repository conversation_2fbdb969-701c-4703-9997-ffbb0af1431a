<template>
  <v-container fluid>
    <v-flex xs12>
      <v-card class="grey lighten-4 elevation-0">
        <v-form ref="validForm" v-model="formValid" lazy-validation>
          <v-text-field
            name="id"
            label="單號"
            type="number"
            hint="PInventoryID is required"
            value="Input text"
            v-model="pinventory.id"
            variant="outlined"
            readonly
          ></v-text-field>
          <v-menu
            :close-on-content-click="false"
            v-model="classDateMenu"
            transition="v-scale-transition"
            offset-y
            :nudge-left="40"
            max-width="290px"
          >
            <template v-slot:activator="{ props }">
              <v-text-field
                v-bind="props"
                label="日期"
                v-model="pinventory.classDate"
                prepend-icon="mdi-calendar"
                readonly
              ></v-text-field>
            </template>
            <v-date-picker
              v-model="pinventory.classDate"
              no-title
              scrollable
            >
            </v-date-picker>
          </v-menu>
          <v-radio-group
            name="shiftName"
            label="勤別"
            v-model="pinventory.shiftName"
            :rules="[(value: string) => !!value || '必要!!請選擇']"
            required
            row
          >
            <v-radio label="I" value="1"></v-radio>
            <v-radio label="II" value="2"></v-radio>
            <v-radio label="III" value="3"></v-radio>
          </v-radio-group>
          <v-autocomplete
            :items="employees"
            label="人員"
            item-title="employName"
            item-value="employId"
            v-model="pinventory.employId"
            :rules="[(value: number) => !!value || '必要!!請選擇']"
            required
          ></v-autocomplete>
          <v-radio-group
            name="groupName"
            label="組別"
            v-model="pinventory.groupName"
            :rules="[(value: string) => !!value || '必要!!請選擇']"
            required
            row
          >
            <v-radio label="A" value="A"></v-radio>
            <v-radio label="B" value="B"></v-radio>
            <v-radio label="C" value="C"></v-radio>
            <v-radio label="D" value="D"></v-radio>
          </v-radio-group>
          <v-text-field
            name="quantity"
            label="個數"
            type="number"
            v-model="pinventory.quantity"
            variant="outlined"
            readonly
          ></v-text-field>
        </v-form>
      </v-card>
    </v-flex>
    <v-dialog v-model="addProductModal" width="700" persistent>
      <v-form ref="validDetail" v-model="detailValid" lazy-validation>
        <v-card>
          <v-card-title>
            {{ modalTitle }}
            <v-spacer></v-spacer>
            <v-card-actions>
              <v-btn
                color="success"
                variant="text"
                :disabled="!detailValid"
                @click="savePInventoryline"
              >
                Confirm
              </v-btn>
              <v-btn
                color="warning"
                variant="text"
                @click="cancelAddProduct"
              >
                Cancel
              </v-btn>
            </v-card-actions>
          </v-card-title>
          <v-card-text>
            {{ modalText }}
            <v-text-field
              ref="qrCodeInput"
              v-model="searchFilter.contain.codeName"
              append-icon="mdi-magnify"
              label="Cap QRCode"
              @change="getProduct"
              counter="14"
              :rules="[(value: string) => !!value || '必要!!請選擇']"
              required
            ></v-text-field>
          </v-card-text>
        </v-card>
      </v-form>
    </v-dialog>
    <confirm-dialog
      :dialog="dialog"
      :dialogTitle="dialogTitle"
      :dialogText="dialogText"
      @onConfirm="onConfirm"
      @onCancel="onCancel"
    ></confirm-dialog>
    <v-snackbar
      v-model="snackbar.show"
      location="top end"
      :timeout="5000"
      :color="snackbar.color"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn variant="text" @click="snackbar.show = false">Close</v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Table from '@/components/Table.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import { productModule } from '@/store/modules/products'
import { pinventoryModule } from '@/store/modules/pinventories'
import { appModule } from '@/stores/app'
import { buildSearchFilters, getISOClassDate } from '@/utils/app-util'
import { PInventory, PInventoryline, Product } from '@/types'

interface Header {
  text: string
  left?: boolean
  value: string
  sortable?: boolean
}

interface ValidForm {
  validate: () => boolean
}

// Template refs
const validForm = ref<ValidForm>()
const validDetail = ref<ValidForm>()
const qrCodeInput = ref<HTMLElement>()

// Router
const router = useRouter()
const route = useRoute()

// Reactive data
const modalTitle = ref("新增Pack盤點(明細)")
const modalText = ref("請掃Cap QRCode")
const addProductModal = ref(false)
const dialog = ref(false)
const dialogTitle = ref("Pack盤點(明細)刪除確認")
const dialogText = ref("刪除該筆記錄?")
const classDateMenu = ref(false)
const errors = ref<string[]>([])
const formValid = ref(false)
const detailValid = ref(false)
const title = ref("")
const type = ref("PACK")
const pinventoryId = ref<number | null>(null)
const pinventorylineId = ref<number | null>(null)
const categoryId = ref(0)
const color = ref("")
const selectedPInventoryline = ref<any>(null)
const isYarnQRcode = ref<boolean | null>(null)
const query = ref("")
const search = ref("")
const pinventory = ref({} as PInventory)
const pinventoryline = ref<PInventoryline[]>([])

const snackbar = ref({
  show: false,
  message: "",
  color: "error"
})

const headers: Header[] = [
  { text: "序號", left: true, value: "countdown" },
  { text: "品種", left: true, value: "productName" },
  { text: "過磅日期", left: true, value: "productDate" },
  { text: "Bushing NO", value: "bushingNO" },
  { text: "Cake位置", value: "positionName" },
  { text: "Cake重量(g)", value: "cakeWeight" },
  { text: "開機日期", left: true, value: "workDate" },
  { text: "Twister NO", value: "twisterNO" },
  { text: "Spindle NO", value: "spindleNO" },
  { text: "", value: "actions", sortable: false }
]

const searchFilter = ref({ contain: { codeName: "" } })

// Computed properties
const employees = computed(() => pinventoryModule.employees)
const product = computed(() => productModule.product)
const loading = computed(() => appModule.loading)
const mode = computed(() => appModule.mode)
const notice = computed(() => appModule.notice)
const pagination = computed(() => pinventoryModule.pagination)

// Methods
const showError = (message: string) => {
  snackbar.value = {
    show: true,
    message,
    color: "error"
  }
}

const showSuccess = (message: string) => {
  snackbar.value = {
    show: true,
    message,
    color: "success"
  }
}

const save = async () => {
  if (!pinventory.value.id) {
    try {
      await pinventoryModule.savePInventory(pinventory.value)
      saveRoute()
    } catch (err: any) {
      console.error("Error:", err.message)
    }
  } else {
    try {
      await pinventoryModule.savePInventory(pinventory.value)
    } catch (err: any) {
      console.error("Error:", err.message)
    }
  }
}

const saveRoute = () => {
  const id = pinventoryModule.pinventoryId
  if (id !== null) {
    pinventoryId.value = id
    router.push(`pinventoryofpack/${id}`)
  }
}

const getPInventoryById = () => {
  const id = route.params.id
  if (id) {
    pinventoryModule.getPInventoryById(id as string)
  }
}

const getProduct = async () => {
  buildSearchFilters(searchFilter.value)
  const codeName = searchFilter.value.contain.codeName
  const trimmedCodeName = codeName.trim()
  if (!trimmedCodeName) {
    showError("請輸入Cap QRcode!")
    return ""
  }
  isYarnQRcode.value = /^\d{14}[a-zA-Z0-9\s.]*$/.test(trimmedCodeName)
  if (!isYarnQRcode.value) {
    showError("無效 Cap QRcode!")
    return ""
  }
  if (isYarnQRcode.value) {
    query.value = trimmedCodeName.slice(0, 14)
    const isDuplicate = await pinventoryModule.getDuplicatePInventorylineByCode(query.value)
    if (isDuplicate) {
      showError("重複 Cap QRcode!")
      return ""
    }
    await productModule.getProductById(query.value)
    if (!Array.isArray(product.value) || !product.value[0]) {
      showError("查無資料!")
      return ""
    } else {
      return product.value
    }
  }
}

const cancel = () => {
  router.push({ name: "pinventoriesofpack" })
}

const remove = (item: any) => {
  selectedPInventoryline.value = item
  dialog.value = true
}

const onConfirm = () => {
  pinventoryModule.deletePInventoryline(selectedPInventoryline.value)
  selectedPInventoryline.value = null
  getPInventoryById()
  dialog.value = false
}

const onCancel = () => {
  selectedPInventoryline.value = null
  dialog.value = false
}

const addProduct = () => {
  addProductModal.value = true
  query.value = ""
  searchFilter.value.contain.codeName = ""
  if (pinventory.value.id !== undefined) {
    pinventoryId.value = pinventory.value.id
  }
  productModule.clearProducts()
  nextTick(() => {
    if (validDetail.value) {
      validDetail.value.validate()
    }
    nextTick(() => {
      if (qrCodeInput.value) {
        qrCodeInput.value.focus()
      }
    })
  })
}

const savePInventoryline = () => {
  if (pinventory.value.id !== undefined && pinventory.value.id !== null) {
    const pinventoryIdValue = pinventory.value.id
    if (Array.isArray(product.value) && product.value[0]) {
      const addProduct = product.value[0]
      const newPInventoryline: PInventoryline = {
        id: null,
        value: null,
        pinventoryId: pinventoryIdValue,
        furnaceName: addProduct.furnaceName,
        productName: addProduct.productName,
        productDate: addProduct.ProductDate,
        categoryId: addProduct.categoryId,
        category: addProduct.category,
        categoryName: addProduct.categoryName || '',
        bushingNO: addProduct.bushingNO,
        workDate: addProduct.workDate,
        positionName: addProduct.positionName,
        cakeWeight: addProduct.cakeWeight,
        twisterNO: addProduct.twisterNO || '',
        spindleNO: addProduct.spindleNO,
        texName: addProduct.texName,
        biName: addProduct.biName,
        batchName: addProduct.batchName,
        codeName: addProduct.codeName,
        documentNO: 0,
        tracksheetNO: '',
        m_product_id: addProduct.m_product_id,
        m_twqrcodeline_id: addProduct.m_twqrcodeline_id,
        created: new Date().toISOString(),
        typeName: type.value,
        classDate: pinventory.value.classDate
      }
      pinventoryModule.addPInventorylineToPInventory(newPInventoryline)
      pinventorylineId.value = null
      getPInventoryById()
      resetForm()
    }
  }
}

const resetForm = () => {
  searchFilter.value.contain.codeName = ""
  productModule.clearProducts()
  nextTick(() => {
    if (qrCodeInput.value) {
      qrCodeInput.value.focus()
    }
  })
}

const cancelAddProduct = () => {
  addProductModal.value = false
  query.value = ""
  searchFilter.value.contain.codeName = ""
  pinventoryModule.clearPInventoryline()
  productModule.clearProducts()
  getPInventoryById()
}

// Lifecycle hooks
onMounted(() => {
  getPInventoryById()
  pinventoryModule.getEmployees()

  window.scrollTo(0, 0)
  if (route.params.id) {
    title.value = "Pack盤點(明細)"
    pinventoryModule.clearPInventoryline()
    pinventoryModule.getPInventoryById(route.params.id as string)
  } else {
    title.value = "Pack盤點(新增)"
    pinventory.value.typeName = type.value
    const toDate = getISOClassDate()
    pinventory.value.classDate = toDate.slice(0, 10)
    if (validForm.value) {
      validForm.value.validate()
    }
  }
})
</script>
