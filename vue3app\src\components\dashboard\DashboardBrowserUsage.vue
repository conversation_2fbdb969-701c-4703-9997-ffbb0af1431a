<template>
  <v-card-text>
    <div class="d-flex align-center justify-space-between mb-4">
      <div>
        <div class="text-h6">Browser Usage</div>
        <div class="text-subtitle-2">Last 30 days</div>
      </div>
      <v-btn icon="mdi-dots-vertical" variant="text"></v-btn>
    </div>
    <v-list>
      <v-list-item
        v-for="(browser, index) in browsers"
        :key="index"
      >
        <template v-slot:prepend>
          <v-icon :color="browser.color">{{ browser.icon }}</v-icon>
        </template>
        <v-list-item-title>{{ browser.name }}</v-list-item-title>
        <template v-slot:append>
          <span class="text-caption">{{ browser.percentage }}%</span>
        </template>
      </v-list-item>
    </v-list>
  </v-card-text>
</template>

<script setup lang="ts">
interface Browser {
  name: string
  icon: string
  color: string
  percentage: number
}

const browsers: Browser[] = [
  {
    name: 'Chrome',
    icon: 'mdi-google-chrome',
    color: 'red',
    percentage: 45
  },
  {
    name: 'Firefox',
    icon: 'mdi-firefox',
    color: 'orange',
    percentage: 25
  },
  {
    name: 'Safari',
    icon: 'mdi-apple-safari',
    color: 'blue',
    percentage: 20
  },
  {
    name: 'Edge',
    icon: 'mdi-microsoft-edge',
    color: 'light-blue',
    percentage: 10
  }
]
</script>
