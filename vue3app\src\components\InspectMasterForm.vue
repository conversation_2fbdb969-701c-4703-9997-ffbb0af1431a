<template>
  <v-form ref="form" v-model="formValid" lazy-validation>
    <v-row>
      <v-col cols="12" md="6">
        <v-text-field
          v-model="localFormData.id"
          label="單號"
          variant="outlined"
          readonly
        ></v-text-field>
      </v-col>
      <v-col cols="12" md="6">
        <v-text-field
          v-model="localFormData.classDate"
          label="日期"
          type="date"
          variant="outlined"
          required
          :rules="validationRules.required"
          @update:model-value="updateFormData"
        ></v-text-field>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12" md="6">
        <v-radio-group
          v-model="localFormData.shiftName"
          label="勤別"
          :rules="validationRules.required"
          required
          row
          @update:model-value="updateFormData"
        >
          <v-radio label="I" value="1"></v-radio>
          <v-radio label="II" value="2"></v-radio>
          <v-radio label="III" value="3"></v-radio>
        </v-radio-group>
      </v-col>
      <v-col cols="12" md="6">
        <v-select
          v-model="localFormData.employId"
          :items="employeeOptions"
          item-title="employName"
          item-value="employId"
          label="人員"
          variant="outlined"
          :rules="validationRules.required"
          required
          @update:model-value="updateFormData"
        ></v-select>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12" md="6">
        <v-radio-group
          v-model="localFormData.groupName"
          label="組別"
          :rules="validationRules.required"
          required
          row
          @update:model-value="updateFormData"
        >
          <v-radio label="A" value="A"></v-radio>
          <v-radio label="B" value="B"></v-radio>
          <v-radio label="C" value="C"></v-radio>
          <v-radio label="D" value="D"></v-radio>
        </v-radio-group>
      </v-col>
      <v-col cols="12" md="6">
        <v-select
          v-model="localFormData.typeName"
          :items="typeOptions"
          label="類型"
          variant="outlined"
          :rules="validationRules.required"
          required
          @update:model-value="updateFormData"
        ></v-select>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12">
        <v-text-field
          v-model="localFormData.quantity"
          label="數量"
          type="number"
          variant="outlined"
          :rules="validationRules.number"
          @update:model-value="updateFormData"
        ></v-text-field>
      </v-col>
    </v-row>
  </v-form>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { Inspect } from '@/types'

interface Props {
  modelValue: Partial<Inspect>
  formValid: boolean
  employeeOptions: any[]
  typeOptions: string[]
  validationRules: any
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: Partial<Inspect>): void
  (e: 'update:formValid', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 本地表單數據
const localFormData = ref<Partial<Inspect>>({ ...props.modelValue })
const form = ref()

// 監聽外部數據變化
watch(() => props.modelValue, (newValue) => {
  localFormData.value = { ...newValue }
}, { deep: true })

// 監聽表單驗證狀態
watch(() => props.formValid, (newValue) => {
  emit('update:formValid', newValue)
})

// 更新表單數據
const updateFormData = () => {
  emit('update:modelValue', { ...localFormData.value })
}

// 驗證表單
const validate = () => {
  return form.value?.validate()
}

// 重置表單
const reset = () => {
  form.value?.reset()
}

// 重置驗證
const resetValidation = () => {
  form.value?.resetValidation()
}

// 暴露方法給父組件
defineExpose({
  validate,
  reset,
  resetValidation
})
</script>

<style scoped>
.v-form {
  width: 100%;
}

.v-row {
  margin-bottom: 8px;
}

.v-text-field,
.v-select,
.v-radio-group {
  margin-bottom: 16px;
}
</style>
