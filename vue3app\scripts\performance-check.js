#!/usr/bin/env node

/**
 * AITS 專案性能檢查腳本
 * 
 * 此腳本用於檢查專案的性能指標和代碼品質
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🚀 開始 AITS 專案性能檢查...\n')

// 性能檢查項目
const performanceChecks = [
  {
    name: '📦 打包體積分析',
    test: () => checkBundleSize()
  },
  {
    name: '🔍 TypeScript 類型檢查',
    test: () => checkTypeScript()
  },
  {
    name: '📝 代碼品質檢查',
    test: () => checkCodeQuality()
  },
  {
    name: '🧪 測試覆蓋率檢查',
    test: () => checkTestCoverage()
  },
  {
    name: '📊 依賴分析',
    test: () => checkDependencies()
  },
  {
    name: '🎯 Vue3 遷移進度',
    test: () => checkVue3Migration()
  }
]

// 執行所有檢查
async function runAllChecks() {
  const results = []
  
  for (const check of performanceChecks) {
    console.log(`\n🔍 執行: ${check.name}`)
    console.log('─'.repeat(50))
    
    try {
      const result = await check.test()
      results.push({
        name: check.name,
        status: 'success',
        result: result
      })
      console.log(`✅ ${check.name} - 完成`)
    } catch (error) {
      results.push({
        name: check.name,
        status: 'error',
        error: error.message
      })
      console.log(`❌ ${check.name} - 失敗: ${error.message}`)
    }
  }
  
  // 生成報告
  generateReport(results)
}

// 檢查打包體積
function checkBundleSize() {
  console.log('分析打包體積...')
  
  try {
    // 檢查 dist 目錄是否存在
    const distPath = path.join(process.cwd(), 'dist')
    if (!fs.existsSync(distPath)) {
      console.log('⚠️  dist 目錄不存在，執行構建...')
      execSync('npm run build', { stdio: 'inherit' })
    }
    
    // 分析文件大小
    const files = fs.readdirSync(distPath, { recursive: true })
    const jsFiles = files.filter(file => file.endsWith('.js'))
    const cssFiles = files.filter(file => file.endsWith('.css'))
    
    let totalSize = 0
    const fileStats = []
    
    files.forEach(file => {
      const filePath = path.join(distPath, file)
      if (fs.statSync(filePath).isFile()) {
        const size = fs.statSync(filePath).size
        totalSize += size
        fileStats.push({
          name: file,
          size: formatBytes(size)
        })
      }
    })
    
    console.log(`📦 總打包大小: ${formatBytes(totalSize)}`)
    console.log(`📄 JS 文件數量: ${jsFiles.length}`)
    console.log(`🎨 CSS 文件數量: ${cssFiles.length}`)
    
    // 檢查是否超過建議大小 (5MB)
    const maxSize = 5 * 1024 * 1024
    if (totalSize > maxSize) {
      console.log(`⚠️  打包體積過大，建議優化 (當前: ${formatBytes(totalSize)}, 建議: ${formatBytes(maxSize)})`)
    }
    
    return {
      totalSize: formatBytes(totalSize),
      jsFiles: jsFiles.length,
      cssFiles: cssFiles.length,
      status: totalSize <= maxSize ? 'good' : 'warning'
    }
  } catch (error) {
    throw new Error(`打包體積檢查失敗: ${error.message}`)
  }
}

// 檢查 TypeScript
function checkTypeScript() {
  console.log('檢查 TypeScript 類型...')
  
  try {
    execSync('npx vue-tsc --noEmit', { stdio: 'pipe' })
    console.log('✅ TypeScript 類型檢查通過')
    return { status: 'good', errors: 0 }
  } catch (error) {
    const output = error.stdout?.toString() || error.stderr?.toString() || ''
    const errorCount = (output.match(/error TS/g) || []).length
    console.log(`⚠️  發現 ${errorCount} 個 TypeScript 錯誤`)
    return { status: 'warning', errors: errorCount, output: output }
  }
}

// 檢查代碼品質
function checkCodeQuality() {
  console.log('檢查代碼品質...')
  
  try {
    execSync('npx eslint src --ext .vue,.js,.ts', { stdio: 'pipe' })
    console.log('✅ ESLint 檢查通過')
    return { status: 'good', issues: 0 }
  } catch (error) {
    const output = error.stdout?.toString() || ''
    const warningCount = (output.match(/warning/g) || []).length
    const errorCount = (output.match(/error/g) || []).length
    
    console.log(`⚠️  發現 ${errorCount} 個錯誤, ${warningCount} 個警告`)
    return { 
      status: errorCount > 0 ? 'error' : 'warning', 
      errors: errorCount,
      warnings: warningCount 
    }
  }
}

// 檢查測試覆蓋率
function checkTestCoverage() {
  console.log('檢查測試覆蓋率...')
  
  try {
    const output = execSync('npx vitest run --coverage', { stdio: 'pipe' }).toString()
    
    // 解析覆蓋率數據
    const coverageMatch = output.match(/All files\s+\|\s+([\d.]+)/)
    const coverage = coverageMatch ? parseFloat(coverageMatch[1]) : 0
    
    console.log(`📊 測試覆蓋率: ${coverage}%`)
    
    return {
      coverage: coverage,
      status: coverage >= 80 ? 'good' : coverage >= 60 ? 'warning' : 'poor'
    }
  } catch (error) {
    console.log('⚠️  無法執行測試覆蓋率檢查')
    return { status: 'unknown', coverage: 0 }
  }
}

// 檢查依賴
function checkDependencies() {
  console.log('分析專案依賴...')
  
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  const dependencies = Object.keys(packageJson.dependencies || {})
  const devDependencies = Object.keys(packageJson.devDependencies || {})
  
  console.log(`📦 生產依賴: ${dependencies.length}`)
  console.log(`🔧 開發依賴: ${devDependencies.length}`)
  
  // 檢查過時的依賴
  try {
    const outdated = execSync('npm outdated --json', { stdio: 'pipe' }).toString()
    const outdatedPackages = Object.keys(JSON.parse(outdated))
    console.log(`⚠️  過時的依賴: ${outdatedPackages.length}`)
    
    return {
      dependencies: dependencies.length,
      devDependencies: devDependencies.length,
      outdated: outdatedPackages.length,
      status: outdatedPackages.length <= 5 ? 'good' : 'warning'
    }
  } catch (error) {
    return {
      dependencies: dependencies.length,
      devDependencies: devDependencies.length,
      outdated: 0,
      status: 'good'
    }
  }
}

// 檢查 Vue3 遷移進度
function checkVue3Migration() {
  console.log('檢查 Vue3 遷移進度...')
  
  const srcPath = path.join(process.cwd(), 'src', 'pages')
  const files = fs.readdirSync(srcPath).filter(file => file.endsWith('.vue'))
  
  let vue3Count = 0
  let vue2Count = 0
  
  files.forEach(file => {
    const content = fs.readFileSync(path.join(srcPath, file), 'utf8')
    
    if (content.includes('<script setup') || content.includes('Vue3Simple')) {
      vue3Count++
    } else if (content.includes('@Component') || content.includes('class ')) {
      vue2Count++
    }
  })
  
  const totalFiles = vue3Count + vue2Count
  const migrationProgress = totalFiles > 0 ? (vue3Count / totalFiles * 100).toFixed(1) : 0
  
  console.log(`📊 Vue3 遷移進度: ${migrationProgress}%`)
  console.log(`✅ Vue3 組件: ${vue3Count}`)
  console.log(`⚠️  Vue2 組件: ${vue2Count}`)
  
  return {
    vue3Count,
    vue2Count,
    progress: migrationProgress,
    status: migrationProgress >= 90 ? 'good' : migrationProgress >= 70 ? 'warning' : 'poor'
  }
}

// 生成報告
function generateReport(results) {
  console.log('\n' + '='.repeat(60))
  console.log('📊 AITS 專案性能檢查報告')
  console.log('='.repeat(60))
  
  results.forEach(result => {
    const status = result.status === 'success' ? '✅' : '❌'
    console.log(`${status} ${result.name}`)
    
    if (result.result) {
      console.log(`   狀態: ${getStatusEmoji(result.result.status)}`)
    }
  })
  
  console.log('\n📈 總體評估:')
  const successCount = results.filter(r => r.status === 'success').length
  const totalCount = results.length
  const successRate = (successCount / totalCount * 100).toFixed(1)
  
  console.log(`   成功率: ${successRate}% (${successCount}/${totalCount})`)
  
  if (successRate >= 80) {
    console.log('🎉 專案狀態良好！')
  } else if (successRate >= 60) {
    console.log('⚠️  專案需要一些改進')
  } else {
    console.log('🚨 專案需要重點關注')
  }
  
  console.log('\n📝 建議:')
  console.log('1. 完成剩餘的 Vue3 遷移')
  console.log('2. 修復 TypeScript 類型錯誤')
  console.log('3. 增加單元測試覆蓋率')
  console.log('4. 定期更新依賴包')
}

// 輔助函數
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function getStatusEmoji(status) {
  switch (status) {
    case 'good': return '🟢 良好'
    case 'warning': return '🟡 警告'
    case 'poor': return '🔴 需改進'
    default: return '⚪ 未知'
  }
}

// 執行檢查
if (require.main === module) {
  runAllChecks().catch(error => {
    console.error('❌ 性能檢查失敗:', error)
    process.exit(1)
  })
}

module.exports = { runAllChecks }
