<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <span class="text-h4">
              <v-icon class="mr-2">mdi-arrow-down-bold</v-icon>
              {{ isEditMode ? '編輯Downgrade T2記錄' : '新增Downgrade T2記錄' }}
            </span>
          </v-card-title>

          <v-card-text>
            <v-form ref="form" v-model="formValid">
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.id"
                    label="單號"
                    :rules="[rules.required]"
                    outlined
                    dense
                    readonly
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="formData.classDate"
                    label="班別日期"
                    type="date"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.shiftName"
                    :items="shiftOptions"
                    label="勤別"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.employeeId"
                    :items="employeeOptions"
                    item-title="employName"
                    item-value="id"
                    label="人員"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.groupName"
                    :items="groupOptions"
                    label="組別"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-select
                    v-model="formData.typeName"
                    :items="typeOptions"
                    label="類型"
                    :rules="[rules.required]"
                    outlined
                    dense
                  />
                </v-col>
              </v-row>
            </v-form>

            <v-divider class="my-4" />

            <!-- 明細檔表格 -->
            <v-row>
              <v-col cols="12">
                <h3>
                  <v-icon class="mr-2">mdi-format-list-bulleted</v-icon>
                  Downgrade T2明細
                </h3>

                <v-card variant="outlined" class="mt-3">
                  <v-card-title>
                    <v-spacer />
                    <v-btn
                      color="primary"
                      @click="addDetailItem"
                      prepend-icon="mdi-plus"
                      size="small"
                    >
                      新增明細
                    </v-btn>
                  </v-card-title>

                  <v-card-text>
                    <v-data-table
                      :headers="detailHeaders"
                      :items="detailItems"
                      :items-per-page="5"
                      class="elevation-1"
                    >
                      <template v-slot:item.actions="{ item }">
                        <v-btn
                          icon="mdi-pencil"
                          size="small"
                          color="primary"
                          @click="editDetailItem(item)"
                          class="mr-2"
                        />
                        <v-btn
                          icon="mdi-delete"
                          size="small"
                          color="error"
                          @click="deleteDetailItem(item.id)"
                        />
                      </template>

                      <template v-slot:no-data>
                        <v-alert type="info" class="ma-4">
                          尚無明細資料，請點擊「新增明細」按鈕添加。
                        </v-alert>
                      </template>
                    </v-data-table>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>

          <v-card-actions>
            <v-btn
              color="primary"
              @click="saveForm"
              :disabled="!formValid"
              prepend-icon="mdi-content-save"
            >
              {{ isEditMode ? '更新' : '儲存' }}
            </v-btn>

            <v-btn
              color="secondary"
              @click="resetForm"
              prepend-icon="mdi-refresh"
            >
              重設
            </v-btn>

            <v-btn
              color="error"
              @click="goBack"
              prepend-icon="mdi-arrow-left"
            >
              返回
            </v-btn>

            <v-spacer />

            <v-chip color="info">
              表單狀態: {{ formValid ? '有效' : '無效' }}
            </v-chip>

            <v-chip color="warning" class="ml-2">
              明細數量: {{ detailItems.length }}
            </v-chip>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <!-- 明細編輯對話框 -->
    <v-dialog v-model="detailDialog" max-width="800px">
      <v-card>
        <v-card-title>
          <span class="text-h5">{{ editingDetailId ? '編輯' : '新增' }}Downgrade T2明細</span>
        </v-card-title>

        <v-card-text>
          <v-form ref="detailForm" v-model="detailFormValid">
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="detailData.codeName"
                  label="產品代碼"
                  :rules="[rules.required]"
                  outlined
                  dense
                />
              </v-col>

              <v-col cols="12" md="6">
                <v-text-field
                  v-model="detailData.productName"
                  label="產品名稱"
                  outlined
                  dense
                />
              </v-col>

              <v-col cols="12" md="6">
                <v-select
                  v-model="detailData.categoryId"
                  :items="categoryOptions"
                  item-title="name"
                  item-value="id"
                  label="類別"
                  :rules="[rules.required]"
                  outlined
                  dense
                />
              </v-col>

              <v-col cols="12" md="6">
                <v-select
                  v-model="detailData.remarkId"
                  :items="remarkOptions"
                  item-title="name"
                  item-value="id"
                  label="異常原因"
                  :rules="[rules.required]"
                  outlined
                  dense
                />
              </v-col>

              <v-col cols="12" md="6">
                <v-select
                  v-model="detailData.gradeId"
                  :items="gradeOptions"
                  item-title="name"
                  item-value="id"
                  label="等級"
                  outlined
                  dense
                />
              </v-col>

              <v-col cols="12" md="6">
                <v-text-field
                  v-model="detailData.cakeWeight"
                  label="重量"
                  type="number"
                  outlined
                  dense
                />
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>

        <v-card-actions>
          <v-spacer />
          <v-btn
            color="grey"
            @click="closeDetailDialog"
          >
            取消
          </v-btn>
          <v-btn
            color="primary"
            @click="saveDetailItem"
            :disabled="!detailFormValid"
          >
            {{ editingDetailId ? '更新' : '新增' }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 成功訊息 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          關閉
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { downgradeModule } from '@/store/modules/downgrades'
import { productModule } from '@/store/modules/products'

const route = useRoute()
const router = useRouter()

// 響應式數據
const form = ref()
const detailForm = ref()
const formValid = ref(false)
const detailFormValid = ref(false)

const formData = ref({
  id: 0,
  classDate: new Date().toISOString().slice(0, 10),
  shiftName: '',
  employeeId: 0,
  groupName: '',
  typeName: 'YARN'
})

// 明細檔數據
const detailItems = ref([])
const detailDialog = ref(false)
const editingDetailId = ref(null)

const detailData = ref({
  codeName: '',
  productName: '',
  categoryId: 0,
  remarkId: 0,
  gradeId: 0,
  cakeWeight: 0
})

const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

// 選項數據
const shiftOptions = ['早班', '中班', '晚班']
const groupOptions = ['A組', 'B組', 'C組', 'D組']
const typeOptions = ['YARN', 'CAKE', 'PACK']

const employeeOptions = ref([])
const categoryOptions = ref([])
const remarkOptions = ref([])
const gradeOptions = ref([])

// 明細檔表格標題
const detailHeaders = [
  { title: 'ID', key: 'id', align: 'start' },
  { title: '產品代碼', key: 'codeName', align: 'start' },
  { title: '產品名稱', key: 'productName', align: 'start' },
  { title: '類別', key: 'categoryName', align: 'start' },
  { title: '異常原因', key: 'remarkName', align: 'start' },
  { title: '等級', key: 'gradeName', align: 'start' },
  { title: '重量', key: 'cakeWeight', align: 'end' },
  { title: '操作', key: 'actions', align: 'center', sortable: false }
]

// 驗證規則
const rules = {
  required: (value: any) => !!value || '此欄位為必填'
}

// 計算屬性
const isEditMode = computed(() => !!route.params.id)

// 方法
const saveForm = async () => {
  try {
    await downgradeModule.saveDowngrade(formData.value)
    showMessage(
      isEditMode.value ? '更新成功！' : '儲存成功！',
      'success'
    )

    if (!isEditMode.value) {
      const newId = downgradeModule.downgradeId
      if (newId) {
        router.push(`/downgrade/${newId}`)
      }
    }
  } catch (error) {
    showMessage('操作失敗，請重試', 'error')
  }
}

const resetForm = () => {
  formData.value = {
    id: 0,
    classDate: new Date().toISOString().slice(0, 10),
    shiftName: '',
    employeeId: 0,
    groupName: '',
    typeName: 'YARN'
  }
  detailItems.value = []
  form.value?.resetValidation()
  showMessage('表單已重設', 'info')
}

const goBack = () => {
  router.push('/downgrades')
}

// 明細檔操作方法
const addDetailItem = () => {
  editingDetailId.value = null
  detailData.value = {
    codeName: '',
    productName: '',
    categoryId: 0,
    remarkId: 0,
    gradeId: 0,
    cakeWeight: 0
  }
  detailDialog.value = true
}

const editDetailItem = (item: any) => {
  editingDetailId.value = item.id
  detailData.value = { ...item }
  detailDialog.value = true
}

const saveDetailItem = () => {
  if (!detailFormValid.value) return

  if (editingDetailId.value) {
    // 編輯模式
    const index = detailItems.value.findIndex((item: any) => item.id === editingDetailId.value)
    if (index > -1) {
      detailItems.value[index] = { ...detailData.value, id: editingDetailId.value }
      showMessage('明細項目已更新', 'success')
    }
  } else {
    // 新增模式
    const newDetail = { ...detailData.value, id: Date.now() }
    detailItems.value.push(newDetail)
    showMessage('明細項目已新增', 'success')
  }

  closeDetailDialog()
}

const closeDetailDialog = () => {
  detailDialog.value = false
  editingDetailId.value = null
  detailForm.value?.resetValidation()
}

const deleteDetailItem = (id: number) => {
  const index = detailItems.value.findIndex((item: any) => item.id === id)
  if (index > -1) {
    detailItems.value.splice(index, 1)
    showMessage('已刪除明細項目', 'warning')
  }
}

const showMessage = (message: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}

// 載入選項數據
const loadOptions = async () => {
  try {
    // 載入員工選項
    await downgradeModule.getEmployees()
    employeeOptions.value = downgradeModule.employees

    // 載入類別選項
    await productModule.getCategories()
    categoryOptions.value = productModule.categories

    // 載入異常原因選項
    await productModule.getRemarks()
    remarkOptions.value = productModule.remarks

    // 模擬等級選項
    gradeOptions.value = [
      { id: 1, name: 'A級' },
      { id: 2, name: 'B級' },
      { id: 3, name: 'C級' }
    ]
  } catch (error) {
    console.error('載入選項數據失敗:', error)
  }
}

// 生命週期
onMounted(async () => {
  await loadOptions()

  if (isEditMode.value) {
    // 載入編輯數據
    await downgradeModule.getDowngradeById(route.params.id as string)
    const downgrade = downgradeModule.downgrade

    formData.value = {
      id: downgrade.id || 0,
      classDate: downgrade.classDate || new Date().toISOString().slice(0, 10),
      shiftName: downgrade.shiftName || '',
      employeeId: downgrade.employeeId || 0,
      groupName: downgrade.groupName || '',
      typeName: downgrade.typeName || 'YARN'
    }

    detailItems.value = downgrade.downgradelines || []
    showMessage(`載入編輯數據 (ID: ${route.params.id})`, 'info')
  } else {
    showMessage('新增模式已準備就緒', 'info')
  }
})
</script>

<style scoped>
.v-card {
  margin-bottom: 16px;
}
</style>
