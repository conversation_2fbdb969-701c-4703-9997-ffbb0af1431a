import { getData, putData, postData, deleteData } from "@/utils/backend-api";
import {
  Employee,
  PInventory,
  Entity,
  Product,
  Tracksheet,
  PInventoryline
} from "@/types";
import { getDefaultPagination, getPagination } from "@/utils/store-util";
import { appModule } from "./app";
import { get } from "lodash";
import {
  VuexModule,
  Module,
  Mutation,
  Action,
  getModule
} from "vuex-module-decorators";
import store from "@/store";

export interface PInventoryState {
  items: Entity[];
  pagination: Pagination;
  loading: boolean;
  pinventoryId: number;
  pinventory: PInventory;
  employee: string;
  pinventoryline: PInventoryline[];
  tracksheet: Tracksheet;
  employees: Employee[];
}

@Module({ store, dynamic: true, name: "pinventories" })
class PInventoryModule extends VuexModule implements PInventoryState {
  public items: Entity[] = [];
  public pagination = getDefaultPagination();
  public loading = false;
  public employee = "";
  public pinventoryId = null;
  public pinventory = {} as PInventory;
  public pinventoryline: PInventoryline[] = [];
  public employees: Employee[] = [];
  public product = {} as Product;
  public tracksheet = {} as Tracksheet;

  @Action
  getEmployees() {
    getData("employees/tw").then(res => {
      if (res.data) {
        const employees = res.data.map((c: Employee) => {
          c.employName = c.employNO + " " + c.userName;
          c.value = c.id;
          return c;
        });
        this.setEmployees(employees);
      }
    });
  }

  @Action
  async getProductById(id: string) {
    try {
      this.setLoading(true);
      if (id) {
        const res = await getData("products/" + id);
        const product = res.data;
        this.setProduct(product);
      } else {
        this.setProduct({} as Product);
      }
    } catch (err) {
        console.log(err);
    } finally {
      this.setLoading(false);
    }
  }

  @Action
  async getAllPInventoriesByType(type: string) {
    this.setLoading(true);
    try {
      const res = await getData(`pinventories/${type}`);
      const pinventories = res.data;

      this.setPInventory(pinventories);
      this.setDataTable(pinventories);
    } catch (error) {
      console.error(error);
    } finally {
      this.setLoading(false);
    }
  }

  @Action
  getPInventoryById(id: string) {
    if (id) {
      getData("pinventories/" + id).then(
        res => {
          const _pinventory = res.data;
          const pinventory = _pinventory[0];
          pinventory.quantity = pinventory.pinventorylines?.length;
          this.setPInventory(pinventory);
          this.setDataTable(pinventory.pinventorylines);
        },
        (err: TODO) => {
          console.log(err);
        }
      );
    } else {
      const pinventory = {} as PInventory;
      pinventory.pinventorylines = [];
      this.setPInventory(pinventory);
      this.setLoading(false);
    }
  }

  @Action
  async geTracksheetById(id: string) {
    try {
      this.setLoading(true);
      if (id) {
        const res = await getData("tracksheet/yarn/" + id);
        const tracksheet = res.data;
        this.setTracksheet(tracksheet);
      } else {
        this.setTracksheet({} as Tracksheet);
      }
    } catch (err) {
        console.log(err);
    } finally {
        this.setLoading(false);
    }
  }

  @Action
  async getDuplicatePInventorylineByCode(tracks: string[]): Promise<boolean> {
    const type= tracks[0].toString();
    const trackId = tracks[2].toString();
    try {
      this.setLoading(true);
      if (trackId) {
        const res = await getData("pinventorylines/duplicate/" + trackId);
        const data = res.data;
        if (data !== undefined && data !== null) {
          for (const item of data) {
            if (item.typeName === type) {
              return true;
            }
          }
        }
      } else {
        return false;
      }
    } catch (err) {
      console.log(err);
      return false;
    } finally {
      this.setLoading(false);
    }
  }

  @Action
  searchPInventories(searchQuery: string) {
    getData("pinventories" + searchQuery).then(res => {
      const pinventories = res.data;
      pinventories.forEach((item: PInventory) => {
        item.quantity = item.pinventorylines?.length;
      });
      this.setDataTable(pinventories);
      this.setLoading(false);
    });
  }

  @Action
  quickSearch(headers: TableHeader[], qsFilter: SeachQuery): void {
    getData("pinventories").then(res => {
      const pinventories = res.data.filter((r: TODO) =>
        headers.some((header: TableHeader) => {
          const val = get(r, [header.value]);
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter)) ||
            false
          );
        })
      );
      pinventories.forEach((item: PInventory) => {
        item.quantity = item.pinventorylines?.length;
      });
      this.setDataTable(pinventories);
      this.setLoading(false);
    });
  }

  @Action
  savePInventory(pinventory: PInventory): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!pinventory.id) {
        postData("pinventories/", pinventory)
          .then(res => {
            const pinventory = res.data;
            const PInventoryId = { id: pinventory[0].id };
            const addPInventory = { ...PInventoryId, ...this.pinventory};
            this.setPInventory(addPInventory);
            this.setPInventoryId(addPInventory.id);
            appModule.sendSuccessNotice("New record has been added.");
            appModule.closeNoticeWithDelay(3000);
            resolve();
          })
          .catch((err: TODO) => {
            console.log(err);
            appModule.sendErrorNotice("Operation failed! Please try again later.");
            appModule.closeNoticeWithDelay(5000);
            reject(err);
          });
      } else {
        putData("pinventories/" + pinventory.id.toString(), pinventory)
          .then(() => {
            appModule.sendSuccessNotice("The record has been updated.");
            appModule.closeNoticeWithDelay(3000);
            resolve();
          })
          .catch((err: TODO) => {
            console.log(err);
            appModule.sendErrorNotice("Operation failed! Please try again later.");
            appModule.closeNoticeWithDelay(5000);
            reject(err);
          });
      }
    });
  }

  @Action
  addPInventorylineToPInventory(pinventoryline: PInventoryline) {
    if (pinventoryline) {
      const typeName = { typeName: this.pinventory.typeName };
      const classDate = { classDate: this.pinventory.classDate };
      const newPInventoryline = { ...pinventoryline, ...typeName, ...classDate};
      this.savePInventoryline(newPInventoryline);
      const pinventoryId = this.pinventory.id;
      this.getPInventoryById(pinventoryId.toString());
      const newPInventory = this.pinventory;
      this.setPInventory(newPInventory);
    }
  }

  @Action savePInventoryline(pinventoryline: PInventoryline) {
    if (!pinventoryline.id) {
      postData("pinventorylines/", pinventoryline)
        .then(res => {
          const pinventoryline = res.data;
          this.setPInventoryline(pinventoryline);
          appModule.sendSuccessNotice("New record has been added.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    } else {
      putData("pinventories/" + pinventoryline.id.toString(), pinventoryline)
        .then(res => {
          const pinventory = res.data;
          this.setPInventory(pinventory);
          appModule.sendSuccessNotice("The record has been updated.");
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    }
  }

  @Action
  async deletePInventory(id: number) {
    try {
      await deleteData(`pinventories/${id.toString()}`);
      appModule.sendSuccessNotice("Operation is done.");
      appModule.closeNoticeWithDelay(3000);
    }
    catch (error) {
      console.error(error);
      appModule.sendErrorNotice("Operation failed! Please try again later.");
      appModule.closeNoticeWithDelay(5000);
    }
    finally {
      this.setLoading(false);
    }
  }

  @Action
  deletePInventoryline(pinventoryline: PInventoryline) {
    if (pinventoryline) {
      const pinventoryId = this.pinventory.id;
      const pinventorylineId = pinventoryline.id;
      const { pinventorylines } = this.pinventory;
      pinventorylines.splice(
        pinventorylines.findIndex((p: PInventoryline) => p.id === pinventoryline.id),1
      );
      this.setPInventoryline(pinventorylines);
      deleteData(`pinventorylines/${pinventorylineId.toString()}`)
        .then(() => {
          this.getPInventoryById(pinventoryId.toString());
          const newPInventory = this.pinventory;
          this.setPInventory(newPInventory);
          appModule.sendSuccessNotice("Operation is done.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    }
  }

  @Action
  clearPInventoryline() {
    this.setLoading(true);
    const pinventoryline = [];
    this.setPInventoryline(pinventoryline);
    this.setLoading(false);
  }

  @Action
  setDataTable(items: PInventory[]) {
    const pagination = getPagination(items);
    this.setPagination(pagination);
    this.setItems(items);
  }

  @Mutation
  setEmployees(employees: Employee[]) {
    this.employees = employees;
  }

  @Mutation
    setPInventoryId(id: number | null) {
      this.pinventoryId = id;
  }

  @Mutation
  setPInventory(pinventory: PInventory) {
    this.pinventory = pinventory;
  }

  @Mutation
  setPInventoryline(pinventoryline: PInventoryline[]) {
    this.pinventoryline = pinventoryline;
  }

  @Mutation
  setTracksheet(tracksheet: Tracksheet) {
    this.tracksheet = tracksheet;
  }

  @Mutation
  setProduct(product: Product) {
    this.product = product;
  }

  @Mutation
  setItems(pinventories: PInventory[]) {
    this.items = pinventories;
  }

  @Mutation
  setPagination(pagination: Pagination) {
    this.pagination = pagination;
  }

  @Mutation
  setLoading(loading: boolean) {
    this.loading = loading;
  }
}

export const pinventoryModule = getModule(PInventoryModule);

