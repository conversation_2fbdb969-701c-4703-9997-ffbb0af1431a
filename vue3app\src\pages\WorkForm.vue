<template>
  <!-- Previous template content remains unchanged until v-text-field -->
  <v-container fluid>
    <v-flex xs12>
      <v-card class="grey lighten-4 elevation-0">
        <v-form ref="validForm" v-model="formValid" lazy-validation>
          <!-- Previous content -->
          <v-text-field
            name="id"
            label="單號"
            type="number"
            hint="WorkID is required"
            value="Input text"
            v-model="work.id"
            variant="outlined"
            readonly
          ></v-text-field>
          <!-- Previous content -->
          <v-menu
            :close-on-content-click="false"
            v-model="workDateMenu"
            transition="v-scale-transition"
            offset-y
            :nudge-left="40"
            max-width="290px"
          >
            <template v-slot:activator="{ props }">
              <v-text-field
                v-bind="props"
                label="日期"
                v-model="work.workDate"
                prepend-icon="mdi-calendar"
                readonly
              ></v-text-field>
            </template>
            <v-date-picker v-model="work.workDate" no-title scrollable>
            </v-date-picker>
          </v-menu>
          <!-- Previous content -->
          <v-radio-group
            name="shiftName"
            label="勤別"
            v-model="work.shiftName"
            :rules="[(value: string) => !!value || '必要!!請選擇']"
            required
            row
          >
            <v-radio label="I" value="1"></v-radio>
            <v-radio label="II" value="2"></v-radio>
            <v-radio label="III" value="3"></v-radio>
          </v-radio-group>
          <!-- Previous content -->
          <v-radio-group
            name="groupName"
            label="組別"
            v-model="work.groupName"
            :rules="[(value: string) => !!value || '必要!!請選擇']"
            required
            row
          >
            <v-radio label="A" value="A"></v-radio>
            <v-radio label="B" value="B"></v-radio>
            <v-radio label="C" value="C"></v-radio>
            <v-radio label="D" value="D"></v-radio>
          </v-radio-group>
          <!-- Previous content -->
          <v-text-field
            name="quantity"
            label="個數"
            type="number"
            v-model="work.quantity"
            variant="outlined"
            readonly
          ></v-text-field>
          <!-- Rest of the template content -->
        </v-form>
      </v-card>
    </v-flex>

    <!-- Dialog content -->
    <v-dialog v-model="addTrackModal" width="700" persistent>
      <v-form ref="validDetail" v-model="detailValid" lazy-validation>
        <v-card>
          <v-card-title>
            捻線開機明細(新增)
            <v-spacer></v-spacer>
            <v-card-actions>
              <v-btn
                color="success"
                variant="text"
                :disabled="!detailValid"
                @click="saveWorkline"
              >
                Confirm
              </v-btn>
              <v-btn
                color="warning"
                variant="text"
                @click="cancelAddTrack"
              >
                Cancel
              </v-btn>
            </v-card-actions>
          </v-card-title>
          <v-card-text>
            <v-text-field
              v-model="searchFilter.contain.twisterNO"
              append-icon="mdi-magnify"
              label="輸入捻線機號"
              @change="getTrack"
              counter="3"
              variant="outlined"
              :rules="[(value: string) => !!value || '必要!!請選擇']"
              required
            ></v-text-field>
            <!-- Rest of dialog content -->
          </v-card-text>
        </v-card>
      </v-form>
    </v-dialog>

    <!-- Confirm dialog -->
    <confirm-dialog
      :dialog="dialog"
      :dialogTitle="dialogTitle"
      :dialogText="dialogText"
      @onConfirm="onConfirm"
      @onCancel="onCancel"
    ></confirm-dialog>

    <!-- Snackbar -->
    <v-snackbar
      v-if="loading === false"
      location="top end"
      :timeout="5000"
      :color="mode"
      :model-value="snackbar"
      @update:model-value="updateSnackbar"
    >
      {{ notice }}
      <template v-slot:actions>
        <v-btn variant="text" @click="closeSnackbar">Close</v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script lang="ts">
import { Component, Vue, Ref } from 'vue-facing-decorator'
import { useRouter, useRoute } from 'vue-router'
import Table from '@/components/Table.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import { trackModule } from '@/store/modules/tracks'
import { workModule } from '@/store/modules/works'
import { appModule } from '@/store/modules/app'
import { buildSearchFilters, buildJsonServerQuery, getISOClassDate } from '@/utils/app-util'

interface Header {
  text: string
  left?: boolean
  value: string
  sortable?: boolean
}

interface ValidForm {
  validate: () => boolean
}

@Component({
  name: 'WorkForm',
  components: {
    Table,
    ConfirmDialog
  }
})
export default class WorkForm extends Vue {
  @Ref('validForm') validForm!: ValidForm
  @Ref('validDetail') validDetail!: ValidForm

  private router = useRouter()
  private route = useRoute()

  private modalTitle = "Add Track"
  private modalText = "Select the category and track from the list"
  private addTrackModal = false
  private dialog = false
  private dialogTitle = "Track Delete Dialog"
  private dialogText = "Do you want to delete this track?"
  private workDateMenu = false
  private worklineDateMenu = false
  private worklineTimeMenu = false
  private errors: string[] = []
  private formValid = false
  private detailValid = false
  private title = ""
  private workId: number | null = null
  private worklineId: number | null = null
  private categoryId = 0
  private remarkId = 0
  private color = ""
  private selectedWorkline: any = null
  private query = ""
  private jsonQuery = ""
  private search = ""

  headers: Header[] = [
    { text: "開機日期時間", left: true, value: "worklineTime" },
    { text: "開機人員", left: true, value: "employName" },
    { text: "捻線機號", left: true, value: "twisterNO" },
    { text: "爐別", left: true, value: "furnaceName" },
    { text: "品種", left: true, value: "productName" },
    { text: "掃瞄T1個數", value: "trackT1Qty" },
    { text: "掃瞄T2個數", value: "trackT2Qty" },
    { text: "開機T1個數", value: "workT1Qty" },
    { text: "開機T2個數", value: "workT2Qty" },
    { text: "", value: "actions", sortable: false }
  ]

  searchFilter = { contain: { twisterNO: "" } }

  get employees() {
    return workModule.employees
  }

  get work() {
    return workModule.work
  }

  get workline() {
    return workModule.workline
  }

  get track() {
    return trackModule.track
  }

  get loading() {
    return appModule.loading
  }

  get mode() {
    return appModule.mode
  }

  get snackbar() {
    return appModule.snackbar
  }

  get notice() {
    return appModule.notice
  }

  get pagination() {
    return workModule.pagination
  }

  updateSnackbar(value: boolean) {
    if (!value) {
      appModule.closeNotice()
    }
  }

  save() {
    workModule.saveWork(this.work)
  }

  getWorkById() {
    const id = this.route.params.id
    if (id) {
      workModule.getWorkById(id as string)
    }
  }

  getTrack() {
    buildSearchFilters(this.searchFilter)
    this.jsonQuery = buildJsonServerQuery(this.searchFilter)
    this.query = this.searchFilter.contain.twisterNO
    if (this.query) {
      trackModule.getTrackById(this.query)
      this.query = ""
      return trackModule.track
    }
    return ""
  }

  cancel() {
    this.router.push({ name: "works" })
  }

  remove(item: any) {
    this.selectedWorkline = item
    this.dialog = true
  }

  onConfirm() {
    workModule.deleteWorkline(this.selectedWorkline)
    this.selectedWorkline = null
    this.getWorkById()
    this.dialog = false
  }

  onCancel() {
    this.selectedWorkline = null
    this.dialog = false
  }

  addTrack() {
    this.addTrackModal = true
    this.query = ""
    this.searchFilter.contain.twisterNO = ""
    this.workId = this.work.id
    trackModule.clearTracks()
    if (this.validDetail) {
      this.validDetail.validate()
    }
  }

  saveWorkline() {
    const WorkId = { workId: this.work.id }
    const addTrack = this.track[0]
    const addWorkline = this.workline
    const newWorkline = { ...WorkId, ...addTrack, ...addWorkline }
    workModule.addWorklineToWork(newWorkline)
    this.worklineId = null
    this.getWorkById()
    this.addTrackModal = false
  }

  cancelAddTrack() {
    this.addTrackModal = false
    this.query = ""
    this.searchFilter.contain.twisterNO = ""
    workModule.clearWorkline()
    trackModule.clearTracks()
  }

  closeSnackbar() {
    appModule.closeNotice()
  }

  created() {
    this.getWorkById()
    workModule.getEmployees()
  }

  mounted() {
    if (this.route.params.id) {
      this.title = "捻線開機"
    } else {
      this.title = "捻線開機(新增)"
      const toDate = getISOClassDate()
      this.work.workDate = toDate.slice(0, 10)
      if (this.validForm) {
        this.validForm.validate()
      }
    }
  }
}
</script>
