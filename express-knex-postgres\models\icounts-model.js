const db = require("../config/dbConfig.js");
require("dotenv").config();
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET ALL ICOUNTS
const find = () => {
  return db("aits_icounts")
    .select({
      id: "aits_icounts.id",
      classDate: "aits_icounts.classdate",
      shiftName: "aits_icounts.shiftname",
      employId: "aits_icounts.employid",
      groupName:"aits_icounts.groupname",
      typeName: "aits_icounts.typename",
      created: "aits_icounts.created",
      updated: "aits_icounts.updated",
      quantity: db.raw("count(aits_icountlines.icountid)")      
    })
    .leftJoin("aits_icountlines", "aits_icounts.id", "aits_icountlines.icountid")
    .where("aits_icounts.typename", "YARN")
    .where("aits_icounts.classdate", ">=", process.env.DUE_TO_QUERY_DATE)
    .groupBy("aits_icounts.id", "aits_icounts.classdate", "aits_icounts.shiftname", "aits_icounts.employid", "aits_icounts.groupname")
    .orderBy("aits_icounts.id", "desc")
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

// GET SPECIFIC ICOUNT BY ID
const findById = id => {
  return db("aits_icounts")
    .select({
      id: "aits_icounts.id",
      classDate: "aits_icounts.classdate",
      shiftName: "aits_icounts.shiftname",
      employId: "aits_icounts.employid",
      groupName: "aits_icounts.groupname",
      typeName: "aits_icounts.typename",
      created: "aits_icounts.created",
      updated: "aits_icounts.updated"
    })
    .where("aits_icounts.id", id)
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
    });
};

// ADD A ICOUNT
const addICount = icount => {
  return db.transaction(trx => {
    return db("aits_icounts")
      .insert({
        classdate: icount.classDate,
        shiftname: icount.shiftName,
        employid: icount.employId,
        groupname: icount.groupName,
        typename: icount.typeName,
        created: db.fn.now()
      }, "id")
      .then(trx.commit)
      .catch(trx.rollback);
    })  
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

// UPDATE ICOUNT
const updateICount = (id, icount) => {
  let origin,result;
  return db.transaction(trx => {
    return db("aits_icounts")
      .where("aits_icounts.id", id)
      .then(old_icount => {
        origin = old_icount;
        if (old_icount) {
          return db("aits_icounts")
          .where("aits_icounts.id", id)
          .update({
            classdate: icount.classDate,
            shiftname: icount.shiftName,
            employid: icount.employId,
            groupname: icount.groupName,
            typename: icount.typeName,
            updated: db.fn.now()
          })
          .then(trx.commit)
          .catch(trx.rollback);
        }
    })
  })
  .then(()=>{
    return db("aits_icounts")
    .where("aits_icounts.id", id)
    .then(new_icount => { 
      result = new_icount;
      infoLogger.info(`update icount content: ${JSON.stringify({origin,result})}`)
    })
  }) 
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

// REMOVE ICOUNT
const removeICount = id => {
  let result;
  return db.transaction(trx => {
    return db("aits_icounts")
      .where("aits_icounts.id", id)
      .then(icount => {
        result = icount;
        if (icount) { 
          return db("aits_icounts")
            .where("aits_icounts.id", id)
            .del()
            .then(trx.commit)
            .catch(trx.rollback);
        }
      })
    })
  .then(() => { 
    infoLogger.info(`remove icount content: ${JSON.stringify(result)}`)
  })
  .catch((err) => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

module.exports = {
  find,
  findById,
  addICount,
  updateICount,
  removeICount
};
