<template>
  <v-container>
    <v-card>
      <v-card-title>
        <span class="text-h4">🚀 Vue3編輯功能簡單測試</span>
      </v-card-title>
      
      <v-card-text>
        <v-alert type="info" class="mb-4">
          <strong>測試目標：</strong>驗證Vue3基本功能是否正常運作
        </v-alert>
        
        <!-- 基本響應式測試 -->
        <v-row>
          <v-col cols="12">
            <h3>1. 響應式數據測試</h3>
            <v-text-field
              v-model="testData.name"
              label="輸入您的姓名"
              variant="outlined"
              @input="onNameChange"
            ></v-text-field>
            <p>您輸入的姓名：<strong>{{ testData.name }}</strong></p>
            <p>字元數量：<strong>{{ nameLength }}</strong></p>
          </v-col>
        </v-row>
        
        <!-- 表單驗證測試 -->
        <v-row>
          <v-col cols="12">
            <h3>2. 表單驗證測試</h3>
            <v-form ref="formRef" v-model="formValid">
              <v-text-field
                v-model="testData.email"
                label="電子郵件"
                variant="outlined"
                :rules="emailRules"
                required
              ></v-text-field>
              
              <v-text-field
                v-model="testData.phone"
                label="電話號碼"
                variant="outlined"
                :rules="phoneRules"
                required
              ></v-text-field>
              
              <v-btn
                color="primary"
                @click="validateForm"
                :disabled="!formValid"
              >
                驗證表單
              </v-btn>
            </v-form>
          </v-col>
        </v-row>
        
        <!-- 事件處理測試 -->
        <v-row>
          <v-col cols="12">
            <h3>3. 事件處理測試</h3>
            <v-btn
              color="success"
              @click="handleClick"
              class="mr-2"
            >
              點擊測試 ({{ clickCount }})
            </v-btn>
            
            <v-btn
              color="warning"
              @click="resetData"
            >
              重置數據
            </v-btn>
          </v-col>
        </v-row>
        
        <!-- 計算屬性測試 -->
        <v-row>
          <v-col cols="12">
            <h3>4. 計算屬性測試</h3>
            <v-slider
              v-model="testData.value"
              label="數值"
              min="0"
              max="100"
              step="1"
              thumb-label
            ></v-slider>
            <p>當前數值：{{ testData.value }}</p>
            <p>數值狀態：{{ valueStatus }}</p>
            <p>是否為偶數：{{ isEven ? '是' : '否' }}</p>
          </v-col>
        </v-row>
        
        <!-- 生命週期測試 -->
        <v-row>
          <v-col cols="12">
            <h3>5. 生命週期測試</h3>
            <v-list>
              <v-list-item
                v-for="log in lifecycleLogs"
                :key="log.id"
                :title="log.message"
                :subtitle="log.timestamp"
              >
                <template v-slot:prepend>
                  <v-icon color="primary">mdi-check-circle</v-icon>
                </template>
              </v-list-item>
            </v-list>
          </v-col>
        </v-row>
        
        <!-- 測試結果 -->
        <v-row>
          <v-col cols="12">
            <h3>6. 測試結果</h3>
            <v-card variant="outlined" :color="testResultColor">
              <v-card-text>
                <h4>{{ testResultTitle }}</h4>
                <p>{{ testResultMessage }}</p>
                <v-list>
                  <v-list-item
                    v-for="result in testResults"
                    :key="result.name"
                  >
                    <template v-slot:prepend>
                      <v-icon :color="result.passed ? 'success' : 'error'">
                        {{ result.passed ? 'mdi-check' : 'mdi-close' }}
                      </v-icon>
                    </template>
                    <v-list-item-title>{{ result.name }}</v-list-item-title>
                    <v-list-item-subtitle>{{ result.message }}</v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    
    <!-- 通知訊息 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          關閉
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUpdated, nextTick } from 'vue'

// 響應式數據
const testData = ref({
  name: '',
  email: '',
  phone: '',
  value: 50
})

const formRef = ref()
const formValid = ref(false)
const clickCount = ref(0)
const lifecycleLogs = ref<Array<{id: number, message: string, timestamp: string}>>([])
const testResults = ref<Array<{name: string, passed: boolean, message: string}>>([])

const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

// 計算屬性
const nameLength = computed(() => testData.value.name.length)

const valueStatus = computed(() => {
  const value = testData.value.value
  if (value < 30) return '低'
  if (value < 70) return '中'
  return '高'
})

const isEven = computed(() => testData.value.value % 2 === 0)

const testResultColor = computed(() => {
  const passedCount = testResults.value.filter(r => r.passed).length
  const totalCount = testResults.value.length
  if (totalCount === 0) return 'grey'
  if (passedCount === totalCount) return 'success'
  if (passedCount > totalCount / 2) return 'warning'
  return 'error'
})

const testResultTitle = computed(() => {
  const passedCount = testResults.value.filter(r => r.passed).length
  const totalCount = testResults.value.length
  return `測試結果：${passedCount}/${totalCount} 通過`
})

const testResultMessage = computed(() => {
  const passedCount = testResults.value.filter(r => r.passed).length
  const totalCount = testResults.value.length
  if (totalCount === 0) return '尚未執行測試'
  if (passedCount === totalCount) return '🎉 所有測試都通過了！Vue3編輯功能基本架構運作正常。'
  return '⚠️ 有些測試未通過，請檢查相關功能。'
})

// 驗證規則
const emailRules = [
  (v: string) => !!v || '請輸入電子郵件',
  (v: string) => /.+@.+\..+/.test(v) || '請輸入有效的電子郵件格式'
]

const phoneRules = [
  (v: string) => !!v || '請輸入電話號碼',
  (v: string) => /^[\d\-\+\(\)\s]+$/.test(v) || '請輸入有效的電話號碼格式'
]

// 方法
const addLifecycleLog = (message: string) => {
  lifecycleLogs.value.push({
    id: Date.now(),
    message,
    timestamp: new Date().toLocaleTimeString()
  })
}

const showMessage = (message: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}

const onNameChange = () => {
  updateTestResult('響應式數據綁定', true, '姓名輸入正常響應')
}

const handleClick = () => {
  clickCount.value++
  updateTestResult('事件處理', true, `按鈕點擊事件正常 (${clickCount.value}次)`)
  showMessage(`按鈕被點擊了 ${clickCount.value} 次`)
}

const validateForm = () => {
  if (formRef.value) {
    const isValid = formRef.value.validate()
    updateTestResult('表單驗證', isValid, isValid ? '表單驗證通過' : '表單驗證失敗')
    showMessage(isValid ? '表單驗證通過' : '表單驗證失敗', isValid ? 'success' : 'error')
  }
}

const resetData = () => {
  testData.value = {
    name: '',
    email: '',
    phone: '',
    value: 50
  }
  clickCount.value = 0
  if (formRef.value) {
    formRef.value.resetValidation()
  }
  showMessage('數據已重置')
  updateTestResult('數據重置', true, '數據重置功能正常')
}

const updateTestResult = (name: string, passed: boolean, message: string) => {
  const existingIndex = testResults.value.findIndex(r => r.name === name)
  const result = { name, passed, message }
  
  if (existingIndex !== -1) {
    testResults.value[existingIndex] = result
  } else {
    testResults.value.push(result)
  }
}

const runInitialTests = () => {
  // 測試計算屬性
  updateTestResult('計算屬性', nameLength.value >= 0, '計算屬性正常運作')
  
  // 測試響應式數據
  updateTestResult('響應式數據', testData.value !== null, '響應式數據初始化正常')
  
  // 測試生命週期
  updateTestResult('生命週期', lifecycleLogs.value.length > 0, '生命週期鉤子正常執行')
}

// 生命週期
onMounted(() => {
  addLifecycleLog('onMounted: 組件已掛載')
  
  nextTick(() => {
    addLifecycleLog('nextTick: DOM更新完成')
    runInitialTests()
  })
})

onUpdated(() => {
  addLifecycleLog('onUpdated: 組件已更新')
})

// 初始化
addLifecycleLog('setup: 組件初始化')
</script>

<style scoped>
.v-card {
  margin-bottom: 16px;
}

.v-row {
  margin-bottom: 16px;
}

h3 {
  color: #1976d2;
  margin-bottom: 12px;
}
</style>
