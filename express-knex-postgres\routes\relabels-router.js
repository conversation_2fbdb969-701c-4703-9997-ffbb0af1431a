const router = require("express").Router();
const moment = require('moment');

const relabelsDB = require("../models/relabels-model.js");
const employeesDB = require("../models/employees-model.js");
const relabellinesDB = require("../models/relabellines-model.js");
const categoriesDB = require("../models/categories-model.js");
const remarksDB = require("../models/remarks-model.js");

// GET ALL RELABEL BY TYPE WITH PAGINATION
router.get("/NoMFD", async (req, res) => {
  try {
    const urlPath = req.originalUrl.split('/')[3].split('?')[0]; // 移除查詢參數
    const type = urlPath.toUpperCase(); 
    
    if (!type){
      return res.status(400).json({ error: "Type parameter is missing." });
    }

    // 檢查是否有分頁參數
    const hasPageParams = req.query.page || req.query.limit;
    
    if (hasPageParams) {
      // 使用分頁查詢
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const offset = (page - 1) * limit;

      // 獲取總數
      const totalCount = await relabelsDB.countByType(type);
      
      // 獲取分頁資料
      const relabels = await relabelsDB.findByTypeWithPagination(type, limit, offset);
      
      const employees = await employeesDB.findOfTW();
      
      // Map employees to relabel
      relabels.forEach(mem => {
        const employee = employees.find(info => info.employId === mem.employId);
        if (employee) {
          mem.employName = `${employee.employNO} ${employee.userName}`;
        }
      });

      // Format dates
      relabels.forEach(mem => {
        mem.classDate = moment(mem.classDate).format("YYYY-M-D");
      });

      // 計算分頁資訊
      const totalPages = Math.ceil(totalCount / limit);
      
      res.status(200).json({
        data: relabels,
        pagination: {
          page: page,
          limit: limit,
          totalItems: totalCount,
          totalPages: totalPages,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      });
    } else {
      // 使用原來的查詢（不分頁）
      const relabels = await relabelsDB.findByType(type);
      const employees = await employeesDB.findOfTW();
      
      // Map employees to relabel
      relabels.forEach(mem => {
        const employee = employees.find(info => info.employId === mem.employId);
        if (employee) {
          mem.employName = `${employee.employNO} ${employee.userName}`;
        }
      });

      // Format dates
      relabels.forEach(mem => {
        mem.classDate = moment(mem.classDate).format("YYYY-M-D");
      });

      // 為了兼容性，也返回分頁格式
      res.status(200).json({
        data: relabels,
        pagination: {
          page: 1,
          limit: relabels.length,
          totalItems: relabels.length,
          totalPages: 1,
          hasNextPage: false,
          hasPrevPage: false
        }
      });
    }
  } catch (err) {
    console.error('API Error:', err);
    res.status(500).json({ err: err });
  }
});

// GET RELABELS BY ID
router.get("/:id", async (req, res) => {
  const relabelId = req.params.id;
  try {
    const relabel = await relabelsDB.findById(relabelId);
    if (!relabel) {
      return res.status(404).json({ err: "The specified id does not exist" });
    }

    const relabellines = await relabellinesDB.findById(relabel[0].id);
    const employee = await employeesDB.findById(relabel[0].employId);
    const categories = await categoriesDB.find();
    const remarks = await remarksDB.find();
    // Map employees to relabel
    relabel.forEach(mem => {      
        mem.employName = `${employee[0].employNO} ${employee[0].userName}`;      
    });
    // Format dates
    relabel.forEach(mem => {
      mem.classDate = moment(mem.classDate).format("YYYY-M-D");
    });
    relabellines.forEach(mem => {
      mem.workDate = moment(mem.workDate).format("YYYY-M-D HH:mm");
    });
    // Map categories to relabellines
    relabellines.forEach(mem => {
      const category = categories.find(info => info.categoryId === mem.categoryId);
      if (category) {
        mem.categoryName = category.categoryName;
      }
    });
    // Map remarks to relabellines
    relabellines.forEach(mem => {
      const remark = remarks.find(info => info.remarkId === mem.remarkId);
      if (remark) {
        mem.remarkName = remark.remarkName;
      }
    });
    // Assign relabellines to respective relabel
    relabel.forEach(mem => {
      mem.relabellines = relabellines.filter(d => d.relabelId === mem.id);
    });
    res.status(200).json(relabel);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// INSERT RELABEL INTO DB
router.post("/", async (req, res) => {
  const newRelabel = req.body;
  if (!newRelabel.shiftName) {
    res.status(404).json({ err:"You are missing information" });
  } else {
    try {
      const relabel = await relabelsDB.addRelabel(newRelabel);
      res.status(201).json(relabel);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

// UPDATE RELABEL INTO DB
router.put("/:id", async (req, res) => {
  const relabelId = req.params.id;
  const newChanges = req.body;
  if (!newChanges.shiftName) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const addChanges = await relabelsDB.updateRelabel(relabelId, newChanges);
      res.status(200).json(addChanges);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

// REMOVE RELABEL INTO DB
router.delete("/:id", async (req, res) => {
  const relabelId = req.params.id;
  try {
    const deleting = await relabelsDB.removeRelabel(relabelId);
    res.status(204).json(deleting);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;
