<template>
  <v-container>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <h2>測試頁面</h2>
          </v-card-title>
          <v-card-text>
            <p>這是一個測試頁面，用來驗證導航功能是否正常工作。</p>
            <p>當前路由：{{ $route.name }}</p>
            <p>當前時間：{{ currentTime }}</p>
          </v-card-text>
          <v-card-actions>
            <v-btn color="primary" @click="goBack">
              返回儀表板
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const currentTime = ref('')

let timer: NodeJS.Timeout

function updateTime() {
  currentTime.value = new Date().toLocaleString()
}

function goBack() {
  router.push({ name: 'dashboard' })
}

onMounted(() => {
  updateTime()
  timer = setInterval(updateTime, 1000)
  console.log('TestPage mounted successfully')
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>
