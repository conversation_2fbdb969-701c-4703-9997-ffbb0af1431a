<template>
  <v-text-field
    :model-value="modelValue"
    :label="label"
    :placeholder="placeholder"
    :type="type"
    :variant="responsiveVariant"
    :density="responsiveDensity"
    :disabled="disabled"
    :readonly="readonly"
    :clearable="clearable"
    :counter="counter"
    :rules="rules"
    :error-messages="errorMessages"
    :hint="hint"
    :persistent-hint="persistentHint"
    :prepend-icon="prependIcon"
    :append-icon="appendIcon"
    :prepend-inner-icon="prependInnerIcon"
    :append-inner-icon="appendInnerIcon"
    :class="textFieldClass"
    :style="textFieldStyle"
    @update:model-value="handleInput"
    @focus="handleFocus"
    @blur="handleBlur"
    @keyup.enter="handleEnter"
    ref="textFieldRef"
  >
    <template v-if="$slots.prepend" v-slot:prepend>
      <slot name="prepend" />
    </template>
    
    <template v-if="$slots.append" v-slot:append>
      <slot name="append" />
    </template>
    
    <template v-if="$slots['prepend-inner']" v-slot:prepend-inner>
      <slot name="prepend-inner" />
    </template>
    
    <template v-if="$slots['append-inner']" v-slot:append-inner>
      <slot name="append-inner" />
    </template>
  </v-text-field>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAndroidLayout } from '@/composables/useAndroidLayout'

interface Props {
  // v-model
  modelValue?: string | number
  
  // 基本屬性
  label?: string
  placeholder?: string
  type?: string
  disabled?: boolean
  readonly?: boolean
  clearable?: boolean
  counter?: boolean | number | string
  
  // 驗證
  rules?: any[]
  errorMessages?: string | string[]
  hint?: string
  persistentHint?: boolean
  
  // 圖標
  prependIcon?: string
  appendIcon?: string
  prependInnerIcon?: string
  appendInnerIcon?: string
  
  // 響應式屬性覆蓋
  variant?: 'filled' | 'outlined' | 'underlined' | 'solo' | 'solo-inverted' | 'solo-filled'
  density?: 'default' | 'comfortable' | 'compact'
  
  // Android特定屬性
  autoFocus?: boolean
  forceDesktopStyle?: boolean
  preventZoom?: boolean
  customMinHeight?: number
}

interface Emits {
  (e: 'update:modelValue', value: string | number): void
  (e: 'focus', event: FocusEvent): void
  (e: 'blur', event: FocusEvent): void
  (e: 'enter', event: KeyboardEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  disabled: false,
  readonly: false,
  clearable: false,
  autoFocus: false,
  forceDesktopStyle: false,
  preventZoom: true
})

const emit = defineEmits<Emits>()

// 使用Android Layout composable
const {
  isMobile,
  textFieldConfig,
  textFieldClass: androidTextFieldClass,
  handleAutoFocus
} = useAndroidLayout()

// 組件引用
const textFieldRef = ref()

// 計算響應式variant
const responsiveVariant = computed(() => {
  if (props.forceDesktopStyle) {
    return props.variant || 'outlined'
  }
  
  return props.variant || textFieldConfig.value.variant
})

// 計算響應式density
const responsiveDensity = computed(() => {
  if (props.forceDesktopStyle) {
    return props.density || 'default'
  }
  
  return props.density || textFieldConfig.value.density
})

// 計算文字輸入框類別
const textFieldClass = computed(() => {
  const classes = [androidTextFieldClass.value]
  
  // 添加Android優化類別
  if (isMobile.value) {
    classes.push('android-touch-textfield')
    
    if (props.preventZoom) {
      classes.push('android-no-zoom')
    }
  }
  
  return classes.filter(Boolean).join(' ')
})

// 計算文字輸入框樣式
const textFieldStyle = computed(() => {
  const style: Record<string, string> = {}
  
  // 設定最小高度
  const minHeight = props.customMinHeight || textFieldConfig.value.minHeight
  if (minHeight) {
    style.minHeight = `${minHeight}px`
  }
  
  return style
})

// 事件處理
const handleInput = (value: string | number) => {
  emit('update:modelValue', value)
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

const handleEnter = (event: KeyboardEvent) => {
  emit('enter', event)
}

// 生命週期
onMounted(() => {
  if (props.autoFocus) {
    handleAutoFocus(textFieldRef)
  }
})

// 暴露方法
defineExpose({
  focus: () => textFieldRef.value?.focus(),
  blur: () => textFieldRef.value?.blur(),
  select: () => textFieldRef.value?.select()
})
</script>

<style scoped>
/* Android觸摸文字輸入框優化 */
.android-touch-textfield :deep(.v-field__input) {
  min-height: 48px !important;
  padding: 12px 16px !important;
}

/* 防止iOS縮放 */
.android-no-zoom :deep(.v-field__input) {
  font-size: 16px !important;
}

/* 觸摸區域優化 */
.android-touch-textfield :deep(.v-field) {
  min-height: 48px !important;
}

/* 標籤動畫優化 */
.android-touch-textfield :deep(.v-label) {
  transition: all 0.2s ease !important;
}

/* 聚焦狀態優化 */
.android-touch-textfield :deep(.v-field--focused) {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* 錯誤狀態優化 */
.android-touch-textfield :deep(.v-field--error) {
  animation: shake 0.3s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* 清除按鈕優化 */
.android-touch-textfield :deep(.v-field__clearable) {
  min-width: 44px !important;
  min-height: 44px !important;
}

/* 圖標優化 */
.android-touch-textfield :deep(.v-field__prepend-inner),
.android-touch-textfield :deep(.v-field__append-inner) {
  padding: 8px !important;
}

/* 計數器優化 */
.android-touch-textfield :deep(.v-counter) {
  font-size: 12px !important;
  opacity: 0.7 !important;
}

/* 提示文字優化 */
.android-touch-textfield :deep(.v-messages) {
  font-size: 12px !important;
  line-height: 1.4 !important;
  padding-top: 4px !important;
}

/* 高對比度模式支援 */
@media (prefers-contrast: high) {
  .android-touch-textfield :deep(.v-field) {
    border: 2px solid currentColor !important;
  }
}

/* 減少動畫模式支援 */
@media (prefers-reduced-motion: reduce) {
  .android-touch-textfield :deep(.v-label),
  .android-touch-textfield :deep(.v-field) {
    transition: none !important;
  }
  
  .android-touch-textfield :deep(.v-field--error) {
    animation: none !important;
  }
}

/* 深色模式優化 */
@media (prefers-color-scheme: dark) {
  .android-touch-textfield :deep(.v-field--focused) {
    box-shadow: 0 2px 4px rgba(255, 255, 255, 0.1) !important;
  }
}
</style>
