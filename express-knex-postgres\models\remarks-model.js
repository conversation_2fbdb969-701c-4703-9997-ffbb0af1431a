const db = require("../config/dbConfig.js");
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET ALL REMARKS
const find = () => {
  return db("m_attributevalue")
    .select({
      remarkId: "m_attributevalue.m_attributevalue_id",
      remarkNO: "m_attributevalue.value",
      remarkName: "m_attributevalue.name"
    })
    .whereIn('m_attribute_id', ['1000018', '1000019', '1000020', '1000021', '1000022', '1000023', '1000024', '1000027', '1000028', '1000029', '1000031', '1000042', '1000050'])
    .orderBy("m_attributevalue.value","asc")
    
};

// GET SPECIFIC REMARK BY ID
const findById = id => {
  return db("aits_remarks").where("id", id);

  //SQL RAW METHOD
  // return db.raw(`SELECT * FROM remarks
  //                  WHERE id = ${id}`);
};

// ADD A REMARK
const addRemark = remark => {
  return db("aits_remarks").insert(remark, "id");
};

// UPDATE REMARK
const updateRemark = (id, post) => {
  return db("aits_remarks")
    .where("id", id)
    .update(post);
};

// REMOVE REMARK
const removeRemark = id => {
  return db("aits_remarks")
    .where("id", id)
    .del();
};

module.exports = {
  find,
  findById,
  addRemark,
  updateRemark,
  removeRemark
};
