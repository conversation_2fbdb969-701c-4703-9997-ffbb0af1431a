#!/usr/bin/env node

/**
 * Vue3編輯功能測試腳本
 * 
 * 此腳本用於測試Vue3編輯功能的基本組件是否正常工作
 */

const fs = require('fs')
const path = require('path')

console.log('🚀 開始測試Vue3編輯功能...\n')

// 測試項目
const tests = [
  {
    name: '檢查組合式函數文件',
    test: () => checkComposableFiles()
  },
  {
    name: '檢查Vue3表單模板',
    test: () => checkVue3Templates()
  },
  {
    name: '檢查文件語法',
    test: () => checkFileSyntax()
  },
  {
    name: '檢查導入依賴',
    test: () => checkImportDependencies()
  }
]

// 需要檢查的組合式函數文件
const composableFiles = [
  'src/composables/useFormEdit.ts',
  'src/composables/useFormValidation.ts',
  'src/composables/useReactiveData.ts',
  'src/composables/useLifecycle.ts',
  'src/composables/useMasterDetail.ts'
]

// 需要檢查的Vue3模板文件
const vue3Templates = [
  'src/templates/FormTemplate.vue',
  'src/pages/InspectFormVue3.vue',
  'src/tests/EditFunctionTest.vue'
]

function checkComposableFiles() {
  const results = []
  
  for (const file of composableFiles) {
    const filePath = path.join(__dirname, file)
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8')
      
      // 檢查基本結構
      const hasExport = content.includes('export')
      const hasImport = content.includes('import')
      const hasFunction = content.includes('function') || content.includes('=>')
      
      if (hasExport && hasFunction) {
        results.push(`✅ ${file} - 結構正確`)
      } else {
        results.push(`❌ ${file} - 結構不完整`)
      }
    } else {
      results.push(`❌ ${file} - 文件不存在`)
    }
  }
  
  return results
}

function checkVue3Templates() {
  const results = []
  
  for (const file of vue3Templates) {
    const filePath = path.join(__dirname, file)
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8')
      
      // 檢查Vue3特徵
      const hasScriptSetup = content.includes('<script setup')
      const hasCompositionAPI = content.includes('ref(') || content.includes('reactive(')
      const hasTemplate = content.includes('<template>')
      
      if (hasTemplate && (hasScriptSetup || hasCompositionAPI)) {
        results.push(`✅ ${file} - Vue3結構正確`)
      } else {
        results.push(`❌ ${file} - 不是有效的Vue3組件`)
      }
    } else {
      results.push(`❌ ${file} - 文件不存在`)
    }
  }
  
  return results
}

function checkFileSyntax() {
  const results = []
  const allFiles = [...composableFiles, ...vue3Templates]
  
  for (const file of allFiles) {
    const filePath = path.join(__dirname, file)
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8')
      
      // 基本語法檢查
      const syntaxIssues = []
      
      // 檢查括號匹配
      const openBraces = (content.match(/\{/g) || []).length
      const closeBraces = (content.match(/\}/g) || []).length
      if (openBraces !== closeBraces) {
        syntaxIssues.push('括號不匹配')
      }
      
      // 檢查引號匹配（簡單檢查）
      const singleQuotes = (content.match(/'/g) || []).length
      const doubleQuotes = (content.match(/"/g) || []).length
      if (singleQuotes % 2 !== 0) {
        syntaxIssues.push('單引號不匹配')
      }
      if (doubleQuotes % 2 !== 0) {
        syntaxIssues.push('雙引號不匹配')
      }
      
      if (syntaxIssues.length === 0) {
        results.push(`✅ ${file} - 語法檢查通過`)
      } else {
        results.push(`❌ ${file} - 語法問題: ${syntaxIssues.join(', ')}`)
      }
    }
  }
  
  return results
}

function checkImportDependencies() {
  const results = []
  
  // 檢查InspectFormVue3.vue的導入
  const inspectFormPath = path.join(__dirname, 'src/pages/InspectFormVue3.vue')
  if (fs.existsSync(inspectFormPath)) {
    const content = fs.readFileSync(inspectFormPath, 'utf8')
    
    const requiredImports = [
      'useFormValidation',
      'useReactiveData',
      'useLifecycle',
      'useMasterDetail'
    ]
    
    const missingImports = requiredImports.filter(imp => !content.includes(imp))
    
    if (missingImports.length === 0) {
      results.push('✅ InspectFormVue3.vue - 所有必要的導入都存在')
    } else {
      results.push(`❌ InspectFormVue3.vue - 缺少導入: ${missingImports.join(', ')}`)
    }
  } else {
    results.push('❌ InspectFormVue3.vue - 文件不存在')
  }
  
  return results
}

// 執行測試
async function runTests() {
  let totalTests = 0
  let passedTests = 0
  
  for (const test of tests) {
    console.log(`📋 ${test.name}`)
    console.log('─'.repeat(50))
    
    try {
      const results = test.test()
      results.forEach(result => {
        console.log(`  ${result}`)
        totalTests++
        if (result.startsWith('✅')) {
          passedTests++
        }
      })
    } catch (error) {
      console.log(`  ❌ 測試執行失敗: ${error.message}`)
      totalTests++
    }
    
    console.log('')
  }
  
  // 總結
  console.log('📊 測試總結')
  console.log('='.repeat(50))
  console.log(`總測試項目: ${totalTests}`)
  console.log(`通過: ${passedTests}`)
  console.log(`失敗: ${totalTests - passedTests}`)
  console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`)
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有測試都通過了！Vue3編輯功能基礎架構已就緒。')
  } else {
    console.log('\n⚠️  有些測試失敗了，請檢查上述問題。')
  }
  
  // 提供下一步建議
  console.log('\n📝 下一步建議:')
  console.log('1. 在瀏覽器中測試InspectFormVue3.vue組件')
  console.log('2. 測試表單驗證功能')
  console.log('3. 測試主檔與明細檔的CRUD操作')
  console.log('4. 測試鍵盤快捷鍵和事件處理')
  console.log('5. 進行完整的用戶流程測試')
}

// 檢查Node.js版本
const nodeVersion = process.version
console.log(`Node.js版本: ${nodeVersion}`)

if (parseInt(nodeVersion.slice(1)) < 14) {
  console.log('⚠️  建議使用Node.js 14或更高版本')
}

console.log('')

// 執行測試
runTests().catch(error => {
  console.error('測試執行失敗:', error)
  process.exit(1)
})
