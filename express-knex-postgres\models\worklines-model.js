const db = require("../config/dbConfig.js");
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET ALL WORKLINES
const find = () => {
  return db("aits_worklines")
    .select({
    id: "aits_worklines.id",
    workId: "aits_worklines.workid",
    employId: "aits_worklines.employid",
    worklineTime: "aits_worklines.worklinetime",
    twisterNO:"aits_worklines.twisterno",
    furnaceName: "aits_worklines.furnacename",
    productName: "aits_worklines.productname",
    trackTime: "aits_worklines.tracktime",
    trackT1Qty: "aits_worklines.trackt1qty",
    trackT2Qty: "aits_worklines.trackt2qty",
    workT1Qty: "aits_worklines.workt1qty",
    workT2Qty: "aits_worklines.workt2qty", 
    
  })
};

// GET SPECIFIC WORKLINE BY ID
const findById = id => {
  return db("aits_worklines")
    .select({
    id: "aits_worklines.id",
    workId: "aits_worklines.workid",
    employId: "aits_worklines.employid",
    worklineTime: "aits_worklines.worklinetime", 
    twisterNO:"aits_worklines.twisterno",
    furnaceName: "aits_worklines.furnacename",
    productName: "aits_worklines.productname",
    trackTime: "aits_worklines.tracktime",
    trackT1Qty: "aits_worklines.trackt1qty",
    trackT2Qty: "aits_worklines.trackt2qty",
    workT1Qty: "aits_worklines.workt1qty",
    workT2Qty: "aits_worklines.workt2qty",
    
    }).where("aits_worklines.workid", id);
  
    //SQL RAW METHOD
  // return db.raw(`SELECT * FROM worklines
  //                  WHERE id = ${id}`);
};

// GET SPECIFIC WORKLINE BY CODE
const findByCode = id => {
  return db("aits_worklines")
    .select({
    id: "aits_worklines.id",
    workId: "aits_worklines.workid",
    employId: "aits_worklines.employid",
    worklineTime: "aits_worklines.worklinetime",
    twisterNO:"aits_worklines.twisterno",
    furnaceName: "aits_worklines.furnacename",
    productName: "aits_worklines.productname",
    trackTime: "aits_worklines.tracktime",
    trackT1Qty: "aits_worklines.trackt1qty",
    trackT2Qty: "aits_worklines.trackt2qty",
    workT1Qty: "aits_worklines.workt1qty",
    workT2Qty: "aits_worklines.workt2qty",
    
    }).where("aits_worklines.twisterno", id);
  
    //SQL RAW METHOD
  // return db.raw(`SELECT * FROM worklines
  //                  WHERE id = ${id}`);
};

// ADD A WORKLINE
const addWorkline = workline => {
  return db("aits_worklines").
    insert({
    workid: workline.workId,
    employid: workline.employId,
    worklinetime: workline.worklineTime, 
    twisterno: workline.twisterNO,
    furnacename: workline.furnaceName,
    productname: workline.productName,
    tracktime: workline.trackTime,
    trackt1qty: workline.trackT1Qty,
    trackt2qty: workline.trackT2Qty,
    workt1qty: workline.workT1Qty,
    workt2qty: workline.workT2Qty
    }, 'id');
};

// UPDATE WORKLINE
const updateWorkline = (id, post) => {
  return db("aits_worklines")
    .where("id", id)
    .update(post);
};

// REMOVE WORKLINE
const removeWorkline = id => {
  return db("aits_worklines")
    .where("id", id)
    .del();
};

module.exports = {
  find,
  findById,
  findByCode,
  addWorkline,
  updateWorkline,
  removeWorkline
};
