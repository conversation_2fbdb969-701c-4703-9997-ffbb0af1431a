import { vi } from 'vitest'
import { config } from '@vue/test-utils'

// Mock global objects
global.console = {
  ...console,
  // 在測試中靜音某些日誌
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn()
}

// Mock window.confirm and window.alert
global.confirm = vi.fn(() => true)
global.alert = vi.fn()

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn()
}
global.localStorage = localStorageMock

// Mock sessionStorage
global.sessionStorage = localStorageMock

// Mock fetch
global.fetch = vi.fn()

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  unobserve: vi.fn()
}))

// Mock ResizeObserver
global.ResizeObserver = vi.fn(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  unobserve: vi.fn()
}))

// Vue Test Utils 全局配置
config.global.mocks = {
  $t: (key: string) => key, // Mock i18n
  $route: {
    params: {},
    query: {},
    path: '/',
    name: 'home'
  },
  $router: {
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn(),
    forward: vi.fn()
  }
}

// Mock Vue Router composables
vi.mock('vue-router', () => ({
  useRoute: () => ({
    params: {},
    query: {},
    path: '/',
    name: 'home'
  }),
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn(),
    forward: vi.fn()
  }),
  createRouter: vi.fn(),
  createWebHistory: vi.fn()
}))

// Mock Vuetify components
config.global.stubs = {
  'v-app': true,
  'v-main': true,
  'v-container': true,
  'v-row': true,
  'v-col': true,
  'v-card': true,
  'v-card-title': true,
  'v-card-text': true,
  'v-card-actions': true,
  'v-btn': true,
  'v-text-field': true,
  'v-select': true,
  'v-radio-group': true,
  'v-radio': true,
  'v-data-table': true,
  'v-dialog': true,
  'v-form': true,
  'v-snackbar': true,
  'v-progress-linear': true,
  'v-alert': true,
  'v-spacer': true,
  'v-icon': true,
  'v-chip': true,
  'v-pagination': true,
  'v-expand-transition': true
}

// 設置測試環境變量
process.env.NODE_ENV = 'test'
