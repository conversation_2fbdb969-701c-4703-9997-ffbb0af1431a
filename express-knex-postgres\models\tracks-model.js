const db = require("../config/dbConfig.js");
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET ALL TRACKS
const find = () => {
  return db("m_twqrcode")
    .select({
    twisterNO:"m_twqrcode.mach_tmis_no",
    productName: "rv_product_class.name",
    trackTime: "m_twqrcode.created",
    gradeName: "rv_product_class.prod_class"
    })
  .count({ trackQty: 'm_twqrcode.mach_tmis_no' })
	.innerJoin('m_twqrcodeline', 'm_twqrcode.m_twqrcode_id', 'm_twqrcodeline.m_twqrcode_id')
  .innerJoin('rv_product_class', 'm_twqrcodeline.m_product_id', 'rv_product_class.m_product_id')
  .groupBy('m_twqrcode.m_twqrcode_id', 'm_twqrcode.mach_tmis_no', 'm_twqrcodeline.m_product_id', 'rv_product_class.name', 'rv_product_class.prod_class')

};

// GET SPECIFIC TRACK BY ID
const findById = id => {
  return db("m_twqrcode")
    .select({
    twisterNO:"m_twqrcode.mach_tmis_no",
    productName: "rv_product_class.name",
    trackTime: "m_twqrcode.created",
    gradeName: "rv_product_class.prod_class"
    })
  .count({ trackQty: 'm_twqrcode.mach_tmis_no' })
	.innerJoin('m_twqrcodeline', 'm_twqrcode.m_twqrcode_id', 'm_twqrcodeline.m_twqrcode_id')
  .innerJoin('rv_product_class', 'm_twqrcodeline.m_product_id', 'rv_product_class.m_product_id')
  .groupBy('m_twqrcode.m_twqrcode_id', 'm_twqrcode.mach_tmis_no', 'm_twqrcodeline.m_product_id', 'rv_product_class.name', 'rv_product_class.prod_class')
  .where("aits_tracks.id", id);

    //SQL RAW METHOD
  // return db.raw(`SELECT * FROM tracks
  //                  WHERE id = ${id}`);
};

// GET SPECIFIC TRACK T1 BY CODE
const findT1ByCode = id => {
  return db("m_twqrcode")
    .select({
    twisterNO:"m_twqrcode.mach_tmis_no",
    productName: "rv_product_class.name",
    trackTime: "m_twqrcode.created"
    })
  .count({ trackT1Qty: 'm_twqrcode.mach_tmis_no' })
	.innerJoin('m_twqrcodeline', 'm_twqrcode.m_twqrcode_id', 'm_twqrcodeline.m_twqrcode_id')
  .innerJoin('rv_product_class', 'm_twqrcodeline.m_product_id', 'rv_product_class.m_product_id')
  .groupBy('m_twqrcode.m_twqrcode_id', 'm_twqrcode.mach_tmis_no', 'm_twqrcodeline.m_product_id', 'rv_product_class.name', 'rv_product_class.prod_class')
  .orderBy('m_twqrcode.class_date', 'm_twqrcode_id', 'm_twqrcode.mach_tmis_no')
  .where("m_twqrcode.mach_tmis_no", id)
  .where("rv_product_class.prod_class", "T1")
  .where("m_twqrcode.created","<","2021-09-04")

    //SQL RAW METHOD
  // return db.raw(`SELECT * FROM tracks
  //                  WHERE id = ${id}`);
};

// GET SPECIFIC TRACK T2 BY CODE
const findT2ByCode = id => {
  return db("m_twqrcode")
    .select({
    twisterNO:"m_twqrcode.mach_tmis_no",
    productName: "rv_product_class.name",
    trackTime: "m_twqrcode.created",
    gradeName: "rv_product_class.prod_class"
    })
  .count({ trackT2Qty: 'm_twqrcode.mach_tmis_no' })
	.innerJoin('m_twqrcodeline', 'm_twqrcode.m_twqrcode_id', 'm_twqrcodeline.m_twqrcode_id')
  .innerJoin('rv_product_class', 'm_twqrcodeline.m_product_id', 'rv_product_class.m_product_id')
  .groupBy('m_twqrcode.m_twqrcode_id', 'm_twqrcode.mach_tmis_no', 'm_twqrcodeline.m_product_id', 'rv_product_class.name', 'rv_product_class.prod_class')
  .orderBy('m_twqrcode.class_date','m_twqrcode_id','m_twqrcode.mach_tmis_no')  
  .where("m_twqrcode.mach_tmis_no", id)
  .where("rv_product_class.prod_class","T2")

    //SQL RAW METHOD
  // return db.raw(`SELECT * FROM tracks
  //                  WHERE id = ${id}`);
};

// GET SPECIFIC TW TRACK T1 BY CODE
const findT1ByTWCode = id => {
  return db("m_twqrcode")
    .select({
    twisterNO:"m_twqrcode.mach_tmis_no",
    productName: "rv_product_class.name",
    trackTime: "m_twqrcode.created",
    gradeName: "rv_product_class.prod_class"
    })
  .count({ trackT1Qty: 'm_twqrcode.mach_tmis_no' })
	.innerJoin('m_twqrcodeline', 'm_twqrcode.m_twqrcode_id', 'm_twqrcodeline.m_twqrcode_id')
  .innerJoin('rv_product_class', 'm_twqrcodeline.m_product_id', 'rv_product_class.m_product_id')
  .groupBy('m_twqrcode.m_twqrcode_id', 'm_twqrcode.mach_tmis_no', 'm_twqrcodeline.m_product_id', 'rv_product_class.name', 'rv_product_class.prod_class')
  .orderBy('m_twqrcode.class_date', 'm_twqrcode_id', 'm_twqrcode.mach_tmis_no')
  .where("m_twqrcode.m_twqrcode_id", id)
  .where("rv_product_class.prod_class", "T1")

    //SQL RAW METHOD
  // return db.raw(`SELECT * FROM tracks
  //                  WHERE id = ${id}`);
};
// GET SPECIFIC TW TRACK T2 BY CODE
const findT2ByTWCode = id => {
  return db("m_twqrcode")
    .select({
      twisterNO: "m_twqrcode.mach_tmis_no",
      productName: "rv_product_class.name",
      trackTime: "m_twqrcode.created",
      gradeName: "rv_product_class.prod_class"
    })
    .count({ trackT2Qty: 'm_twqrcode.mach_tmis_no' })
    .innerJoin('m_twqrcodeline', 'm_twqrcode.m_twqrcode_id', 'm_twqrcodeline.m_twqrcode_id')
    .innerJoin('rv_product_class', 'm_twqrcodeline.m_product_id', 'rv_product_class.m_product_id')
    .groupBy('m_twqrcode.m_twqrcode_id', 'm_twqrcode.mach_tmis_no', 'm_twqrcodeline.m_product_id', 'rv_product_class.name', 'rv_product_class.prod_class')
    .orderBy('m_twqrcode.class_date', 'm_twqrcode_id', 'm_twqrcode.mach_tmis_no')
    .where("m_twqrcode.m_twqrcode_id",id)
    .where("rv_product_class.prod_class", "T2")

  //SQL RAW METHOD
  // return db.raw(`SELECT * FROM tracks
  //                  WHERE id = ${id}`);
};
// ADD A TRACK
const addTrack = track => {
  return db("aits_tracks").insert(track, "id");
};

// UPDATE TRACK
const updateTrack = (id, post) => {
  return db("aits_tracks")
    .where("id", id)
    .update(post);
};

// REMOVE TRACK
const removeTrack = id => {
  return db("aits_tracks")
    .where("id", id)
    .del();
};

module.exports = {
  find,
  findById,
  findT1ByCode,
  findT2ByCode,
  findT1ByTWCode,
  findT2ByTWCode,
  addTrack,
  updateTrack,
  removeTrack
};
