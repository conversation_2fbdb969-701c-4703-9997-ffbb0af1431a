#!/usr/bin/env node

/**
 * 快速測試腳本 - 跳過TypeScript編譯，直接啟動開發服務器
 */

const { spawn } = require('child_process')
const { platform } = require('os')

console.log('🚀 快速啟動Vue3編輯功能測試...\n')

console.log('⚠️  注意: 此腳本跳過TypeScript編譯檢查，直接啟動開發服務器')
console.log('如果遇到運行時錯誤，請檢查瀏覽器Console\n')

// 顯示測試URL
console.log('🌐 測試URL列表:')
console.log('─'.repeat(60))
console.log('主測試頁面:     http://localhost:3000/vue3-test')
console.log('新增品檢記錄:   http://localhost:3000/test-vue3/inspects/new')
console.log('編輯品檢記錄:   http://localhost:3000/test-vue3/inspect/1')
console.log('功能測試頁面:   http://localhost:3000/test-vue3/functions')
console.log('')

// 顯示測試重點
console.log('🎯 重點測試項目:')
console.log('─'.repeat(60))
console.log('1. 頁面是否能正常載入')
console.log('2. Vue3組件是否正確渲染')
console.log('3. 表單基本功能是否運作')
console.log('4. 響應式數據是否正常')
console.log('5. 事件處理是否正確')
console.log('')

console.log('🔧 如果遇到問題:')
console.log('─'.repeat(60))
console.log('- 檢查瀏覽器Console的錯誤訊息')
console.log('- 檢查Network標籤的請求狀態')
console.log('- 確認後端API服務是否啟動')
console.log('- 檢查Vue DevTools擴展')
console.log('')

const isWindows = platform() === 'win32'
const npmCommand = isWindows ? 'npm.cmd' : 'npm'

// 啟動Vite開發服務器，跳過TypeScript檢查
console.log('🎯 正在啟動開發服務器 (跳過TypeScript檢查)...')

const devServer = spawn(npmCommand, ['run', 'dev', '--', '--force'], {
  stdio: 'inherit',
  shell: true,
  cwd: __dirname,
  env: {
    ...process.env,
    VITE_SKIP_TS_CHECK: 'true'
  }
})

// 等待服務器啟動後打開瀏覽器
setTimeout(() => {
  console.log('\n🌐 正在打開瀏覽器...')
  
  const urls = [
    'http://localhost:3000/vue3-test',
    'http://localhost:5173/vue3-test',
    'http://localhost:8080/vue3-test'
  ]
  
  const openBrowser = (url) => {
    const start = isWindows ? 'start' : platform() === 'darwin' ? 'open' : 'xdg-open'
    const command = isWindows ? `${start} ${url}` : `${start} "${url}"`
    
    require('child_process').exec(command, (error) => {
      if (error) {
        console.log(`❌ 無法自動打開瀏覽器: ${error.message}`)
        console.log(`請手動訪問: ${url}`)
      } else {
        console.log(`✅ 已打開瀏覽器: ${url}`)
      }
    })
  }
  
  openBrowser(urls[0])
  
  console.log('\n📋 測試檢查清單:')
  console.log('─'.repeat(60))
  console.log('□ 主測試頁面載入成功')
  console.log('□ Vue3組件正確顯示')
  console.log('□ 表單欄位可以輸入')
  console.log('□ 按鈕點擊有響應')
  console.log('□ 驗證訊息正確顯示')
  console.log('□ 數據綁定正常運作')
  console.log('□ 無JavaScript錯誤')
  console.log('')
  
  console.log('🎉 如果上述項目都正常，表示Vue3編輯功能基本架構運作正常！')
  console.log('')
  
}, 5000)

// 處理進程退出
process.on('SIGINT', () => {
  console.log('\n\n👋 正在關閉開發服務器...')
  devServer.kill('SIGINT')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n\n👋 正在關閉開發服務器...')
  devServer.kill('SIGTERM')
  process.exit(0)
})

devServer.on('close', (code) => {
  console.log(`\n開發服務器已關閉，退出代碼: ${code}`)
  process.exit(code)
})

devServer.on('error', (error) => {
  console.error(`❌ 啟動開發服務器失敗: ${error.message}`)
  console.log('\n請嘗試手動執行:')
  console.log('npm run dev')
  console.log('\n然後在瀏覽器中訪問: http://localhost:3000/vue3-test')
  process.exit(1)
})
