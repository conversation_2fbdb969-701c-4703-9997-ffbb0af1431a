const router = require("express").Router();
const moment = require('moment');

const worksDB = require("../models/works-model.js");
const employeesDB = require("../models/employees-model.js");
const worklinesDB = require("../models/worklines-model.js");

// SEARCH OR GET ALL WORKS
router.get("/", async (req, res) => {
  try {
    let works;
    // Check if there are any search parameters
    if (Object.keys(req.query).length > 0) {
      works = await worksDB.search(req.query);
    } else {
      works = await worksDB.find();
    }

    const worklines = await worklinesDB.find();
    const employees = await employeesDB.findOfTW();
    
    works.map(mem => {
      mem.workDate = moment(mem.workDate).format("YYYY-M-D");
    });
    
    worklines.map(mem => {
      return employees.map(info => {
        if (info.employId === mem.employId) {
          mem.employName = info.employNO+'  '+info.userName;
          return mem;
        }
      })
    });
    
    worklines.map(mem => {
      mem.worklineTime = moment(mem.worklineTime).format("YYYY-M-D HH:mm");
      return mem;
    });
    
    works.map((mem, index) => {
      const workId = works[index].id;
      mem["worklines"] = worklines.filter(d => {
        const worklineId = d.workId;
        if (worklineId === workId) {            
          return d;
        }
      });  
      return mem;
    });

    res.status(200).json(works);
  } catch (err) {
    console.error('Error in GET /works:', err);
    res.status(500).json({ err: err.message || err });
  }
});

// GET WORK BY ID
router.get("/:id", async (req, res) => {
  const workId = req.params.id;
  try {
    const work = await worksDB.findById(workId);
    if (!work) {
      res.status(404).json({ err: "The specified id does not exist" });
    } else {
      const worklines = await worklinesDB.find(work[0].id);
      const employees = await employeesDB.findOfTW();
      
      work.map(mem => {
        mem.workDate = moment(mem.workDate).format("YYYY-M-D");
      });
      
      worklines.map(mem => {
        return employees.map(info => {
          if (info.employId === mem.employId) {
            mem.employName = info.employNO+'  '+info.userName;
            return mem;
          }
        })
      });
      
      worklines.map(mem => {
        mem.worklineTime = moment(mem.worklineTime).format("YYYY-M-D HH:mm");
        return mem;
      }); 
      
      work.map((mem, index) => {
        const workId = work[index].id;
        mem["worklines"] = worklines.filter(d => {
          const worklineId = d.workId;
          if (worklineId === workId) {            
            return d;
          }
        });  
        return mem;
      });
      
      res.status(200).json(work);
    }
  } catch (err) {
    console.error('Error in GET /works/:id:', err);
    res.status(500).json({ err: err.message });
  }
});

// INSERT WORK INTO DB
router.post("/", async (req, res) => {
  const newWork = req.body;
  if (!newWork.workDate) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const work = await worksDB.addWork(newWork);
      res.status(201).json(work);
    } catch (err) {
      console.error('Error in POST /works:', err);
      res.status(500).json({ err: err.message });
    }
  }
});

// UPDATE WORK
router.put("/:id", async (req, res) => {
  const workId = req.params.id;
  const newChanges = req.body;
  if (!newChanges.firstname) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const addChanges = await worksDB.updateWork(workId, newChanges);
      res.status(200).json(addChanges);
    } catch (err) {
      console.error('Error in PUT /works/:id:', err);
      res.status(500).json({ err: err.message });
    }
  }
});

// DELETE WORK
router.delete("/:id", async (req, res) => {
  const workId = req.params.id;
  try {
    const deleting = await worksDB.removeWork(workId);
    res.status(204).json(deleting);
  } catch (err) {
    console.error('Error in DELETE /works/:id:', err);
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;
