export interface User {
  id?: number
  username: string
  password: string
}

export interface UserInfo {
  id: number
  username: string
  email: string
  roles: string[]
  firstname: string
  lastname: string
}

export interface Entity {
  id: number
  createdAt?: Date
  updatedAt?: Date
  value?: any
}

export interface Employee extends Entity {
  name: string
  code: string
  employName?: string
  employNO?: string
  userName?: string
}

export interface Product extends Entity {
  name: string
  code: string
  categoryId: number
  category?: Category
  furnaceName?: string
  productName?: string
  ProductDate?: string
  categoryName?: string
  remarkId?: number
  remark?: Remark
  remarkName?: string
  gradeId?: number
  grade?: string
  gradeName?: string
  bushingNO?: string
  workDate?: string
  positionName?: string
  cakeWeight?: number
  twisterNO?: string
  spindleNO?: string
  texName?: string
  biName?: string
  batchName?: string
  codeName?: string
  m_product_id?: number
  m_twqrcodeline_id?: number
}

export interface Category extends Entity {
  name: string
  code: string
  text?: string
  categoryName?: string
}

export interface Remark extends Entity {
  name: string
  code: string
  text?: string
  remarkName?: string
}

export interface Track extends Entity {
  name: string
  code: string
  trackDate?: string
  shiftName?: string
  qrCode?: string
  abnormalityId?: number
  dispositionId?: number
  furnaceProduct?: string
}

export interface Tracksheet extends Entity {
  name: string
  code: string
  trackId: number
  track?: Track
  tracksheetNO?: string
  furnaceName?: string
  productName?: string
  trackDate?: string
  twisterNO?: string
}

export interface Work extends Entity {
  code: string
  date: Date
  employeeId: number
  employee?: Employee
  worklines?: Workline[]
  quantity?: number
  workDate?: string
  group?: string
  shift?: string
  amount?: number
  shiftName?: string
  groupName?: string
}

export interface Workline extends Entity {
  workId: number
  trackId: number
  work?: Work
  track?: Track
}

export interface Inspect extends Entity {
  code: string
  date: Date
  employeeId: number
  employee?: Employee
  inspectlines?: Inspectline[]
  quantity?: number
  typeName?: string
  classDate?: string
  shiftName?: string
  employId?: number
  groupName?: string
}

export interface Inspectline extends Entity {
  inspectId: number
  productId?: number
  remarkId: number
  inspect?: Inspect
  product?: Product
  remark?: Remark
  furnaceName?: string
  productName?: string
  productDate?: string
  gradeName?: string
  categoryId?: number
  category?: Category
  categoryName?: string
  remarkName?: string
  bushingNO?: string
  positionName?: string
  cakeWeight?: number
  workDate?: string
  twisterNO?: string
  spindleNO?: string
  texName?: string
  biName?: string
  batchName?: string
  codeName?: string
  m_product_id?: number
  m_twqrcodeline_id?: number
  documentNO?: number
  tracksheetNO?: string
  gradeId?: number
  grade?: string
  dryTime?: number
  countdown?: number
}

export interface ICount extends Entity {
  code: string
  date: Date
  employeeId: number
  employee?: Employee
  icountlines?: ICountline[]
  id: number
}

export interface ICountline extends Entity {
  icountId: number
  productId: number
  categoryId: number
  icount?: ICount
  product?: Product
  category?: Category
  id: number
}

export interface Disposal extends Entity {
  code: string
  date: Date
  employeeId: number
  employee?: Employee
  disposallines?: Disposalline[]
  quantity?: number
  typeName?: string
  classDate?: string
  shiftName?: string
  employId?: number
  groupName?: string
}

export interface Disposalline extends Entity {
  disposalId: number
  productId?: number
  categoryId?: number | string
  remarkId?: number
  disposal?: Disposal
  product?: Product
  category?: Category
  remark?: Remark
  furnaceName?: string
  productName?: string
  productDate?: string
  gradeName?: string
  categoryName?: string
  remarkName?: string
  workDate?: string
  twisterNO?: string
  spindleNO?: string
  batchName?: string
  codeName?: string
  m_product_id?: number
  m_twqrcodeline_id?: number
  countdown?: number
  created?: string
  value?: any
}

export interface Downgrade extends Entity {
  code?: string
  date?: Date
  employeeId?: number
  employId?: number
  employee?: Employee
  downgradelines?: Downgradeline[]
  quantity?: number
  typeName?: string
  classDate?: string
  shiftName?: string
  groupName?: string
  employName?: string
}

export interface Downgradeline extends Entity {
  downgradeId: number
  productId?: number
  categoryId?: number | string
  remarkId?: number
  downgrade?: Downgrade
  product?: Product
  category?: Category
  remark?: Remark
  furnaceName?: string
  productName?: string
  productDate?: string
  gradeName?: string
  categoryName?: string
  remarkName?: string
  workDate?: string
  twisterNO?: string
  spindleNO?: string
  batchName?: string
  codeName?: string
  m_product_id?: number
  m_twqrcodeline_id?: number
  countdown?: number
  created?: string
  value?: any
}

export interface PInventory extends Entity {
  code?: string
  date?: Date
  employeeId?: number
  employId?: number
  tracksheetId?: number
  employee?: Employee
  tracksheet?: Tracksheet
  pinventorylines?: PInventoryline[]
  quantity?: number
  typeName?: string
  classDate?: string
  shiftName?: string
  groupName?: string
  employName?: string
  created?: string
  updated?: string
}

export interface PInventoryline extends Entity {
  pinventoryId: number
  productId?: number
  pinventory?: PInventory
  product?: Product
  furnaceName?: string
  productName?: string
  productDate?: string
  categoryId?: string | number
  category?: Category
  categoryName?: string
  bushingNO?: string
  workDate?: string
  positionName?: string
  cakeWeight?: number
  twisterNO?: string
  spindleNO?: string
  texName?: string
  biName?: string
  batchName?: string
  codeName?: string
  documentNO?: number
  tracksheetNO?: string
  m_product_id?: string | number
  m_twqrcodeline_id?: string | number
  created?: string
  typeName?: string
  classDate?: string
  pinventoryT1Qty?: number
  pinventoryT2Qty?: number
}

export interface Relabel extends Entity {
  code?: string
  date?: Date
  employeeId?: number
  employId?: number
  employee?: Employee
  relabellines?: Relabelline[]
  quantity?: number
  typeName?: string
  classDate?: string
  shiftName?: string
  groupName?: string
  employName?: string
}

export interface Relabelline extends Entity {
  relabelId: number
  productId?: number
  categoryId?: number | string
  remarkId?: number
  relabel?: Relabel
  product?: Product
  category?: Category
  remark?: Remark
  furnaceName?: string
  productName?: string
  productDate?: string
  gradeName?: string
  categoryName?: string
  remarkName?: string
  workDate?: string
  twisterNO?: string
  spindleNO?: string
  batchName?: string
  codeName?: string
  m_product_id?: number
  m_twqrcodeline_id?: number
  isMFD?: string
  countdown?: number
  created?: string
  value?: any
}

export interface SearchFilter {
  [key: string]: any
  equal?: { [key: string]: any }
  greaterThan?: { [key: string]: any }
  lessThan?: { [key: string]: any }
  greaterThanOrEqual?: { [key: string]: any }
  lessThanOrEqual?: { [key: string]: any }
  contain?: { [key: string]: any }
  startsWith?: { [key: string]: any }
  endsWith?: { [key: string]: any }
  filters?: Array<{
    property: string
    op: string
    val: any
  }>
}

export interface Pagination {
  page: number
  rowsPerPage?: number
  limit?: number
  sortBy?: string[]
  descending?: boolean[]
  search?: string
  totalItems: number
  pages?: number
  totalPages?: number
  hasNextPage?: boolean
  hasPrevPage?: boolean
}

export interface TableHeader {
  text: string
  value: string
  align?: string
  sortable?: boolean
  filterable?: boolean
}
