/**
 * Android Layout 組件統一導出
 */

// 組件導入
import AndroidContainer from './AndroidContainer.vue'
import AndroidButton from './AndroidButton.vue'
import AndroidTextField from './AndroidTextField.vue'
import AndroidDataTable from './AndroidDataTable.vue'

// Composable導入
import { useAndroidLayout } from '@/composables/useAndroidLayout'

// 配置導入
import { 
  getCurrentConfig, 
  mergeConfig, 
  defaultAndroidLayoutConfig,
  environmentConfigs,
  type AndroidLayoutConfig 
} from '@/config/android-layout-config'

// 組件導出
export {
  AndroidContainer,
  AndroidButton,
  AndroidTextField,
  AndroidDataTable
}

// Composable導出
export {
  useAndroidLayout
}

// 配置導出
export {
  getCurrentConfig,
  mergeConfig,
  defaultAndroidLayoutConfig,
  environmentConfigs,
  type AndroidLayoutConfig
}

// Vue插件安裝函數
import type { App } from 'vue'

export const AndroidLayoutPlugin = {
  install(app: App) {
    // 註冊全域組件
    app.component('AndroidContainer', AndroidContainer)
    app.component('AndroidButton', AndroidButton)
    app.component('AndroidTextField', AndroidTextField)
    app.component('AndroidDataTable', AndroidDataTable)
    
    // 提供全域配置
    const config = getCurrentConfig()
    app.provide('androidLayoutConfig', config)
    
    // 全域屬性
    app.config.globalProperties.$androidLayout = {
      config,
      useAndroidLayout
    }
  }
}

// 預設導出
export default AndroidLayoutPlugin

// 便利函數導出
export const createAndroidLayoutConfig = (overrides: Partial<AndroidLayoutConfig> = {}) => {
  return mergeConfig(defaultAndroidLayoutConfig, overrides)
}

// 快速配置預設
export const quickConfigs = {
  // 僅移動端優化
  mobileOnly: createAndroidLayoutConfig({
    enabled: true,
    features: {
      deviceInfo: false,
      fab: false,
      safeArea: true,
      touchOptimization: true,
      autoFocus: false,
      loadingOptimization: true
    }
  }),
  
  // 完整功能
  full: createAndroidLayoutConfig({
    enabled: true,
    features: {
      deviceInfo: true,
      fab: true,
      safeArea: true,
      touchOptimization: true,
      autoFocus: false,
      loadingOptimization: true
    }
  }),
  
  // 開發模式
  development: createAndroidLayoutConfig({
    enabled: true,
    features: {
      deviceInfo: true,
      fab: true,
      safeArea: true,
      touchOptimization: true,
      autoFocus: false,
      loadingOptimization: false
    }
  }),
  
  // 生產模式
  production: createAndroidLayoutConfig({
    enabled: true,
    features: {
      deviceInfo: false,
      fab: false,
      safeArea: true,
      touchOptimization: true,
      autoFocus: false,
      loadingOptimization: true
    }
  })
}

// 類型導出
export type {
  UseAndroidLayoutOptions
} from '@/composables/useAndroidLayout'
