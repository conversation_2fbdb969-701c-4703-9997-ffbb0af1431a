<template>
  <v-navigation-drawer location="right" v-model="rightDrawerVal" temporary>
    <v-list>
      <v-list-item class="my-2">
        <v-list-item-title class="title">Advanced Search</v-list-item-title>
      </v-list-item>
      <slot></slot>
      <v-row class="pl-1 pt-3 ml-1">
        <v-btn elevation="4" color="purple" size="small" icon @click="$emit('searchData')">
          <v-icon>mdi-magnify</v-icon>
        </v-btn>
        <v-btn elevation="4" color="grey" size="small" icon class="ml-2" @click="$emit('cancelSearch')">
          <v-icon>mdi-close-circle-outline</v-icon>
        </v-btn>
      </v-row>
    </v-list>
  </v-navigation-drawer>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  rightDrawer: boolean
}

interface Emits {
  (e: 'searchData'): void
  (e: 'cancelSearch'): void
  (e: 'update:rightDrawer', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const rightDrawerVal = computed({
  get: () => props.rightDrawer,
  set: (value: boolean) => emit('update:rightDrawer', value)
})
</script>
