<template>
  <div class="navigation-section">
    <!-- 區段標題 -->
    <v-list-subheader 
      v-if="!mini && title"
      class="section-header"
    >
      <v-icon :icon="sectionIcon" size="small" class="mr-2" />
      {{ title }}
      <v-spacer />
      <v-chip
        v-if="items.length > 0"
        size="x-small"
        variant="text"
        class="section-count"
      >
        {{ items.length }}
      </v-chip>
    </v-list-subheader>

    <!-- 摺疊/展開控制 -->
    <v-list-item
      v-if="!mini && collapsible"
      @click="toggleExpanded"
      class="section-toggle"
      density="compact"
    >
      <template v-slot:prepend>
        <v-icon 
          :icon="expanded ? 'mdi-chevron-down' : 'mdi-chevron-right'"
          size="small"
        />
      </template>
      <v-list-item-title class="text-caption">
        {{ expanded ? '收起' : '展開' }} {{ title }}
      </v-list-item-title>
    </v-list-item>

    <!-- 菜單項目 -->
    <v-expand-transition>
      <div v-show="expanded || !collapsible">
        <NavigationMenu
          :items="items"
          :active-item="activeItem"
          :is-mobile="isMobile"
          :mini="mini"
          @navigate="$emit('navigate', $event)"
        />
      </div>
    </v-expand-transition>

    <!-- Rail 模式下的區段指示器 -->
    <div v-if="mini && items.length > 0" class="rail-section-indicator">
      <v-tooltip location="end">
        <template v-slot:activator="{ props }">
          <v-btn
            :icon="sectionIcon"
            size="small"
            variant="text"
            v-bind="props"
            class="section-rail-btn"
          />
        </template>
        <div>
          <div class="font-weight-bold mb-1">{{ title }}</div>
          <div v-for="item in items" :key="item.title" class="text-caption">
            • {{ item.title }}
            <v-chip
              v-if="item.badge"
              :color="item.color || 'primary'"
              size="x-small"
              variant="flat"
              class="ml-1"
            >
              {{ item.badge }}
            </v-chip>
          </div>
        </div>
      </v-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import NavigationMenu from './NavigationMenu.vue'

interface AppMenu {
  icon: string
  title: string
  vertical?: string
  link: string
  badge?: string
  color?: string
}

interface Props {
  title: string
  items: AppMenu[]
  activeItem: string
  isMobile: boolean
  mini: boolean
  collapsible?: boolean
  defaultExpanded?: boolean
  icon?: string
}

interface Emits {
  (e: 'navigate', item: AppMenu): void
}

const props = withDefaults(defineProps<Props>(), {
  collapsible: false,
  defaultExpanded: true,
  icon: 'mdi-folder'
})

const emit = defineEmits<Emits>()

// 響應式數據
const expanded = ref(props.defaultExpanded)

// 計算屬性
const sectionIcon = computed(() => {
  if (props.title.includes('Vue3')) return 'mdi-vuejs'
  if (props.title.includes('Android')) return 'mdi-android'
  if (props.title.includes('測試')) return 'mdi-test-tube'
  return props.icon
})

// 方法
const toggleExpanded = () => {
  expanded.value = !expanded.value
}
</script>

<style scoped>
/* 區段容器 */
.navigation-section {
  margin-bottom: 8px;
}

/* 區段標題 */
.section-header {
  font-weight: 600 !important;
  color: rgba(0, 0, 0, 0.6) !important;
  font-size: 12px !important;
  letter-spacing: 0.5px !important;
  text-transform: uppercase !important;
  padding: 8px 16px 4px 16px !important;
  display: flex !important;
  align-items: center !important;
}

.section-count {
  opacity: 0.7;
  font-size: 10px !important;
  height: 16px !important;
}

/* 摺疊/展開控制 */
.section-toggle {
  margin: 0 8px 4px 8px !important;
  border-radius: 6px !important;
  min-height: 32px !important;
}

.section-toggle:hover {
  background-color: rgba(0, 0, 0, 0.04) !important;
}

.section-toggle .v-list-item-title {
  font-size: 11px !important;
  opacity: 0.8;
}

/* Rail 模式指示器 */
.rail-section-indicator {
  display: flex;
  justify-content: center;
  margin: 8px 0;
}

.section-rail-btn {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.section-rail-btn:hover {
  opacity: 1;
}

/* 深色模式適配 */
@media (prefers-color-scheme: dark) {
  .section-header {
    color: rgba(255, 255, 255, 0.6) !important;
  }
  
  .section-toggle:hover {
    background-color: rgba(255, 255, 255, 0.04) !important;
  }
}

/* 動畫效果 */
.v-expand-transition-enter-active,
.v-expand-transition-leave-active {
  transition: all 0.3s ease;
}

.v-expand-transition-enter-from,
.v-expand-transition-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 響應式調整 */
@media (max-width: 599px) {
  .section-header {
    padding: 6px 12px 3px 12px !important;
    font-size: 11px !important;
  }
  
  .section-toggle {
    margin: 0 4px 2px 4px !important;
    min-height: 28px !important;
  }
  
  .section-toggle .v-list-item-title {
    font-size: 10px !important;
  }
}

/* 減少動畫模式支援 */
@media (prefers-reduced-motion: reduce) {
  .v-expand-transition-enter-active,
  .v-expand-transition-leave-active,
  .section-rail-btn {
    transition: none !important;
  }
  
  .v-expand-transition-enter-from,
  .v-expand-transition-leave-to {
    transform: none !important;
  }
}

/* 高對比度模式支援 */
@media (prefers-contrast: high) {
  .section-header {
    color: rgba(0, 0, 0, 0.8) !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  }
  
  @media (prefers-color-scheme: dark) {
    .section-header {
      color: rgba(255, 255, 255, 0.8) !important;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
  }
}
</style>
