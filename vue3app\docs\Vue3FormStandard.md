# Vue3 表單開發規範

基於已完成的Vue3表單升級工作，建立統一的表單開發規範和模板。

## 📋 已完成的Vue3表單

### ✅ 完全升級的表單
1. **InspectFormVue3Simple.vue** - 品質檢驗記錄表單（簡化版）
2. **QIInspectAbnormalFormVue3Simple.vue** - QI品檢異常登錄表單（簡化版）
3. **ICountFormVue3Simple.vue** - QI品檢計數作業表單（簡化版）
4. **DowngradeFormVue3Simple.vue** - 降級異常表單（簡化版）
5. **PInventoryOfYarnFormVue3Simple.vue** - Yarn盤點表單（簡化版）
6. **PInventoryOfCakeFormVue3Simple.vue** - Cake盤點表單（簡化版）
7. **PInventoryOfPackFormVue3Simple.vue** - Pack盤點表單（簡化版）
8. **DisposalFormVue3Simple.vue** - 報廢異常表單（簡化版）
9. **RelabelFormVue3Simple.vue** - Relabel NO MFD表單（簡化版）
10. **EMProductionQueryVue3Simple.vue** - EM即時生產查詢表單（完整版）

### 🔧 部分升級的表單
1. **DowngradeForm.vue** - 降級異常表單（主檔，已補充template，優化Vue3結構）
2. **DisposalForm.vue** - 報廢異常表單（已使用Vue3語法，優化template結構）
3. **RelabelForm.vue** - Relabel NO MFD表單（已使用Vue3語法，優化template結構）

## 🎯 Vue3表單標準架構

### 1. 基本結構

```vue
<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <span class="text-h4">
              <v-icon class="mr-2">mdi-icon-name</v-icon>
              {{ isEditMode ? '編輯記錄' : '新增記錄' }}
            </span>
          </v-card-title>

          <v-card-text>
            <!-- 狀態提示 -->
            <v-alert type="info" class="mb-4" v-if="!isEditMode">
              新增模式提示訊息
            </v-alert>

            <v-alert type="warning" class="mb-4" v-if="isEditMode">
              編輯模式提示訊息
            </v-alert>

            <!-- 主檔表單 -->
            <v-form ref="form" v-model="formValid">
              <v-row>
                <!-- 表單欄位 -->
              </v-row>
            </v-form>

            <!-- 明細檔表格 -->
            <v-divider class="my-4" />
            <v-row>
              <v-col cols="12">
                <h3>明細資料</h3>
                <v-card variant="outlined" class="mt-3">
                  <!-- 明細表格和操作 -->
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>

          <v-card-actions>
            <!-- 操作按鈕 -->
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <!-- 明細編輯對話框 -->
    <v-dialog v-model="detailDialog" max-width="800px">
      <!-- 對話框內容 -->
    </v-dialog>

    <!-- 訊息提示 -->
    <v-snackbar v-model="snackbar.show" :color="snackbar.color" timeout="3000">
      {{ snackbar.message }}
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 路由和導航
const route = useRoute()
const router = useRouter()

// 響應式數據
const form = ref()
const formValid = ref(false)
const formData = ref({
  // 主檔數據結構
})

// 明細檔數據
const detailItems = ref([])
const detailDialog = ref(false)
const detailData = ref({
  // 明細檔數據結構
})

// 計算屬性
const isEditMode = computed(() => !!route.params.id)

// 方法
const saveForm = async () => {
  // 保存邏輯
}

const resetForm = () => {
  // 重設邏輯
}

// 生命週期
onMounted(() => {
  // 初始化邏輯
})
</script>
```

### 2. 響應式數據規範

```typescript
// 主檔數據
const formData = ref({
  id: 0,
  classDate: new Date().toISOString().slice(0, 10),
  shiftName: '',
  employeeId: 0,
  groupName: '',
  typeName: '',
  quantity: 0
})

// 明細檔數據
const detailItems = ref([])
const detailDialog = ref(false)
const editingDetailId = ref(null)

// UI狀態
const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})
```

### 3. 表單驗證規範

```typescript
const rules = {
  required: (value: any) => !!value || '此欄位為必填',
  number: (value: any) => !isNaN(Number(value)) || '請輸入有效數字',
  email: (value: string) => /.+@.+\..+/.test(value) || '請輸入有效的電子郵件'
}
```

### 4. 明細檔操作規範

```typescript
// 新增明細
const addDetailItem = () => {
  editingDetailId.value = null
  detailData.value = { /* 預設值 */ }
  detailDialog.value = true
}

// 編輯明細
const editDetailItem = (item: any) => {
  editingDetailId.value = item.id
  detailData.value = { ...item }
  detailDialog.value = true
}

// 保存明細
const saveDetailItem = () => {
  if (!detailFormValid.value) return

  if (editingDetailId.value) {
    // 編輯模式
    const index = detailItems.value.findIndex(item => item.id === editingDetailId.value)
    if (index > -1) {
      detailItems.value[index] = { ...detailData.value, id: editingDetailId.value }
    }
  } else {
    // 新增模式
    const newDetail = { ...detailData.value, id: Date.now() }
    detailItems.value.push(newDetail)
  }

  closeDetailDialog()
}
```

## 🎨 UI/UX 規範

### 1. 顏色使用
- **主要操作**: `color="primary"`
- **次要操作**: `color="secondary"`
- **危險操作**: `color="error"`
- **成功狀態**: `color="success"`
- **警告狀態**: `color="warning"`
- **資訊提示**: `color="info"`

### 2. 圖示使用
- **新增**: `mdi-plus`
- **編輯**: `mdi-pencil`
- **刪除**: `mdi-delete`
- **儲存**: `mdi-content-save`
- **重設**: `mdi-refresh`
- **返回**: `mdi-arrow-left`
- **成功**: `mdi-check-circle`

### 3. 布局規範
- 使用 `v-container fluid` 作為最外層容器
- 使用 `v-row` 和 `v-col` 進行響應式布局
- 表單欄位使用 `cols="12" md="6"` 實現響應式
- 明細表格使用 `v-data-table` 組件

## 📝 命名規範

### 1. 檔案命名
- Vue3表單: `[功能名]FormVue3.vue`
- 簡化版本: `[功能名]FormVue3Simple.vue`
- 測試版本: `[功能名]TestPlaceholder.vue`

### 2. 變數命名
- 主檔數據: `formData`
- 明細檔數據: `detailItems`
- 對話框狀態: `detailDialog`
- 編輯ID: `editingDetailId`

### 3. 方法命名
- 保存表單: `saveForm`
- 重設表單: `resetForm`
- 新增明細: `addDetailItem`
- 編輯明細: `editDetailItem`
- 保存明細: `saveDetailItem`

## 🧪 測試規範

### 1. 基本功能測試
- [ ] 路由導航正常
- [ ] 表單數據綁定正常
- [ ] 表單驗證正常
- [ ] 明細檔CRUD操作正常

### 2. 響應式測試
- [ ] 數據變更即時反映在UI
- [ ] 計算屬性正常運作
- [ ] 生命週期方法正常執行

### 3. 錯誤處理測試
- [ ] 表單驗證錯誤顯示
- [ ] API錯誤處理
- [ ] 路由錯誤處理

## 📚 參考範例

最佳實踐範例：
- `InspectFormVue3Simple.vue` - 完整的主檔明細表單
- `DowngradeTestPlaceholder.vue` - 路由測試和基本結構
- `Vue3TestPageSimple.vue` - 測試頁面和導航

## 🔄 升級檢查清單

將Vue2表單升級到Vue3時，請檢查：

- [ ] 移除 `vue-facing-decorator` 依賴
- [ ] 將 class 組件改為 `<script setup>`
- [ ] 使用 `ref()` 和 `reactive()` 管理狀態
- [ ] 使用 `computed()` 定義計算屬性
- [ ] 使用 `onMounted()` 等生命週期函數
- [ ] 更新模板語法（如 v-model 語法）
- [ ] 測試所有功能正常運作

---

*此規範基於實際完成的Vue3表單升級工作，持續更新和完善。*
