// 檢驗相關的嚴格類型定義
export type ShiftType = '1' | '2' | '3'
export type GroupType = 'A' | 'B' | 'C' | 'D'
export type InspectType = 'YARN' | 'CAKE' | 'PACK'

// 基礎實體接口
export interface BaseEntity {
  readonly id: number
  readonly createdAt?: Date
  readonly updatedAt?: Date
}

// 員工接口
export interface Employee extends BaseEntity {
  readonly employId: number
  readonly employNO: string
  readonly userName: string
  readonly employName: string
  readonly value?: number
}

// 檢驗主檔接口
export interface Inspect extends BaseEntity {
  classDate: string
  shiftName: ShiftType
  employId: number
  groupName: GroupType
  typeName: InspectType
  quantity: number
  
  // 關聯數據 (只讀)
  readonly employee?: Employee
  readonly inspectlines?: Inspectline[]
  readonly employName?: string
}

// 檢驗明細檔接口
export interface Inspectline extends BaseEntity {
  inspectId: number
  productId?: number
  remarkId?: number
  codeName: string
  remarkName: string
  quantity: number
  
  // 關聯數據 (只讀)
  readonly inspect?: Inspect
  readonly product?: Product
  readonly remark?: Remark
  
  // 產品相關欄位
  readonly furnaceName?: string
  readonly productName?: string
  readonly productDate?: string
  readonly gradeName?: string
  readonly categoryId?: number
  readonly categoryName?: string
  readonly bushingNO?: string
  readonly positionName?: string
  readonly cakeWeight?: number
  readonly workDate?: string
  readonly twisterNO?: string
  readonly spindleNO?: string
  readonly texName?: string
  readonly biName?: string
  readonly batchName?: string
  readonly m_product_id?: number
  readonly m_twqrcodeline_id?: number
  readonly documentNO?: number
  readonly tracksheetNO?: string
  readonly gradeId?: number
  readonly grade?: string
  readonly dryTime?: number
  readonly countdown?: number
}

// 產品接口
export interface Product extends BaseEntity {
  name: string
  code: string
  categoryId: number
  readonly category?: Category
  readonly furnaceName?: string
  readonly productName?: string
  readonly ProductDate?: string
  readonly categoryName?: string
  readonly remarkId?: number
  readonly remark?: string
  readonly remarkName?: string
  readonly gradeId?: number
  readonly grade?: string
  readonly gradeName?: string
  readonly bushingNO?: string
  readonly workDate?: string
  readonly positionName?: string
  readonly cakeWeight?: number
  readonly twisterNO?: string
  readonly spindleNO?: string
  readonly texName?: string
  readonly biName?: string
  readonly batchName?: string
  readonly codeName?: string
  readonly m_product_id?: number
  readonly m_twqrcodeline_id?: number
}

// 分類接口
export interface Category extends BaseEntity {
  name: string
  code: string
  readonly text?: string
  readonly categoryName?: string
}

// 備註接口
export interface Remark extends BaseEntity {
  name: string
  code: string
  readonly text?: string
  readonly remarkName?: string
}

// 表單數據類型 (用於新增/編輯)
export type InspectFormData = Omit<Inspect, keyof BaseEntity | 'employee' | 'inspectlines' | 'employName'> & {
  id?: number | null
}

export type InspectlineFormData = Omit<Inspectline, keyof BaseEntity | 'inspect' | 'product' | 'remark'> & {
  id?: number | null
}

// API 響應類型
export interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
  errors?: string[]
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// 搜索過濾器類型
export interface InspectSearchFilters {
  id?: string
  employName?: string
  groupName?: GroupType
  shiftName?: ShiftType
  typeName?: InspectType
  dateFrom?: string
  dateTo?: string
  quantityMin?: number
  quantityMax?: number
}

// 排序選項類型
export interface SortOption<T> {
  field: keyof T
  direction: 'asc' | 'desc'
}

// 表格標題類型
export interface TableHeader<T = any> {
  title: string
  key: keyof T | string
  align?: 'start' | 'center' | 'end'
  sortable?: boolean
  width?: number | string
  minWidth?: number | string
  maxWidth?: number | string
}

// 驗證規則類型
export type ValidationRule = (value: any) => boolean | string

export interface ValidationRules {
  required: ValidationRule[]
  number: ValidationRule[]
  email?: ValidationRule[]
  date?: ValidationRule[]
  positive?: ValidationRule[]
  integer?: ValidationRule[]
}

// 表單狀態類型
export interface FormState<T> {
  data: T
  valid: boolean
  dirty: boolean
  loading: boolean
  saving: boolean
  errors: Record<keyof T, string[]>
}

// 列表狀態類型
export interface ListState<T> {
  items: T[]
  loading: boolean
  selectedItems: T[]
  searchQuery: string
  filters: Record<string, any>
  pagination: {
    page: number
    pageSize: number
    total: number
  }
  sort: SortOption<T>[]
}

// 通知訊息類型
export interface SnackbarMessage {
  show: boolean
  message: string
  color: 'success' | 'error' | 'warning' | 'info'
  timeout?: number
}

// 對話框狀態類型
export interface DialogState<T = any> {
  show: boolean
  title: string
  message?: string
  data?: T
  loading?: boolean
}

// 操作結果類型
export type OperationResult<T = any> = {
  success: true
  data: T
  message?: string
} | {
  success: false
  error: string
  details?: any
}

// 事件處理器類型
export interface InspectEventHandlers {
  onSaveSuccess?: (data: Inspect) => void
  onSaveError?: (error: any) => void
  onLoadSuccess?: (data: Inspect) => void
  onLoadError?: (error: any) => void
  onDeleteSuccess?: (id: number) => void
  onDeleteError?: (error: any) => void
}

export interface InspectlineEventHandlers {
  onDetailSaved?: (detail: Inspectline) => void
  onDetailDeleted?: (detail: Inspectline) => void
  onDetailError?: (error: any, operation: string) => void
}
