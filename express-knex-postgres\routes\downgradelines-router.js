const router = require("express").Router();
const moment = require('moment');

const downgradelinesDB = require("../models/downgradelines-model.js");
const productsDB = require("../models/products-model.js");
const categoriesDB = require("../models/categories-model.js");
const remarksDB = require("../models/remarks-model.js");

// GET DOWNGRADELINE BY ID
router.get("/:id", async (req, res) => {
  const downgradeId = req.params.id;
  try {
    const downgradeline = await downgradelinesDB.findById(downgradeId);
    if (!downgradeline) {
      res.status(404).json({ err: "The downgradeline with the specified id does not exist" });
    } else {
      const categories = await categoriesDB.find();
      const remarks = await remarksDB.find();

      downgradeline.map(mem => {
        mem.workDate = moment(mem.workDate).format("YYYY-M-D HH:mm");
        const category = categories.find(info => info.categoryId === mem.categoryId);
        if (category) {
          mem.categoryName = category.categoryName;
        }
        const remark = remarks.find(info => info.remarkId === mem.remarkId);
        if (remark) {
          mem.remarkName = remark.remarkName;
        }
        return mem;
      });

      res.status(200).json(downgradeline);
    }
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET DOWNGRADELINE BY CODE
router.get("/duplicate/:id", async (req, res) => {
  const codeName = req.params.id;
  try {
    const exist = await downgradelinesDB.findByCode(codeName);
    if (Array.isArray(exist) && exist.length > 0) {
      res.status(200).json(exist);
    } else {
      res.status(404).json({ error: "Data not found" });
    }
  } catch (err) {
    console.error(err);
    res.status(500).json({ err: err.message });
  }
});

// INSRT DOWNGRADELINE INTO DB
router.post("/", async (req, res) => {
  const newDowngradeline = req.body;
  if (!newDowngradeline.downgradeId) {
    res.status(404).json({ err: "Please provide the id" });
  } else {
    try {
      const newProductId = await productsDB.findProductIdOfGrade(newDowngradeline.productName, newDowngradeline.gradeName);
      const downgradeline = await downgradelinesDB.addDowngradeline(newDowngradeline, newProductId);
      res.status(201).json(downgradeline);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

// REMOVE DOWNGRADELINE INTO DB
router.delete("/:id", async (req, res) => {
  const downgradelineId = req.params.id;
  try {
    const deleting = await downgradelinesDB.removeDowngradeline(downgradelineId);
    res.status(204).json(deleting);
  } catch (err) {
    res.status(500).json({err: err.message });
  }
});

module.exports = router;