import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getData, putData, postData, deleteData } from '@/utils/backend-api'
import type { Employee, PInventory, Entity, Product, Tracksheet, PInventoryline, Pagination } from '@/types'

export const usePInventoriesStore = defineStore('pinventories', () => {
  // State
  const items = ref<Entity[]>([])
  const pagination = ref<Pagination>({
    page: 1,
    limit: 10,
    sortBy: [],
    descending: [],
    search: '',
    totalItems: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false
  })
  const loading = ref(false)
  const employee = ref('')
  const pinventoryId = ref<number | null>(null)
  const pinventory = ref<PInventory>({} as PInventory)
  const pinventorylines = ref<PInventoryline[]>([])
  const tracksheet = ref<Tracksheet>({} as Tracksheet)
  const employees = ref<Employee[]>([])
  const product = ref<Product>({} as Product)

  // Getters
  const isLoading = computed(() => loading.value)
  const currentPInventory = computed(() => pinventory.value)
  const currentPInventorylines = computed(() => pinventorylines.value)

  // Actions
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setPInventory = (value: PInventory) => {
    // 如果是部分更新，合併現有資料
    if (pinventory.value && Object.keys(pinventory.value).length > 0) {
      pinventory.value = { ...pinventory.value, ...value }
    } else {
      pinventory.value = value
    }
  }

  const setPInventoryId = (value: number | null) => {
    pinventoryId.value = value
  }

  const setPInventorylines = (value: PInventoryline[]) => {
    pinventorylines.value = value
  }

  const setTracksheet = (value: Tracksheet) => {
    tracksheet.value = value
  }

  const setEmployees = (value: Employee[]) => {
    employees.value = value
  }

  const setProduct = (value: Product) => {
    product.value = value
  }

  const setItems = (value: Entity[]) => {
    items.value = value
  }

  const setPagination = (value: Pagination) => {
    pagination.value = { ...pagination.value, ...value }
  }

  // API Actions
  const getEmployees = async () => {
    try {
      setLoading(true)
      const res = await getData("employees/tw")
      if (res.data) {
        // 先去重，再處理數據
        const uniqueData = res.data.filter((employee: any, index: number, self: any[]) =>
          index === self.findIndex((e: any) => e.employId === employee.employId)
        )

        const employeeList = uniqueData.map((c: any, index: number) => {
          return {
            // 確保每個項目都有唯一的 key，使用 employId 作為主鍵
            employId: c.employId,
            employNO: c.employNO,
            userName: c.userName,
            // 根據 Vue2 的邏輯，顯示格式為 employNO + userName
            employName: c.employNO + " " + c.userName,
            // 使用 employId 作為 value，與後端 API 一致
            value: c.employId,
            // 為 Vuetify 提供唯一的 key
            key: `employee_${c.employId}_${index}`,
            // 其他可能需要的欄位
            ...c
          }
        })

        console.log('Store: 員工數據處理完成:', employeeList)
        setEmployees(employeeList)
      }
    } catch (error) {
      console.error('獲取員工列表失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const getAllPInventoriesByType = async (type: string) => {
    setLoading(true)
    try {
      const response = await getData(`pinventories/${type}`)

      if (response.data && Array.isArray(response.data)) {
        // 後端返回的數據已經包含正確的 quantity 值
        const allItems = response.data.map((item: any) => ({
          ...item,
          // 使用後端計算的 quantity，不要重新計算
          quantity: parseInt(item.quantity) || 0
        }))

        items.value = allItems
        pagination.value.totalItems = allItems.length
      } else {
        items.value = []
        pagination.value.totalItems = 0
      }
    } catch (error) {
      console.error('API Error:', error)
      items.value = []
      pagination.value.totalItems = 0
      throw error
    } finally {
      setLoading(false)
    }
  }

  const getPInventoryById = async (id: string | number) => {
    try {
      setLoading(true)
      console.log('Store: 開始獲取盤點記錄，ID:', id)
      const res = await getData(`pinventories/${id}`)
      console.log('Store: API 響應:', res)

      if (res.data && Array.isArray(res.data) && res.data.length > 0) {
        // 後端返回的是數組，取第一個元素
        const pinventoryData = res.data[0]
        console.log('Store: 設置主檔數據:', pinventoryData)

        // 計算數量
        if (pinventoryData.pinventorylines && Array.isArray(pinventoryData.pinventorylines)) {
          pinventoryData.quantity = pinventoryData.pinventorylines.length
          setPInventorylines(pinventoryData.pinventorylines)
        } else {
          pinventoryData.quantity = 0
          setPInventorylines([])
        }

        setPInventory(pinventoryData)
        pinventoryId.value = Number(id)
      } else {
        console.log('Store: API 響應格式不正確或無數據')
        setPInventory({} as PInventory)
        setPInventorylines([])
      }
    } catch (error) {
      console.error('獲取盤點記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const savePInventory = async (data: PInventory) => {
    try {
      setLoading(true)
      let res

      if (data.id && data.id > 0) {
        // 更新現有記錄 - 使用 PUT 方法
        console.log('Store: 更新現有記錄，ID:', data.id)
        res = await putData(`pinventories/${data.id}`, data)
        if (res.data) {
          // PUT 請求通常返回更新後的單個對象
          const updatedPInventory = Array.isArray(res.data) ? res.data[0] : res.data
          setPInventory(updatedPInventory)
          pinventoryId.value = updatedPInventory.id
        }
      } else {
        // 創建新記錄 - 使用 POST 方法
        console.log('Store: 創建新記錄')
        res = await postData('pinventories/', data)
        if (res.data && Array.isArray(res.data) && res.data.length > 0) {
          // 後端返回數組，取第一個元素
          const newPInventory = res.data[0]
          setPInventory(newPInventory)
          pinventoryId.value = newPInventory.id
        }
      }

      return res.data
    } catch (error) {
      console.error('保存盤點記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const deletePInventory = async (id: number) => {
    try {
      setLoading(true)
      await deleteData(`pinventories/${id}`)

      // 如果刪除的是當前記錄，清空狀態
      if (pinventoryId.value === id) {
        pinventory.value = {} as PInventory
        pinventorylines.value = []
        pinventoryId.value = null
      }
    } catch (error) {
      console.error('刪除盤點記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 明細檔操作
  const addPInventorylineToPInventory = async (data: PInventoryline) => {
    try {
      if (data) {
        // 按照 Vue2 版本的邏輯添加 typeName 和 classDate
        const typeName = { typeName: pinventory.value.typeName }
        const classDate = { classDate: pinventory.value.classDate }
        const newPInventoryline = { ...data, ...typeName, ...classDate }

        console.log('Store: 發送前添加 typeName 和 classDate:', newPInventoryline)

        const res = await postData('pinventorylines/', newPInventoryline)
        if (res.data) {
          // 重新載入明細檔
          if (pinventoryId.value) {
            await getPInventoryById(pinventoryId.value)
          }
        }
        return res.data
      }
    } catch (error) {
      console.error('新增明細記錄失敗:', error)
      throw error
    }
  }

  const updatePInventoryline = async (data: PInventoryline) => {
    try {
      const res = await putData(`pinventorylines/${data.id}`, data)
      if (res.data) {
        // 重新載入明細檔
        if (pinventoryId.value) {
          await getPInventoryById(pinventoryId.value)
        }
      }
      return res.data
    } catch (error) {
      console.error('更新明細記錄失敗:', error)
      throw error
    }
  }

  const deletePInventoryline = async (id: number) => {
    try {
      await deleteData(`pinventorylines/${id}`)

      // 重新載入明細檔
      if (pinventoryId.value) {
        await getPInventoryById(pinventoryId.value)
      }
    } catch (error) {
      console.error('刪除明細記錄失敗:', error)
      throw error
    }
  }

  const getDuplicatePInventorylineByCode = async (tracks: string[]): Promise<boolean> => {
    const type = tracks[0].toString()
    const trackId = tracks[2].toString()
    try {
      setLoading(true)
      if (trackId) {
        const res = await getData("pinventorylines/duplicate/" + trackId)
        const data = res.data
        if (data !== undefined && data !== null && Array.isArray(data) && data.length > 0) {
          for (const item of data) {
            if (item.typeName === type) {
              return true
            }
          }
        }
        return false
      } else {
        return false
      }
    } catch (error: any) {
      // 404 錯誤表示沒有重複，這是正常情況
      if (error?.response?.status === 404) {
        console.log('沒有重複的 QR Code，可以繼續')
        return false
      }
      console.error('檢查重複 QR Code 失敗:', error)
      return false
    } finally {
      setLoading(false)
    }
  }

  // 清空明細檔
  const clearPInventorylines = () => {
    setPInventorylines([])
  }

  // 重置狀態
  const resetState = () => {
    items.value = []
    pinventory.value = {} as PInventory
    pinventorylines.value = []
    tracksheet.value = {} as Tracksheet
    pinventoryId.value = null
    employee.value = ''
    loading.value = false
  }

  return {
    // State
    items,
    pagination,
    loading,
    employee,
    pinventoryId,
    pinventory,
    pinventorylines,
    tracksheet,
    employees,
    product,

    // Getters
    isLoading,
    currentPInventory,
    currentPInventorylines,

    // Actions
    setLoading,
    setPInventory,
    setPInventoryId,
    setPInventorylines,
    setTracksheet,
    setEmployees,
    setProduct,
    setItems,
    setPagination,
    getEmployees,
    getAllPInventoriesByType,
    getPInventoryById,
    savePInventory,
    deletePInventory,
    addPInventorylineToPInventory,
    updatePInventoryline,
    deletePInventoryline,
    getDuplicatePInventorylineByCode,
    clearPInventorylines,
    resetState
  }
})
