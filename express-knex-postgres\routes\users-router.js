const router = require("express").Router();
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

const usersDB = require("../models/users-model.js");

// Login endpoint
router.post("/login", async (req, res) => {
  const { username, password } = req.body;
  
  try {
    // For demo purposes, create a test user if database is not available
    let user = null;

    try {
      // Find user by email/username
      user = await usersDB.findByEmail(username);

      // If not found by email, try by username
      if (!user) {
        user = await usersDB.findByUsername(username);
      }
    } catch (dbError) {
      console.log('Database not available, using test user');
      // If database is not available, use test credentials
      if (username === 'test' && password === 'test') {
        // Generate hash for 'test' password
        const testPasswordHash = await bcrypt.hash('test', 10);
        user = {
          id: 1,
          email: '<EMAIL>',
          username: 'test',
          firstname: 'Test',
          lastname: 'User',
          password: testPasswordHash
        };
      }
    }

    if (!user) {
      return res.status(401).json({
        message: "Invalid credentials"
      });
    }

    // Verify password
    const validPassword = await bcrypt.compare(password, user.password);
    if (!validPassword) {
      return res.status(401).json({ 
        message: "Invalid credentials" 
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );

    // Return user data and token
    res.status(200).json({
      token,
      user: {
        id: user.id,
        email: user.email,
        firstname: user.firstname,
        lastname: user.lastname
      }
    });
  } catch (err) {
    console.error('Login error:', err);
    res.status(500).json({ 
      message: "Server error during login",
      error: err.message 
    });
  }
});

// GET ALL USERS
router.get("/", async (req, res) => {
  try {
    const users = await usersDB.find();
    res.status(200).json(users);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET USER BY ID
router.get("/:id", async (req, res) => {
  const userId = req.params.id;
  try {
    const user = await usersDB.findById(userId);
    if (!user) {
      res.status(404).json({ err: "The specified id does not exist" });
    } else {
      res.status(200).json(user);
    }
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;
