import { getData, putData, postData, deleteData } from "@/utils/backend-api";
import {
  Employee,
  Downgrade,
  Entity,
  Product,
  Category,
  Downgradeline
} from "@/types";
import { getDefaultPagination, getPagination } from "@/utils/store-util";
import { appModule } from "./app";
import { get } from "lodash";
import {
  VuexModule,
  Module,
  Mutation,
  Action,
  getModule
} from "vuex-module-decorators";
import store from "@/store";

export interface DowngradeState {
  items: Entity[];
  pagination: Pagination;
  loading: boolean;
  employee: string;
  downgradeId: number;
  downgrade: Downgrade;
  downgradeline: Downgradeline[];
  product: Product;
  employees: Employee[];
  categories: Category[];
}

@Module({ store, dynamic: true, name: "downgrades" })
class DowngradeModule extends VuexModule implements DowngradeState {
  public items: Entity[] = [];
  public pagination = getDefaultPagination();
  public loading = false;
  public employee = "";
  public downgradeId = null;
  public downgrade = {} as Downgrade;
  public downgradeline: Downgradeline[] = [];
  public product = {} as Product;
  public employees: Employee[] = [];
  public categories: Category[] = [];

  @Action
  getEmployees() {
    getData("employees/tw").then(res => {
      if (res.data) {
        const employees = res.data.map((c: Employee) => {
          c.employName = c.employNO + " " + c.userName;
          c.value = c.id;
          return c;
        });
        this.setEmployees(employees);
      }
    });
  }

  @Action
  async getAllDowngradesByType(type: string) {
    this.setLoading(true);
    try {
      const res = await getData(`downgrades/${type}`);
      const downgrades = res.data;
      this.setDowngrade(downgrades);
      this.setDataTable(downgrades);
    } catch (error) {
      console.error(error);
    } finally {
      this.setLoading(false);
    }
  }

  @Action
  getDowngradeById(id: string) {
    if (id) {
      getData("downgrades/" + id).then(
        res => {
          const _downgrade = res.data;
          const downgrade = _downgrade[0];
          downgrade.downgradelines.filter(
            (p: Downgrade) => p !== null && p !== undefined
          );
          downgrade.quantity = downgrade.downgradelines?.length;
          this.setDowngrade(downgrade);
          this.setDataTable(downgrade.downgradelines);
        },
        (err: TODO) => {
          console.log(err);
        }
      );
    } else {
      const downgrade = {} as Downgrade;
      downgrade.downgradelines = [];
      this.setDowngrade(downgrade);
      this.setLoading(false);
    }
  }

  @Action
  async getProductById(id: string) {
    try {
      this.setLoading(true);
      if (id) {
        const res = await getData("products/" + id);
        const product = res.data;
        this.setProduct(product);
      } else {
        this.setProduct({} as Product);
      }
    } catch (err) {
        console.log(err);
    } finally {
      this.setLoading(false);
    }
  }

  @Action async getDuplicateDowngradelineByCode(code: string): Promise<boolean> {
    try {
      this.setLoading(true);
      if (code) {
        const res = await getData("downgradelines/duplicate/" + code);
        const data = res.data;
        if (data !== undefined && data !== null) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    } catch (err) {
      console.log(err);
      return false;
    } finally {
      this.setLoading(false);
    }
  }

  @Action
  searchDowngrades(searchQuery: string) {
    getData("downgrades" + searchQuery).then(res => {
      const downgrades = res.data;
      downgrades.forEach((item: Downgrade) => {
        item.quantity = item.downgradelines?.length;
      });
      this.setDataTable(downgrades);
      this.setLoading(false);
    });
  }

  @Action
  quickSearch(headers: TableHeader[], qsFilter: SeachQuery): void {
    getData("downgrades").then(res => {
      const downgrades = res.data.filter((r: TODO) =>
        headers.some((header: TableHeader) => {
          const val = get(r, [header.value]);
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter)) ||
            false
          );
        })
      );
      downgrades.forEach((item: Downgrade) => {
        item.quantity = item.downgradelines?.length;
      });
      this.setDataTable(downgrades);
      this.setLoading(false);
    });
  }

  @Action
  saveDowngrade(downgrade: Downgrade):Promise<void> {
    return new Promise((resolve, reject) => {
      if (!downgrade.id) {
        postData("downgrades/", downgrade)
          .then(res => {
            const downgrade = res.data;
            const DowngradeId = { id: downgrade[0].id };
            const addDowngrade = { ...DowngradeId, ...this.downgrade};
            this.setDowngrade(addDowngrade);
            this.setDowngradeId(addDowngrade.id);
            appModule.sendSuccessNotice("New record has been added.");
            appModule.closeNoticeWithDelay(3000);
            resolve();
          })
          .catch((err: TODO) => {
            console.log(err);
            appModule.sendErrorNotice("Operation failed! Please try again later.");
            appModule.closeNoticeWithDelay(5000);
            reject(err);
          });
      } else {
        putData("downgrades/" + downgrade.id.toString(), downgrade)
          .then(() => {
            appModule.sendSuccessNotice("The record has been updated.");
            appModule.closeNoticeWithDelay(3000);
            resolve();
          })
          .catch((err: TODO) => {
            console.log(err);
            appModule.sendErrorNotice("Operation failed! Please try again later.");
            appModule.closeNoticeWithDelay(5000);
            reject(err);
          });
      }
    });
  }

  @Action
  addDowngradelineToDowngrade(downgradeline: Downgradeline) {
    if (downgradeline) {
      this.saveDowngradeline(downgradeline);
      const downgradeId = this.downgrade.id;
      this.getDowngradeById(downgradeId.toString());
      const newDowngrade = this.downgrade;
      this.setDowngrade(newDowngrade);
    }
  }

  @Action
  saveDowngradeline(downgradeline: Downgradeline) {
    if (!downgradeline.id) {
      postData("downgradelines/", downgradeline)
        .then(res => {
          const downgradeline = res.data;
          this.setDowngradeline(downgradeline);
          appModule.sendSuccessNotice("New record has been added.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    } else {
      putData("downgrades/" + downgradeline.id.toString(), downgradeline)
        .then(res => {
          const downgrade = res.data;
          this.setDowngrade(downgrade);
          appModule.sendSuccessNotice("The record has been updated.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    }
  }

  @Action
  async deleteDowngrade(id: number) {
    try {
      await deleteData(`downgrades/${id.toString()}`);
      appModule.sendSuccessNotice("Operation is done.");
      appModule.closeNoticeWithDelay(3000);
    }
    catch (error) {
      console.error(error);
      appModule.sendErrorNotice("Operation failed! Please try again later.");
      appModule.closeNoticeWithDelay(5000);
    }
    finally {
      this.setLoading(false);
    }
  }

  @Action
  deleteDowngradeline(downgradeline: Downgradeline) {
    if (downgradeline) {
      const downgradeId = this.downgrade.id;
      const downgradelineId = downgradeline.id;
      const { downgradelines } = this.downgrade;
      downgradelines.splice(
        downgradelines.findIndex((p: Downgradeline) => p.id === downgradeline.id),
      );
      this.setDowngradeline(downgradelines);
      deleteData(`downgradelines/${downgradelineId.toString()}`)
        .then(() => {
          this.getDowngradeById(downgradeId.toString());
          const newDowngrade = this.downgrade;
          this.setDowngrade(newDowngrade);
          appModule.sendSuccessNotice("Operation is done.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    }
  }

  @Action
  clearDowngradeline() {
    this.setLoading(true);
    const downgradeline = [];
    this.setDowngradeline(downgradeline);
    this.setLoading(false);
  }

  @Action
  setDataTable(items: Downgrade[]) {
    const pagination = getPagination(items);
    this.setPagination(pagination);
    this.setItems(items);
  }

  @Mutation
  setEmployees(employees: Employee[]) {
    this.employees = employees;
  }

  @Mutation
  setCategories(categories: Category[]) {
    this.categories = categories;
  }

  @Mutation
  setDowngradeId(id: number | null) {
    this.downgradeId = id;
  }

  @Mutation
  setDowngrade(downgrade: Downgrade) {
    this.downgrade = downgrade;
  }

  @Mutation
  setDowngradeline(downgradeline: Downgradeline[]) {
    this.downgradeline = downgradeline;
  }

  @Mutation
  setProduct(product: Product) {
    this.product = product;
  }

  @Mutation
  setItems(downgrades: Downgrade[]) {
    this.items = downgrades;
  }

  @Mutation
  setPagination(pagination: Pagination) {
    this.pagination = pagination;
  }

  @Mutation
  setLoading(loading: boolean) {
    this.loading = loading;
  }
}

export const downgradeModule = getModule(DowngradeModule);
