import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getData, putData, postData, deleteData } from '@/utils/backend-api'
import type { Employee, Inspect, Entity, Product, Category, Remark, Inspectline, Pagination } from '@/types'

export const useInspectsStore = defineStore('inspects', () => {
  // State
  const items = ref<Entity[]>([])
  const pagination = ref<Pagination>({
    page: 1,
    rowsPerPage: 10,
    sortBy: [],
    descending: [],
    search: '',
    totalItems: 0,
    pages: 0
  })
  const loading = ref(false)
  const employee = ref('')
  const inspectId = ref<number | null>(null)
  const inspect = ref<Inspect>({} as Inspect)
  const inspectlines = ref<Inspectline[]>([])
  const product = ref<Product>({} as Product)
  const employees = ref<Employee[]>([])
  const categories = ref<Category[]>([])
  const remarks = ref<Remark[]>([])

  // Getters
  const isLoading = computed(() => loading.value)
  const currentInspect = computed(() => inspect.value)
  const currentInspectlines = computed(() => inspectlines.value)

  // Actions
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setInspect = (value: Inspect) => {
    // 如果是部分更新，合併現有資料
    if (inspect.value && Object.keys(inspect.value).length > 0) {
      inspect.value = { ...inspect.value, ...value }
    } else {
      inspect.value = value
    }
  }

  const setInspectlines = (value: Inspectline[]) => {
    inspectlines.value = value
  }

  const setEmployees = (value: Employee[]) => {
    employees.value = value
  }

  const setCategories = (value: Category[]) => {
    categories.value = value
  }

  const setRemarks = (value: Remark[]) => {
    remarks.value = value
  }

  // API Actions
  const getEmployees = async () => {
    try {
      setLoading(true)
      const res = await getData("employees/qi")
      if (res.data) {
        // 先去重，再處理數據
        const uniqueData = res.data.filter((employee: any, index: number, self: any[]) =>
          index === self.findIndex((e: any) => e.employId === employee.employId)
        )

        const employeeList = uniqueData.map((c: any, index: number) => {
          return {
            // 確保每個項目都有唯一的 key，使用 employId 作為主鍵
            employId: c.employId,
            employNO: c.employNO,
            userName: c.userName,
            // 根據 Vue2 的邏輯，顯示格式為 employNO + userName
            employName: c.employNO + " " + c.userName,
            // 使用 employId 作為 value，與後端 API 一致
            value: c.employId,
            // 為 Vuetify 提供唯一的 key
            key: `employee_${c.employId}_${index}`,
            // 其他可能需要的欄位
            ...c
          }
        })

        console.log('Store: 員工數據處理完成:', employeeList)
        setEmployees(employeeList)
      }
    } catch (error) {
      console.error('獲取員工列表失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const getCategories = async () => {
    try {
      const res = await getData("categories")
      if (res.data) {
        setCategories(res.data)
      }
    } catch (error) {
      console.error('獲取分類列表失敗:', error)
      throw error
    }
  }

  const getRemarks = async () => {
    try {
      const res = await getData("remarks")
      if (res.data) {
        setRemarks(res.data)
      }
    } catch (error) {
      console.error('獲取備註列表失敗:', error)
      throw error
    }
  }

  const getInspectById = async (id: string | number) => {
    try {
      setLoading(true)
      console.log('Store: 開始獲取檢驗記錄，ID:', id)
      const res = await getData(`inspects/${id}`)
      console.log('Store: API 響應:', res)

      if (res.data && Array.isArray(res.data) && res.data.length > 0) {
        // 後端返回的是數組，取第一個元素
        const inspectData = res.data[0]
        console.log('Store: 設置主檔數據:', inspectData)
        setInspect(inspectData)
        inspectId.value = Number(id)

        // 同時獲取明細檔
        if (inspectData.inspectlines && Array.isArray(inspectData.inspectlines)) {
          console.log('Store: 設置明細檔數據:', inspectData.inspectlines)
          setInspectlines(inspectData.inspectlines)
        } else {
          console.log('Store: 沒有明細檔數據')
          setInspectlines([])
        }
      } else {
        console.log('Store: API 響應格式不正確或無數據')
        setInspect({} as Inspect)
        setInspectlines([])
      }
      return res.data
    } catch (error) {
      console.error('獲取檢驗記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const saveInspect = async (data: Inspect) => {
    try {
      setLoading(true)
      let res

      if (data.id && data.id > 0) {
        // 更新現有記錄 - 使用 PUT 方法
        console.log('Store: 更新現有記錄，ID:', data.id)
        res = await putData(`inspects/${data.id}`, data)
        if (res.data) {
          // PUT 請求通常返回更新後的單個對象
          const updatedInspect = Array.isArray(res.data) ? res.data[0] : res.data
          setInspect(updatedInspect)
          inspectId.value = updatedInspect.id
        }
      } else {
        // 創建新記錄 - 使用 POST 方法
        console.log('Store: 創建新記錄')
        res = await postData('inspects/', data)
        if (res.data && Array.isArray(res.data) && res.data.length > 0) {
          // 後端返回數組，取第一個元素
          const newInspect = res.data[0]
          setInspect(newInspect)
          inspectId.value = newInspect.id
        }
      }

      return res.data
    } catch (error) {
      console.error('保存檢驗記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const updateInspect = async (data: Inspect) => {
    return await saveInspect(data)
  }

  const deleteInspect = async (id: number) => {
    try {
      setLoading(true)
      await deleteData(`inspects/${id}`)

      // 如果刪除的是當前記錄，清空狀態
      if (inspectId.value === id) {
        inspect.value = {} as Inspect
        inspectlines.value = []
        inspectId.value = null
      }
    } catch (error) {
      console.error('刪除檢驗記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 明細檔操作
  const addInspectlineToInspect = async (data: Inspectline) => {
    try {
      const res = await postData('inspectlines/', data)
      if (res.data) {
        // 重新載入明細檔
        if (inspectId.value) {
          await getInspectById(inspectId.value)
        }
      }
      return res.data
    } catch (error) {
      console.error('新增明細記錄失敗:', error)
      throw error
    }
  }

  const updateInspectline = async (data: Inspectline) => {
    try {
      const res = await putData(`inspectlines/${data.id}`, data)
      if (res.data) {
        // 重新載入明細檔
        if (inspectId.value) {
          await getInspectById(inspectId.value)
        }
      }
      return res.data
    } catch (error) {
      console.error('更新明細記錄失敗:', error)
      throw error
    }
  }

  const deleteInspectline = async (data: Inspectline) => {
    try {
      await deleteData(`inspectlines/${data.id}`)

      // 重新載入明細檔
      if (inspectId.value) {
        await getInspectById(inspectId.value)
      }
    } catch (error) {
      console.error('刪除明細記錄失敗:', error)
      throw error
    }
  }

  // 檢查重複的 QR Code
  const getDuplicateInspectlineByCode = async (code: string): Promise<boolean> => {
    try {
      setLoading(true)
      if (code) {
        const res = await getData(`inspectlines/duplicate/${code}`)
        const data = res.data
        if (data !== undefined && data !== null && Array.isArray(data) && data.length > 0) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    } catch (error: any) {
      // 404 錯誤表示沒有重複，這是正常情況
      if (error?.response?.status === 404) {
        console.log('沒有重複的 QR Code，可以繼續')
        return false
      }
      console.error('檢查重複 QR Code 失敗:', error)
      return false
    } finally {
      setLoading(false)
    }
  }

  // 載入檢驗列表
  const loadInspects = async (page: number = 1, limit: number = 10) => {
    try {
      setLoading(true)
      const response = await getData(`inspects/YARN?page=${page}&limit=${limit}`)

      if (response.data) {
        // 檢查是否為新的分頁格式
        if (response.data.data && response.data.pagination) {
          items.value = response.data.data
          pagination.value = {
            ...pagination.value,
            ...response.data.pagination
          }
        } else if (Array.isArray(response.data)) {
          items.value = response.data
          pagination.value.totalItems = response.data.length
        } else {
          items.value = []
          pagination.value.totalItems = 0
        }
      } else {
        items.value = []
        pagination.value.totalItems = 0
      }
      return items.value
    } catch (error) {
      console.error('API Error:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 清空明細檔
  const clearInspectline = () => {
    setLoading(true)
    setInspectlines([])
    setLoading(false)
  }

  // 重置狀態
  const resetState = () => {
    items.value = []
    inspect.value = {} as Inspect
    inspectlines.value = []
    inspectId.value = null
    employee.value = ''
    loading.value = false
  }

  return {
    // State
    items,
    pagination,
    loading,
    employee,
    inspectId,
    inspect,
    inspectlines,
    product,
    employees,
    categories,
    remarks,

    // Getters
    isLoading,
    currentInspect,
    currentInspectlines,

    // Actions
    setLoading,
    setInspect,
    setInspectlines,
    setEmployees,
    setCategories,
    setRemarks,
    getEmployees,
    getCategories,
    getRemarks,
    getInspectById,
    saveInspect,
    updateInspect,
    deleteInspect,
    addInspectlineToInspect,
    updateInspectline,
    deleteInspectline,
    getDuplicateInspectlineByCode,
    loadInspects,
    clearInspectline,
    resetState
  }
})
