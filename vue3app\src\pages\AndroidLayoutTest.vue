<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <v-icon class="mr-2">mdi-android</v-icon>
            Android Layout 優化測試
          </v-card-title>

          <v-card-text>
            <v-alert type="info" class="mb-4">
              <strong>📱 Android Layout 測試</strong>
              此頁面用於測試Android設備上的layout優化效果。
            </v-alert>

            <!-- 設備信息顯示 -->
            <v-row>
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-information</v-icon>
                    設備信息
                  </v-card-title>
                  <v-card-text>
                    <v-list>
                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon>mdi-monitor</v-icon>
                        </template>
                        <v-list-item-title>螢幕類型</v-list-item-title>
                        <v-list-item-subtitle>
                          {{ isMobile ? '移動設備' : isTablet ? '平板設備' : '桌面設備' }}
                        </v-list-item-subtitle>
                      </v-list-item>

                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon>mdi-resize</v-icon>
                        </template>
                        <v-list-item-title>螢幕尺寸</v-list-item-title>
                        <v-list-item-subtitle>
                          {{ screenWidth }} x {{ screenHeight }}
                        </v-list-item-subtitle>
                      </v-list-item>

                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon>mdi-cellphone</v-icon>
                        </template>
                        <v-list-item-title>Vuetify 斷點</v-list-item-title>
                        <v-list-item-subtitle>
                          {{ currentBreakpoint }}
                        </v-list-item-subtitle>
                      </v-list-item>

                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon>mdi-gesture-tap</v-icon>
                        </template>
                        <v-list-item-title>觸摸支持</v-list-item-title>
                        <v-list-item-subtitle>
                          {{ touchSupported ? '支持' : '不支持' }}
                        </v-list-item-subtitle>
                      </v-list-item>
                    </v-list>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>

            <!-- 響應式組件測試 -->
            <v-row class="mt-4">
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-responsive</v-icon>
                    響應式組件測試
                  </v-card-title>
                  <v-card-text>
                    <!-- 按鈕測試 -->
                    <v-row>
                      <v-col cols="12">
                        <h4>按鈕觸摸測試</h4>
                        <v-row :dense="isMobile">
                          <v-col :cols="isMobile ? 12 : 4">
                            <v-btn 
                              color="primary" 
                              :block="isMobile"
                              :size="isMobile ? 'large' : 'default'"
                              @click="showMessage('主要按鈕點擊', 'primary')"
                            >
                              主要按鈕
                            </v-btn>
                          </v-col>
                          <v-col :cols="isMobile ? 12 : 4">
                            <v-btn 
                              color="secondary" 
                              :block="isMobile"
                              :size="isMobile ? 'large' : 'default'"
                              @click="showMessage('次要按鈕點擊', 'secondary')"
                            >
                              次要按鈕
                            </v-btn>
                          </v-col>
                          <v-col :cols="isMobile ? 12 : 4">
                            <v-btn 
                              color="success" 
                              :block="isMobile"
                              :size="isMobile ? 'large' : 'default'"
                              @click="showMessage('成功按鈕點擊', 'success')"
                            >
                              成功按鈕
                            </v-btn>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>

                    <!-- 表單測試 -->
                    <v-row class="mt-4">
                      <v-col cols="12">
                        <h4>表單輸入測試</h4>
                        <v-row>
                          <v-col :cols="isMobile ? 12 : 6">
                            <v-text-field
                              v-model="testForm.name"
                              label="姓名"
                              variant="outlined"
                              :density="isMobile ? 'comfortable' : 'default'"
                            />
                          </v-col>
                          <v-col :cols="isMobile ? 12 : 6">
                            <v-text-field
                              v-model="testForm.email"
                              label="電子郵件"
                              type="email"
                              variant="outlined"
                              :density="isMobile ? 'comfortable' : 'default'"
                            />
                          </v-col>
                          <v-col :cols="isMobile ? 12 : 6">
                            <v-select
                              v-model="testForm.category"
                              :items="categories"
                              label="類別"
                              variant="outlined"
                              :density="isMobile ? 'comfortable' : 'default'"
                            />
                          </v-col>
                          <v-col :cols="isMobile ? 12 : 6">
                            <v-textarea
                              v-model="testForm.description"
                              label="描述"
                              variant="outlined"
                              :density="isMobile ? 'comfortable' : 'default'"
                              :rows="isMobile ? 3 : 4"
                            />
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>

                    <!-- 數據表格測試 -->
                    <v-row class="mt-4">
                      <v-col cols="12">
                        <h4>數據表格測試</h4>
                        <v-data-table
                          :headers="isMobile ? mobileTableHeaders : tableHeaders"
                          :items="tableItems"
                          :items-per-page="isMobile ? 5 : 10"
                          :density="isMobile ? 'comfortable' : 'default'"
                          class="elevation-1"
                        >
                          <template v-slot:item.actions="{ item }">
                            <v-btn
                              icon="mdi-pencil"
                              :size="isMobile ? 'small' : 'default'"
                              color="primary"
                              @click="showMessage(`編輯 ${item.name}`, 'info')"
                              class="mr-1"
                            />
                            <v-btn
                              icon="mdi-delete"
                              :size="isMobile ? 'small' : 'default'"
                              color="error"
                              @click="showMessage(`刪除 ${item.name}`, 'error')"
                            />
                          </template>
                        </v-data-table>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>

            <!-- 測試結果 -->
            <v-row class="mt-4">
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-check-circle</v-icon>
                    測試結果
                  </v-card-title>
                  <v-card-text>
                    <v-list>
                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="success">mdi-check</v-icon>
                        </template>
                        <v-list-item-title>響應式布局</v-list-item-title>
                        <v-list-item-subtitle>根據螢幕尺寸自動調整</v-list-item-subtitle>
                      </v-list-item>

                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="success">mdi-check</v-icon>
                        </template>
                        <v-list-item-title>觸摸優化</v-list-item-title>
                        <v-list-item-subtitle>按鈕和輸入框適合觸摸操作</v-list-item-subtitle>
                      </v-list-item>

                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="success">mdi-check</v-icon>
                        </template>
                        <v-list-item-title>Android 樣式</v-list-item-title>
                        <v-list-item-subtitle>Material Design 規範</v-list-item-subtitle>
                      </v-list-item>

                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="success">mdi-check</v-icon>
                        </template>
                        <v-list-item-title>性能優化</v-list-item-title>
                        <v-list-item-subtitle>流暢的動畫和過渡效果</v-list-item-subtitle>
                      </v-list-item>
                    </v-list>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 消息提示 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
      :location="isMobile ? 'bottom' : 'top'"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          關閉
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useDisplay } from 'vuetify'

const { mobile, smAndDown, mdAndUp, name } = useDisplay()

// 響應式數據
const screenWidth = ref(window.innerWidth)
const screenHeight = ref(window.innerHeight)
const touchSupported = ref('ontouchstart' in window)

const testForm = ref({
  name: '',
  email: '',
  category: '',
  description: ''
})

const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

// 計算屬性
const isMobile = computed(() => mobile.value || smAndDown.value)
const isTablet = computed(() => !mobile.value && !mdAndUp.value)
const currentBreakpoint = computed(() => name.value)

// 測試數據
const categories = ['類別A', '類別B', '類別C', '類別D']

const tableHeaders = [
  { title: 'ID', key: 'id', align: 'start' },
  { title: '名稱', key: 'name', align: 'start' },
  { title: '類別', key: 'category', align: 'start' },
  { title: '狀態', key: 'status', align: 'start' },
  { title: '創建日期', key: 'created', align: 'start' },
  { title: '操作', key: 'actions', align: 'center', sortable: false }
]

const mobileTableHeaders = [
  { title: '名稱', key: 'name', align: 'start' },
  { title: '狀態', key: 'status', align: 'start' },
  { title: '操作', key: 'actions', align: 'center', sortable: false }
]

const tableItems = [
  { id: 1, name: '項目A', category: '類別A', status: '活躍', created: '2023-01-01' },
  { id: 2, name: '項目B', category: '類別B', status: '暫停', created: '2023-01-02' },
  { id: 3, name: '項目C', category: '類別C', status: '活躍', created: '2023-01-03' },
  { id: 4, name: '項目D', category: '類別A', status: '完成', created: '2023-01-04' },
  { id: 5, name: '項目E', category: '類別B', status: '活躍', created: '2023-01-05' }
]

// 方法
const showMessage = (message: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}

const updateScreenSize = () => {
  screenWidth.value = window.innerWidth
  screenHeight.value = window.innerHeight
}

// 生命週期
onMounted(() => {
  window.addEventListener('resize', updateScreenSize)
  showMessage('Android Layout 測試頁面已載入', 'info')
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenSize)
})
</script>

<style scoped>
.v-card {
  margin-bottom: 16px;
}

/* Android 特定樣式 */
@media (max-width: 599px) {
  .v-card-title {
    font-size: 18px !important;
    padding: 12px 16px !important;
  }
  
  .v-card-text {
    padding: 8px 16px 16px !important;
  }
}
</style>
