#!/usr/bin/env node

/**
 * Vue3 遷移自動化腳本
 *
 * 此腳本用於：
 * 1. 將 Vue3 測試頁面轉換為測試代碼
 * 2. 分析 Vue2 表單的數據結構和 API 連結
 * 3. 整合數據連結到 Vue3 表單
 * 4. 替換原始 Vue2 表單
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🚀 開始 Vue3 遷移自動化流程...\n')

// 配置
const config = {
  srcPath: path.join(process.cwd(), 'src'),
  pagesPath: path.join(process.cwd(), 'src', 'pages'),
  testsPath: path.join(process.cwd(), 'src', 'tests'),
  backupPath: path.join(process.cwd(), 'backup'),
  routerPath: path.join(process.cwd(), 'src', 'router', 'index.ts')
}

// Vue2 到 Vue3 的表單映射 (主檔明細架構)
const formMappings = [
  {
    // 主檔 (Form) - 單筆記錄新增/編輯
    vue2Form: 'InspectForm.vue',
    vue2List: 'InspectList.vue',
    vue3Test: 'InspectFormVue3Simple.vue',
    vue3FinalForm: 'InspectForm.vue',
    vue3FinalList: 'InspectList.vue',
    name: '品質檢驗記錄',
    apiEndpoint: 'inspects',
    storeModule: 'inspectModule',
    hasDetailItems: true // 是否有明細檔功能
  },
  {
    vue2Form: 'DowngradeForm.vue',
    vue2List: 'DowngradeList.vue',
    vue3Test: 'DowngradeFormVue3Simple.vue',
    vue3FinalForm: 'DowngradeForm.vue',
    vue3FinalList: 'DowngradeList.vue',
    name: '降級異常',
    apiEndpoint: 'downgrades',
    storeModule: 'downgradeModule',
    hasDetailItems: true
  },
  {
    vue2Form: 'ICountForm.vue',
    vue2List: 'ICountList.vue',
    vue3Test: 'ICountFormVue3Simple.vue',
    vue3FinalForm: 'ICountForm.vue',
    vue3FinalList: 'ICountList.vue',
    name: 'QI品檢計數作業',
    apiEndpoint: 'icounts',
    storeModule: 'icountModule',
    hasDetailItems: false
  },
  {
    vue2Form: 'PInventoryOfYarnForm.vue',
    vue2List: 'PInventoryOfYarnList.vue',
    vue3Test: 'PInventoryOfYarnFormVue3Simple.vue',
    vue3FinalForm: 'PInventoryOfYarnForm.vue',
    vue3FinalList: 'PInventoryOfYarnList.vue',
    name: 'Yarn盤點',
    apiEndpoint: 'yarn-inventory',
    storeModule: 'yarnInventoryModule',
    hasDetailItems: true
  },
  {
    vue2Form: 'PInventoryOfCakeForm.vue',
    vue2List: 'PInventoryOfCakeList.vue',
    vue3Test: 'PInventoryOfCakeFormVue3Simple.vue',
    vue3FinalForm: 'PInventoryOfCakeForm.vue',
    vue3FinalList: 'PInventoryOfCakeList.vue',
    name: 'Cake盤點',
    apiEndpoint: 'cake-inventory',
    storeModule: 'cakeInventoryModule',
    hasDetailItems: true
  },
  {
    vue2Form: 'PInventoryOfPackForm.vue',
    vue2List: 'PInventoryOfPackList.vue',
    vue3Test: 'PInventoryOfPackFormVue3Simple.vue',
    vue3FinalForm: 'PInventoryOfPackForm.vue',
    vue3FinalList: 'PInventoryOfPackList.vue',
    name: 'Pack盤點',
    apiEndpoint: 'pack-inventory',
    storeModule: 'packInventoryModule',
    hasDetailItems: true
  },
  {
    vue2Form: 'DisposalForm.vue',
    vue2List: 'DisposalList.vue',
    vue3Test: 'DisposalFormVue3Simple.vue',
    vue3FinalForm: 'DisposalForm.vue',
    vue3FinalList: 'DisposalList.vue',
    name: '報廢異常',
    apiEndpoint: 'disposals',
    storeModule: 'disposalModule',
    hasDetailItems: true
  },
  {
    vue2Form: 'RelabelForm.vue',
    vue2List: 'RelabelList.vue',
    vue3Test: 'RelabelFormVue3Simple.vue',
    vue3FinalForm: 'RelabelForm.vue',
    vue3FinalList: 'RelabelList.vue',
    name: 'Relabel NO MFD',
    apiEndpoint: 'relabels',
    storeModule: 'relabelModule',
    hasDetailItems: false
  }
]

// 主要執行函數
async function runMigration() {
  try {
    console.log('📋 開始 Vue3 遷移流程...')

    // 1. 創建備份
    await createBackup()

    // 2. 生成測試代碼
    await generateTestCode()

    // 3. 分析 Vue2 表單
    const vue2Analysis = await analyzeVue2Forms()

    // 4. 整合數據連結
    await integrateDataConnections(vue2Analysis)

    // 5. 替換表單
    await replaceForms()

    // 6. 更新路由
    await updateRoutes()

    // 7. 生成報告
    generateMigrationReport()

    console.log('\n🎉 Vue3 遷移完成！')

  } catch (error) {
    console.error('❌ 遷移失敗:', error.message)
    console.log('🔄 正在恢復備份...')
    await restoreBackup()
    process.exit(1)
  }
}

// 創建備份
async function createBackup() {
  console.log('\n📦 創建備份...')

  if (!fs.existsSync(config.backupPath)) {
    fs.mkdirSync(config.backupPath, { recursive: true })
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  const backupDir = path.join(config.backupPath, `vue2-backup-${timestamp}`)

  fs.mkdirSync(backupDir, { recursive: true })

  // 備份 Vue2 表單 (主檔和明細檔)
  formMappings.forEach(mapping => {
    // 備份主檔 (Form)
    const vue2FormPath = path.join(config.pagesPath, mapping.vue2Form)
    if (fs.existsSync(vue2FormPath)) {
      const backupPath = path.join(backupDir, mapping.vue2Form)
      fs.copyFileSync(vue2FormPath, backupPath)
      console.log(`✅ 備份主檔: ${mapping.vue2Form}`)
    }

    // 備份明細檔 (List)
    const vue2ListPath = path.join(config.pagesPath, mapping.vue2List)
    if (fs.existsSync(vue2ListPath)) {
      const backupPath = path.join(backupDir, mapping.vue2List)
      fs.copyFileSync(vue2ListPath, backupPath)
      console.log(`✅ 備份明細檔: ${mapping.vue2List}`)
    }
  })

  // 備份路由文件
  if (fs.existsSync(config.routerPath)) {
    fs.copyFileSync(config.routerPath, path.join(backupDir, 'index.ts'))
    console.log('✅ 備份: router/index.ts')
  }

  console.log(`📦 備份完成: ${backupDir}`)
}

// 生成測試代碼
async function generateTestCode() {
  console.log('\n🧪 生成測試代碼...')

  if (!fs.existsSync(config.testsPath)) {
    fs.mkdirSync(config.testsPath, { recursive: true })
  }

  formMappings.forEach(mapping => {
    const vue3TestPath = path.join(config.pagesPath, mapping.vue3Test)
    if (fs.existsSync(vue3TestPath)) {
      const testCode = generateTestCodeFromVue3Form(vue3TestPath, mapping)
      const testFilePath = path.join(config.testsPath, `${mapping.name}.test.ts`)

      fs.writeFileSync(testFilePath, testCode)
      console.log(`✅ 生成測試: ${mapping.name}.test.ts`)
    }
  })
}

// 從 Vue3 表單生成測試代碼
function generateTestCodeFromVue3Form(vue3Path, mapping) {
  const content = fs.readFileSync(vue3Path, 'utf8')

  return `// ${mapping.name} 測試代碼
// 自動生成於 ${new Date().toISOString()}

import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import ${mapping.vue3Final.replace('.vue', '')} from '@/pages/${mapping.vue3Final}'

describe('${mapping.name}', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(${mapping.vue3Final.replace('.vue', '')})
  })

  it('should render correctly', () => {
    expect(wrapper.exists()).toBe(true)
  })

  it('should have form validation', () => {
    // 測試表單驗證功能
    const form = wrapper.find('form')
    expect(form.exists()).toBe(true)
  })

  it('should handle save operation', async () => {
    // 測試保存操作
    const saveButton = wrapper.find('[data-test="save-button"]')
    if (saveButton.exists()) {
      await saveButton.trigger('click')
      // 驗證保存邏輯
    }
  })

  it('should handle form reset', async () => {
    // 測試表單重設
    const resetButton = wrapper.find('[data-test="reset-button"]')
    if (resetButton.exists()) {
      await resetButton.trigger('click')
      // 驗證重設邏輯
    }
  })

  // 根據 Vue3 測試表單的功能自動生成更多測試
  ${generateAdditionalTests(content, mapping)}
})
`
}

// 生成額外的測試
function generateAdditionalTests(content, mapping) {
  const tests = []

  // 檢查是否有明細檔功能
  if (content.includes('detailItems') || content.includes('明細')) {
    tests.push(`
  it('should handle detail items CRUD', async () => {
    // 測試明細檔的新增、編輯、刪除功能
    expect(wrapper.vm.detailItems).toBeDefined()
  })`)
  }

  // 檢查是否有驗證功能
  if (content.includes('validate') || content.includes('rules')) {
    tests.push(`
  it('should validate form fields', async () => {
    // 測試欄位驗證
    const form = wrapper.vm.$refs.form
    if (form) {
      const isValid = await form.validate()
      expect(typeof isValid).toBe('boolean')
    }
  })`)
  }

  return tests.join('\n')
}

// 分析 Vue2 表單
async function analyzeVue2Forms() {
  console.log('\n🔍 分析 Vue2 表單...')

  const analysis = {}

  formMappings.forEach(mapping => {
    // 分析主檔 (Form)
    const vue2FormPath = path.join(config.pagesPath, mapping.vue2Form)
    if (fs.existsSync(vue2FormPath)) {
      const content = fs.readFileSync(vue2FormPath, 'utf8')
      analysis[mapping.vue2Form] = analyzeVue2FormContent(content, mapping)
      console.log(`✅ 分析主檔: ${mapping.vue2Form}`)
    }

    // 分析明細檔 (List)
    const vue2ListPath = path.join(config.pagesPath, mapping.vue2List)
    if (fs.existsSync(vue2ListPath)) {
      const content = fs.readFileSync(vue2ListPath, 'utf8')
      analysis[mapping.vue2List] = analyzeVue2ListContent(content, mapping)
      console.log(`✅ 分析明細檔: ${mapping.vue2List}`)
    }
  })

  return analysis
}

// 分析 Vue2 表單內容 (主檔)
function analyzeVue2FormContent(content, mapping) {
  const analysis = {
    type: 'form',
    dataModel: extractDataModel(content),
    apiCalls: extractApiCalls(content),
    validationRules: extractValidationRules(content),
    storeModule: mapping.storeModule,
    lifecycle: extractLifecycleMethods(content),
    hasDetailItems: mapping.hasDetailItems,
    detailOperations: extractDetailOperations(content)
  }

  return analysis
}

// 分析 Vue2 明細檔內容 (List)
function analyzeVue2ListContent(content, mapping) {
  const analysis = {
    type: 'list',
    dataModel: extractDataModel(content),
    apiCalls: extractApiCalls(content),
    tableHeaders: extractTableHeaders(content),
    storeModule: mapping.storeModule,
    lifecycle: extractLifecycleMethods(content),
    listOperations: extractListOperations(content)
  }

  return analysis
}

// 提取數據模型
function extractDataModel(content) {
  const dataModel = {}

  // 提取 private 屬性
  const privateMatches = content.match(/private\s+(\w+):\s*([^=\n]+)(?:\s*=\s*([^;\n]+))?/g)
  if (privateMatches) {
    privateMatches.forEach(match => {
      const [, name, type, defaultValue] = match.match(/private\s+(\w+):\s*([^=\n]+)(?:\s*=\s*([^;\n]+))?/) || []
      if (name && type) {
        dataModel[name] = {
          type: type.trim(),
          defaultValue: defaultValue ? defaultValue.trim() : undefined
        }
      }
    })
  }

  return dataModel
}

// 提取 API 調用
function extractApiCalls(content) {
  const apiCalls = []

  // 查找 API 調用模式
  const apiMatches = content.match(/\w+Module\.\w+\([^)]*\)/g)
  if (apiMatches) {
    apiCalls.push(...apiMatches)
  }

  return apiCalls
}

// 提取驗證規則
function extractValidationRules(content) {
  const rules = []

  // 查找驗證規則
  const ruleMatches = content.match(/:rules="\[[^\]]+\]"/g)
  if (ruleMatches) {
    rules.push(...ruleMatches)
  }

  return rules
}

// 提取生命週期方法
function extractLifecycleMethods(content) {
  const lifecycle = {}

  const methods = ['mounted', 'created', 'beforeDestroy', 'destroyed']
  methods.forEach(method => {
    const regex = new RegExp(`${method}\\s*\\(\\s*\\)\\s*{([^}]+)}`, 's')
    const match = content.match(regex)
    if (match) {
      lifecycle[method] = match[1].trim()
    }
  })

  return lifecycle
}

// 提取明細檔操作 (Form 中的明細相關功能)
function extractDetailOperations(content) {
  const operations = []

  // 查找明細相關的方法
  const detailMethods = [
    'addInspectline', 'editInspectline', 'deleteInspectline',
    'addDetail', 'editDetail', 'deleteDetail',
    'saveDetail', 'updateDetail'
  ]

  detailMethods.forEach(method => {
    if (content.includes(method)) {
      operations.push(method)
    }
  })

  return operations
}

// 提取表格標題 (List 中的表格配置)
function extractTableHeaders(content) {
  const headers = []

  // 查找 headers 定義
  const headerMatch = content.match(/headers:\s*\[([^\]]+)\]/s)
  if (headerMatch) {
    try {
      // 簡單解析表格標題
      const headerContent = headerMatch[1]
      const titleMatches = headerContent.match(/text:\s*["']([^"']+)["']/g)
      if (titleMatches) {
        titleMatches.forEach(match => {
          const title = match.match(/["']([^"']+)["']/)[1]
          headers.push(title)
        })
      }
    } catch (error) {
      console.warn('解析表格標題時出錯:', error.message)
    }
  }

  return headers
}

// 提取列表操作 (List 中的 CRUD 操作)
function extractListOperations(content) {
  const operations = []

  // 查找列表相關的方法
  const listMethods = [
    'add', 'edit', 'delete', 'view', 'refresh', 'reload',
    'search', 'filter', 'sort', 'export'
  ]

  listMethods.forEach(method => {
    const regex = new RegExp(`\\b${method}\\s*\\(`, 'g')
    if (content.match(regex)) {
      operations.push(method)
    }
  })

  return operations
}

// 執行遷移
if (require.main === module) {
  runMigration().catch(error => {
    console.error('❌ 遷移失敗:', error)
    process.exit(1)
  })
}

// 整合數據連結
async function integrateDataConnections(vue2Analysis) {
  console.log('\n🔗 整合數據連結...')

  formMappings.forEach(mapping => {
    const vue3TestPath = path.join(config.pagesPath, mapping.vue3Test)
    const analysis = vue2Analysis[mapping.vue2]

    if (fs.existsSync(vue3TestPath) && analysis) {
      const vue3Content = fs.readFileSync(vue3TestPath, 'utf8')
      const integratedContent = integrateVue2DataToVue3(vue3Content, analysis, mapping)

      // 寫入整合後的內容到最終文件
      const finalPath = path.join(config.pagesPath, `${mapping.vue3Final.replace('.vue', '')}Integrated.vue`)
      fs.writeFileSync(finalPath, integratedContent)
      console.log(`✅ 整合: ${mapping.name}`)
    }
  })
}

// 將 Vue2 數據整合到 Vue3 表單
function integrateVue2DataToVue3(vue3Content, vue2Analysis, mapping) {
  let integratedContent = vue3Content

  // 1. 替換模擬 API 調用為真實 API
  integratedContent = replaceApiCalls(integratedContent, vue2Analysis, mapping)

  // 2. 整合數據模型
  integratedContent = integrateDataModel(integratedContent, vue2Analysis)

  // 3. 整合驗證規則
  integratedContent = integrateValidationRules(integratedContent, vue2Analysis)

  // 4. 整合生命週期邏輯
  integratedContent = integrateLifecycle(integratedContent, vue2Analysis)

  // 5. 添加真實的 store 導入
  integratedContent = addStoreImports(integratedContent, vue2Analysis)

  return integratedContent
}

// 替換 API 調用
function replaceApiCalls(content, analysis, mapping) {
  // 替換模擬的保存操作
  const mockSavePattern = /\/\/ 模擬保存操作[\s\S]*?await new Promise\(resolve => setTimeout\(resolve, 1000\)\)/g
  const realSaveCall = `
    // 真實保存操作
    if (isEditMode.value) {
      await ${analysis.storeModule}.updateData(formData.value)
    } else {
      await ${analysis.storeModule}.saveData(formData.value)
    }`

  content = content.replace(mockSavePattern, realSaveCall)

  // 替換模擬的載入操作
  const mockLoadPattern = /\/\/ 模擬載入編輯數據[\s\S]*?showMessage\(`載入編輯數據[^`]*`[^)]*\)/g
  const realLoadCall = `
    // 真實載入操作
    const data = await ${analysis.storeModule}.getDataById(route.params.id)
    if (data) {
      formData.value = data
      showMessage('數據載入成功', 'success')
    }`

  content = content.replace(mockLoadPattern, realLoadCall)

  return content
}

// 整合數據模型
function integrateDataModel(content, analysis) {
  // 根據 Vue2 的數據模型更新 Vue3 的 formData 初始值
  const dataModelEntries = Object.entries(analysis.dataModel)
  if (dataModelEntries.length > 0) {
    const formDataPattern = /const formData = ref\({[\s\S]*?}\)/
    const newFormData = generateFormDataFromModel(analysis.dataModel)
    content = content.replace(formDataPattern, `const formData = ref(${newFormData})`)
  }

  return content
}

// 從數據模型生成表單數據
function generateFormDataFromModel(dataModel) {
  const formData = {}

  Object.entries(dataModel).forEach(([key, info]) => {
    if (info.defaultValue) {
      try {
        formData[key] = JSON.parse(info.defaultValue)
      } catch {
        formData[key] = info.defaultValue.replace(/['"]/g, '')
      }
    } else {
      // 根據類型設置默認值
      if (info.type.includes('string')) {
        formData[key] = ''
      } else if (info.type.includes('number')) {
        formData[key] = 0
      } else if (info.type.includes('boolean')) {
        formData[key] = false
      } else if (info.type.includes('[]')) {
        formData[key] = []
      } else {
        formData[key] = null
      }
    }
  })

  return JSON.stringify(formData, null, 2)
}

// 整合驗證規則
function integrateValidationRules(content, analysis) {
  // 這裡可以根據 Vue2 的驗證規則更新 Vue3 的驗證邏輯
  // 由於驗證規則的格式差異較大，這裡保持現有的 Vue3 驗證邏輯
  return content
}

// 整合生命週期邏輯
function integrateLifecycle(content, analysis) {
  if (analysis.lifecycle.mounted) {
    // 將 Vue2 的 mounted 邏輯轉換為 Vue3 的 onMounted
    const vue3MountedLogic = convertVue2ToVue3Lifecycle(analysis.lifecycle.mounted)

    const onMountedPattern = /onMounted\(\(\) => {[\s\S]*?}\)/
    if (content.match(onMountedPattern)) {
      content = content.replace(onMountedPattern, `onMounted(async () => {
  ${vue3MountedLogic}
})`)
    }
  }

  return content
}

// 轉換 Vue2 生命週期邏輯到 Vue3
function convertVue2ToVue3Lifecycle(vue2Logic) {
  let vue3Logic = vue2Logic

  // 替換 this. 為對應的 ref 變量
  vue3Logic = vue3Logic.replace(/this\./g, '')

  // 替換常見的 Vue2 模式
  vue3Logic = vue3Logic.replace(/this\.\$route\.params/g, 'route.params')
  vue3Logic = vue3Logic.replace(/this\.\$router/g, 'router')

  return vue3Logic
}

// 添加 store 導入
function addStoreImports(content, analysis) {
  const importPattern = /import { ref, computed, onMounted } from 'vue'/
  const newImports = `import { ref, computed, onMounted } from 'vue'
import { ${analysis.storeModule} } from '@/store/modules/${analysis.storeModule.replace('Module', '')}'`

  content = content.replace(importPattern, newImports)

  return content
}

// 替換表單
async function replaceForms() {
  console.log('\n🔄 替換表單...')

  formMappings.forEach(mapping => {
    const integratedPath = path.join(config.pagesPath, `${mapping.vue3Final.replace('.vue', '')}Integrated.vue`)
    const finalPath = path.join(config.pagesPath, mapping.vue3Final)

    if (fs.existsSync(integratedPath)) {
      // 備份原始 Vue2 表單（如果還沒備份）
      if (fs.existsSync(finalPath)) {
        const backupPath = path.join(config.pagesPath, `${mapping.vue2.replace('.vue', '')}Vue2Backup.vue`)
        if (!fs.existsSync(backupPath)) {
          fs.copyFileSync(finalPath, backupPath)
        }
      }

      // 替換為整合後的 Vue3 表單
      fs.copyFileSync(integratedPath, finalPath)

      // 清理臨時文件
      fs.unlinkSync(integratedPath)

      console.log(`✅ 替換: ${mapping.vue3Final}`)
    }
  })
}

// 更新路由
async function updateRoutes() {
  console.log('\n🛣️  更新路由...')

  if (fs.existsSync(config.routerPath)) {
    let routerContent = fs.readFileSync(config.routerPath, 'utf8')

    // 移除 Vue3 測試路由的導入
    formMappings.forEach(mapping => {
      const testImportPattern = new RegExp(`const ${mapping.vue3Test.replace('.vue', '')} = \\(\\) => import\\([^)]+\\)`, 'g')
      routerContent = routerContent.replace(testImportPattern, '')
    })

    // 更新路由配置註釋
    routerContent = routerContent.replace(
      '// Vue3 測試組件',
      '// Vue3 組件 (已完成遷移)'
    )

    fs.writeFileSync(config.routerPath, routerContent)
    console.log('✅ 路由更新完成')
  }
}

// 恢復備份
async function restoreBackup() {
  console.log('🔄 恢復備份...')

  const backupDirs = fs.readdirSync(config.backupPath)
    .filter(dir => dir.startsWith('vue2-backup-'))
    .sort()
    .reverse()

  if (backupDirs.length > 0) {
    const latestBackup = path.join(config.backupPath, backupDirs[0])

    // 恢復表單文件
    formMappings.forEach(mapping => {
      const backupFile = path.join(latestBackup, mapping.vue2)
      const targetFile = path.join(config.pagesPath, mapping.vue3Final)

      if (fs.existsSync(backupFile)) {
        fs.copyFileSync(backupFile, targetFile)
      }
    })

    // 恢復路由文件
    const backupRouter = path.join(latestBackup, 'index.ts')
    if (fs.existsSync(backupRouter)) {
      fs.copyFileSync(backupRouter, config.routerPath)
    }

    console.log('✅ 備份恢復完成')
  }
}

// 生成遷移報告
function generateMigrationReport() {
  console.log('\n📊 生成遷移報告...')

  const report = {
    timestamp: new Date().toISOString(),
    migratedForms: formMappings.length,
    status: 'completed',
    forms: formMappings.map(mapping => ({
      name: mapping.name,
      vue2File: mapping.vue2,
      vue3File: mapping.vue3Final,
      status: 'migrated'
    }))
  }

  const reportPath = path.join(process.cwd(), 'migration-report.json')
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

  console.log(`📊 遷移報告已生成: ${reportPath}`)
  console.log(`✅ 成功遷移 ${formMappings.length} 個表單`)
}

module.exports = {
  runMigration,
  formMappings,
  analyzeVue2Forms,
  generateTestCode,
  integrateDataConnections,
  replaceForms,
  updateRoutes
}
