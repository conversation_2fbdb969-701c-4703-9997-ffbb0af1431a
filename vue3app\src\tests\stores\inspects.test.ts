import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useInspectsStore } from '@/stores/inspects'

// Mock API functions
vi.mock('@/utils/backend-api', () => ({
  getData: vi.fn(),
  postData: vi.fn(),
  putData: vi.fn(),
  deleteData: vi.fn()
}))

import { getData, postData, putData, deleteData } from '@/utils/backend-api'

describe('useInspectsStore', () => {
  let store: ReturnType<typeof useInspectsStore>

  beforeEach(() => {
    // 為每個測試創建新的 Pinia 實例
    setActivePinia(createPinia())
    store = useInspectsStore()
    
    // 清除所有 mock
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('初始狀態', () => {
    it('應該有正確的初始狀態', () => {
      expect(store.loading).toBe(false)
      expect(store.inspectId).toBeNull()
      expect(store.inspect).toEqual({})
      expect(store.inspectlines).toEqual([])
      expect(store.employees).toEqual([])
      expect(store.categories).toEqual([])
      expect(store.remarks).toEqual([])
    })

    it('應該有正確的初始分頁設置', () => {
      expect(store.pagination).toEqual({
        page: 1,
        itemsPerPage: 10,
        totalItems: 0,
        totalPages: 0
      })
    })
  })

  describe('計算屬性', () => {
    it('isLoading 應該反映 loading 狀態', () => {
      expect(store.isLoading).toBe(false)
      
      store.setLoading(true)
      expect(store.isLoading).toBe(true)
    })

    it('currentInspect 應該返回當前檢驗記錄', () => {
      const mockInspect = {
        id: 1,
        classDate: '2023-12-01',
        shiftName: '1',
        employId: 123,
        groupName: 'A',
        typeName: 'YARN',
        quantity: 10
      }

      store.setInspect(mockInspect as any)
      expect(store.currentInspect).toEqual(mockInspect)
    })

    it('currentInspectlines 應該返回當前明細記錄', () => {
      const mockInspectlines = [
        { id: 1, codeName: 'TEST001', remarkName: '異常1', quantity: 2 },
        { id: 2, codeName: 'TEST002', remarkName: '異常2', quantity: 3 }
      ]

      store.setInspectlines(mockInspectlines as any)
      expect(store.currentInspectlines).toEqual(mockInspectlines)
    })
  })

  describe('基本操作', () => {
    it('setLoading 應該正確設置載入狀態', () => {
      store.setLoading(true)
      expect(store.loading).toBe(true)

      store.setLoading(false)
      expect(store.loading).toBe(false)
    })

    it('setInspect 應該正確設置檢驗記錄', () => {
      const mockInspect = {
        id: 1,
        classDate: '2023-12-01',
        shiftName: '1'
      }

      store.setInspect(mockInspect as any)
      expect(store.inspect).toEqual(mockInspect)
    })

    it('setEmployees 應該正確設置員工列表', () => {
      const mockEmployees = [
        { id: 1, employId: 1, employNO: 'E001', userName: '張三', employName: 'E001 張三' },
        { id: 2, employId: 2, employNO: 'E002', userName: '李四', employName: 'E002 李四' }
      ]

      store.setEmployees(mockEmployees as any)
      expect(store.employees).toEqual(mockEmployees)
    })
  })

  describe('API 操作', () => {
    describe('getEmployees', () => {
      it('應該成功獲取員工列表', async () => {
        const mockResponse = {
          data: [
            { id: 1, employId: 1, employNO: 'E001', userName: '張三' },
            { id: 2, employId: 2, employNO: 'E002', userName: '李四' }
          ]
        }

        vi.mocked(getData).mockResolvedValue(mockResponse)

        await store.getEmployees()

        expect(getData).toHaveBeenCalledWith('employees/qi')
        expect(store.employees).toHaveLength(2)
        expect(store.employees[0].employName).toBe('E001 張三')
        expect(store.employees[1].employName).toBe('E002 李四')
      })

      it('應該處理獲取員工列表的錯誤', async () => {
        const mockError = new Error('API 錯誤')
        vi.mocked(getData).mockRejectedValue(mockError)

        await expect(store.getEmployees()).rejects.toThrow('API 錯誤')
        expect(store.loading).toBe(false)
      })
    })

    describe('getInspectById', () => {
      it('應該成功獲取檢驗記錄', async () => {
        const mockInspectData = {
          id: 123,
          classDate: '2023-12-01',
          shiftName: '1',
          inspectlines: [
            { id: 1, codeName: 'TEST001', remarkName: '異常1' }
          ]
        }

        const mockResponse = {
          data: [mockInspectData]
        }

        vi.mocked(getData).mockResolvedValue(mockResponse)

        await store.getInspectById(123)

        expect(getData).toHaveBeenCalledWith('inspects/123')
        expect(store.inspect).toEqual(mockInspectData)
        expect(store.inspectlines).toEqual(mockInspectData.inspectlines)
        expect(store.inspectId).toBe(123)
      })

      it('應該處理空響應', async () => {
        const mockResponse = { data: [] }
        vi.mocked(getData).mockResolvedValue(mockResponse)

        await store.getInspectById(123)

        expect(store.inspect).toEqual({})
        expect(store.inspectlines).toEqual([])
      })

      it('應該處理獲取檢驗記錄的錯誤', async () => {
        const mockError = new Error('找不到記錄')
        vi.mocked(getData).mockRejectedValue(mockError)

        await expect(store.getInspectById(123)).rejects.toThrow('找不到記錄')
        expect(store.loading).toBe(false)
      })
    })

    describe('saveInspect', () => {
      it('應該成功創建新檢驗記錄', async () => {
        const mockInspectData = {
          classDate: '2023-12-01',
          shiftName: '1',
          employId: 123,
          groupName: 'A',
          typeName: 'YARN',
          quantity: 10
        }

        const mockResponse = {
          data: { ...mockInspectData, id: 456 }
        }

        vi.mocked(postData).mockResolvedValue(mockResponse)

        const result = await store.saveInspect(mockInspectData as any)

        expect(postData).toHaveBeenCalledWith('inspects', mockInspectData)
        expect(store.inspect).toEqual(mockResponse.data)
        expect(store.inspectId).toBe(456)
        expect(result).toEqual(mockResponse.data)
      })

      it('應該成功更新現有檢驗記錄', async () => {
        const mockInspectData = {
          id: 123,
          classDate: '2023-12-01',
          shiftName: '2',
          employId: 456,
          groupName: 'B',
          typeName: 'CAKE',
          quantity: 20
        }

        const mockResponse = {
          data: mockInspectData
        }

        vi.mocked(putData).mockResolvedValue(mockResponse)

        const result = await store.saveInspect(mockInspectData as any)

        expect(putData).toHaveBeenCalledWith('inspects/123', mockInspectData)
        expect(store.inspect).toEqual(mockInspectData)
        expect(store.inspectId).toBe(123)
        expect(result).toEqual(mockInspectData)
      })

      it('應該處理保存錯誤', async () => {
        const mockError = new Error('保存失敗')
        vi.mocked(postData).mockRejectedValue(mockError)

        const mockData = { classDate: '2023-12-01' }

        await expect(store.saveInspect(mockData as any)).rejects.toThrow('保存失敗')
        expect(store.loading).toBe(false)
      })
    })

    describe('deleteInspect', () => {
      it('應該成功刪除檢驗記錄', async () => {
        // 先設置一些狀態
        store.inspectId = 123
        store.setInspect({ id: 123, classDate: '2023-12-01' } as any)
        store.setInspectlines([{ id: 1, codeName: 'TEST001' }] as any)

        vi.mocked(deleteData).mockResolvedValue({})

        await store.deleteInspect(123)

        expect(deleteData).toHaveBeenCalledWith('inspects/123')
        expect(store.inspect).toEqual({})
        expect(store.inspectlines).toEqual([])
        expect(store.inspectId).toBeNull()
      })

      it('應該處理刪除錯誤', async () => {
        const mockError = new Error('刪除失敗')
        vi.mocked(deleteData).mockRejectedValue(mockError)

        await expect(store.deleteInspect(123)).rejects.toThrow('刪除失敗')
        expect(store.loading).toBe(false)
      })
    })
  })

  describe('明細檔操作', () => {
    beforeEach(() => {
      store.inspectId = 123
    })

    it('應該成功新增明細記錄', async () => {
      const mockDetailData = {
        inspectId: 123,
        codeName: 'TEST001',
        remarkName: '測試異常',
        quantity: 5
      }

      vi.mocked(postData).mockResolvedValue({ data: mockDetailData })
      vi.mocked(getData).mockResolvedValue({ 
        data: [{ 
          id: 123, 
          inspectlines: [mockDetailData] 
        }] 
      })

      const result = await store.addInspectlineToInspect(mockDetailData as any)

      expect(postData).toHaveBeenCalledWith('inspectlines', mockDetailData)
      expect(getData).toHaveBeenCalledWith('inspects/123') // 重新載入
      expect(result).toEqual(mockDetailData)
    })

    it('應該成功更新明細記錄', async () => {
      const mockDetailData = {
        id: 456,
        inspectId: 123,
        codeName: 'TEST002',
        remarkName: '更新異常',
        quantity: 8
      }

      vi.mocked(putData).mockResolvedValue({ data: mockDetailData })
      vi.mocked(getData).mockResolvedValue({ 
        data: [{ 
          id: 123, 
          inspectlines: [mockDetailData] 
        }] 
      })

      const result = await store.updateInspectline(mockDetailData as any)

      expect(putData).toHaveBeenCalledWith('inspectlines/456', mockDetailData)
      expect(getData).toHaveBeenCalledWith('inspects/123') // 重新載入
      expect(result).toEqual(mockDetailData)
    })

    it('應該成功刪除明細記錄', async () => {
      const mockDetailData = { id: 456 }

      vi.mocked(deleteData).mockResolvedValue({})
      vi.mocked(getData).mockResolvedValue({ 
        data: [{ 
          id: 123, 
          inspectlines: [] 
        }] 
      })

      await store.deleteInspectline(mockDetailData as any)

      expect(deleteData).toHaveBeenCalledWith('inspectlines/456')
      expect(getData).toHaveBeenCalledWith('inspects/123') // 重新載入
    })
  })

  describe('resetState', () => {
    it('應該正確重置所有狀態', () => {
      // 先設置一些狀態
      store.setLoading(true)
      store.inspectId = 123
      store.setInspect({ id: 123 } as any)
      store.setInspectlines([{ id: 1 }] as any)
      store.employee = 'test'

      // 重置狀態
      store.resetState()

      // 驗證所有狀態都被重置
      expect(store.items).toEqual([])
      expect(store.inspect).toEqual({})
      expect(store.inspectlines).toEqual([])
      expect(store.inspectId).toBeNull()
      expect(store.employee).toBe('')
      expect(store.loading).toBe(false)
    })
  })
})

// 💡 這個測試文件展示了如何測試 Pinia Store：
// 1. 測試初始狀態
// 2. 測試計算屬性
// 3. 測試同步操作
// 4. 測試異步 API 操作
// 5. 測試錯誤處理
// 6. 使用 Mock 模擬 API 調用

// 🚀 運行這個測試：
// npm run test:unit src/tests/stores/inspects.test.ts
