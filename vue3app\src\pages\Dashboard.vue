<template>
  <v-container id="dashboard" fluid class="mx-0 pa-0">
    <info-box />
    <v-row>
      <v-col cols="12" md="6">
        <v-card elevation="4">
          <monthly-sales
            title="Monthly Sales"
            subtitle="Last 12 months"
          />
        </v-card>
      </v-col>
      <v-col cols="12" md="6">
        <v-card elevation="4">
          <order-forecast
            title="Order Forecast"
            subtitle="Next 6 months"
          />
        </v-card>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12" md="6">
        <v-card elevation="4">
          <product-analysis
            title="Product Analysis"
            subtitle="Top selling products"
          />
        </v-card>
      </v-col>
      <v-col cols="12" md="6">
        <v-card elevation="4">
          <browser-usage />
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import InfoBox from '../components/dashboard/InfoBox.vue'
import DashboardBarChart from '../components/dashboard/DashboardBarChart.vue'
import BrowserUsage from '../components/dashboard/DashboardBrowserUsage.vue'

// Component aliases for template usage
const MonthlySales = DashboardBarChart
const OrderForecast = DashboardBarChart
const ProductAnalysis = DashboardBarChart
</script>
