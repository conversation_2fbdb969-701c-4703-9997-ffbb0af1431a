<template>
  <v-container fluid>
    <v-card class="grey lighten-4 elevation-0">
      <v-form ref="validForm" v-model="formValid" lazy-validation>
        <v-card-title class="title">
          {{ title }}
          <v-spacer></v-spacer>
          <v-btn
            :elevation="4"
            icon
            size="small"
            color="grey"
            class="mr-2"
            @click="cancel()"
          >
            <v-icon>mdi-close-circle-outline</v-icon>
          </v-btn>
          <v-btn
            :elevation="4"
            icon
            size="small"
            color="purple"
            class="mr-2"
            @click="save()"
            :disabled="isSaving || !formValid"
            :loading="isSaving"
          >
            <v-icon>mdi-content-save-all</v-icon>
          </v-btn>
          <v-btn
            :elevation="4"
            icon
            size="small"
            color="blue"
            @click="addTracksheet()"
          >
            <v-icon>mdi-plus</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-container fluid grid-list-md>
            <v-row>
              <v-col md="4" cols="12">
                <v-text-field
                  name="id"
                  label="單號"
                  type="number"
                  hint="ICountID is required"
                  v-model="icount.id"
                  class="input-group--focused"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col md="4" cols="12">
                <v-menu
                  :close-on-content-click="false"
                  v-model="classDateMenu"
                  transition="v-scale-transition"
                  offset-y
                  :nudge-left="40"
                  max-width="290px"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      v-bind="props"
                      label="日期"
                      v-model="icount.classDate"
                      prepend-icon="mdi-calendar"
                      readonly
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="icount.classDate"
                    no-title
                    scrollable
                  >
                  </v-date-picker>
                </v-menu>
              </v-col>
              <v-col md="4" cols="12">
                <v-radio-group
                  name="shiftName"
                  label="勤別"
                  v-model="icount.shiftName"
                  :rules="[value => !!value || '必要!!請選擇']"
                  required
                  row
                >
                  <v-radio label="I" value="1"></v-radio>
                  <v-radio label="II" value="2"></v-radio>
                  <v-radio label="III" value="3"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col md="6" cols="12">
                <v-autocomplete
                  :items="employees"
                  label="人員"
                  item-title="employName"
                  item-value="employId"
                  v-model="icount.employId"
                  :rules="[value => !!value || '必要!!請選擇']"
                  required
                ></v-autocomplete>
              </v-col>
              <v-col md="4" cols="12">
                <v-radio-group
                  name="groupName"
                  label="組別"
                  v-model="icount.groupName"
                  :rules="[value => !!value || '必要!!請選擇']"
                  required
                  row
                >
                  <v-radio label="A" value="A"></v-radio>
                  <v-radio label="B" value="B"></v-radio>
                  <v-radio label="C" value="C"></v-radio>
                  <v-radio label="D" value="D"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col md="4" cols="12">
                <v-text-field
                  name="quantity"
                  label="小計"
                  type="number"
                  v-model="icount.quantity"
                  class="input-group--focused"
                  readonly
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-card>
                  <v-data-table
                    v-if="loading === false"
                    :headers="headers"
                    :items="icount.icountlines"
                    :items-per-page="10"
                    class="elevation-1 colored-pagination"
                  >
                    <template v-slot:item.countdown="{ index }">
                      {{ index + 1 }}
                    </template>
                    <template v-slot:item.actions="{ item }">
                      <v-btn
                        :elevation="4"
                        icon
                        size="x-small"
                        color="red"
                        @click="remove(item)"
                      >
                        <v-icon>mdi-delete</v-icon>
                      </v-btn>
                    </template>
                  </v-data-table>
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
      </v-form>
    </v-card>

    <!-- 明細編輯對話框 -->
    <v-dialog v-model="addTracksheetModal" width="700" persistent>
      <v-form ref="validDetail" v-model="detailValid" lazy-validation>
        <v-card>
          <v-card-title>
            {{ title }}
            <v-spacer></v-spacer>
            <v-card-actions>
              <v-btn
                class="green lighten-1"
                text
                :disabled="!detailValid"
                @click="saveICountline"
              >
                Confirm
              </v-btn>
              <v-btn
                class="orange lighten-1"
                text
                @click="cancelAddTracksheet"
              >
                Cancel
              </v-btn>
            </v-card-actions>
          </v-card-title>
          <v-card-text>
            1.請掃Yarn傳票QRCode 2.輸入個數
            <v-text-field
              ref="qrCodeInput"
              v-model="searchFilter.contain.tracksheetNO"
              append-icon="mdi-magnify"
              label="Yarn傳票QRCode"
              @keydown.enter.prevent="getTracksheet"
              :counter="19"
              :rules="[value => !!value || '必要!!請選擇']"
              required
            ></v-text-field>
            <v-container fluid grid-list-md>
              <v-row>
                <v-col
                  md="6"
                  cols="12"
                  v-for="(item, index) in tracksheet"
                  :key="index"
                >
                  <v-text-field
                    v-model="item.tracksheetNO"
                    label="單號"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.furnaceName"
                    label="爐別"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.twisterNO"
                    label="捻線機號"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.tracksheetTime"
                    label="下機時間"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.productName"
                    label="品種"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    label="品檢T1個數"
                    v-model="item.icountT1Qty"
                  ></v-text-field>
                  <v-text-field
                    label="品檢T2個數"
                    v-model="item.icountT2Qty"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-container>
          </v-card-text>
        </v-card>
      </v-form>
    </v-dialog>

    <!-- 確認對話框 -->
    <v-dialog v-model="dialog" max-width="400">
      <v-card>
        <v-card-title>{{ dialogTitle }}</v-card-title>
        <v-card-text>{{ dialogText }}</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey" variant="text" @click="onCancel">取消</v-btn>
          <v-btn color="error" variant="text" @click="onConfirm">確認</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 訊息提示 -->
    <v-snackbar
      v-if="loading === false"
      :top="true"
      :right="true"
      :timeout="5000"
      :color="mode"
      v-model="snackbar"
    >
      <div class="text-center">
        {{ notice }}
        <v-btn dark text @click="closeSnackbar">Close</v-btn>
      </div>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useICountsStore } from '@/stores/icounts'
import { useAppStore } from '@/stores/app'

const route = useRoute()
const router = useRouter()

// Store 實例
const icountsStore = useICountsStore()
const appStore = useAppStore()

// 響應式數據
const validForm = ref()
const validDetail = ref()
const qrCodeInput = ref()
const formValid = ref(false)
const detailValid = ref(false)

// Vue2 風格的數據結構
const title = ref("")
const type = ref("YARN")
const icountId = ref(0)
const icountlineId = ref(null)
const selectedICountline = ref(null)
const isYarnTrackSheetNO = ref(null)
const query = ref("")
const classDateMenu = ref(false)
const addTracksheetModal = ref(false)
const dialog = ref(false)
const dialogTitle = ref("品檢計數(明細)刪除確認")
const dialogText = ref("刪除該筆記錄?")
const trackId = ref("")
const tracks = ref<string[]>([])
const funo = ref("")

// 表格標題
const headers = ref([
  { title: "序號", key: "countdown", align: "start" as const },
  { title: "傳票單號", key: "tracksheetNO" },
  { title: "爐別", key: "furnaceName" },
  { title: "品種", key: "productName", align: "start" as const },
  { title: "捻線機號", key: "twisterNO", align: "start" as const },
  { title: "下機時間", key: "tracksheetTime", align: "start" as const },
  { title: "品檢T1個數", key: "icountT1Qty" },
  { title: "品檢T2個數", key: "icountT2Qty" },
  { title: "", key: "actions", sortable: false }
])

const searchFilter = ref({ contain: { tracksheetNO: "" } })

// 計算屬性 - 從 store 獲取數據
const employees = computed(() => icountsStore.employees)

// 使用本地響應式數據來避免直接修改 store 的計算屬性
const icount = ref({} as any)

// 監聽 store 的變化並同步到本地
const storeICount = computed(() => icountsStore.currentICount)
const isUpdatingFromStore = ref(false)

watch(storeICount, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0 && !isUpdatingFromStore.value) {
    // 創建深拷貝以避免響應性問題
    icount.value = JSON.parse(JSON.stringify(newValue))
    // 計算個數（明細檔數量）
    if (icount.value.icountlines) {
      icount.value.quantity = icount.value.icountlines.length
    }
  }
}, { immediate: true, deep: true })

const icountlinesWithIndex = computed(() => {
  if (icount.value && icount.value.icountlines) {
    return icount.value.icountlines.map((item, index) => ({
      ...item,
      countdown: index + 1
    }))
  }
  return []
})

const tracksheet = computed(() => icountsStore.tracksheet)
const loading = computed(() => appStore.loading)
const mode = computed(() => appStore.mode)
const snackbar = computed(() => appStore.snackbar)
const notice = computed(() => appStore.notice)

// 方法
const isSaving = ref(false)
const lastSaveTime = ref(0)

// 明細分頁狀態
const detailPagination = ref({
  page: 1,
  limit: 10,
  totalItems: 0,
  totalPages: 0,
  hasNextPage: false,
  hasPrevPage: false
})

const save = async () => {
  const currentTime = Date.now()

  // 如果正在儲存中，直接返回
  if (isSaving.value) {
    console.log('正在儲存中，忽略重複點擊')
    appStore.sendErrorNotice("正在儲存中，請稍候...")
    return
  }

  // 防止短時間內重複點擊（1秒內）
  if (currentTime - lastSaveTime.value < 1000) {
    console.log('短時間內重複點擊，忽略')
    appStore.sendErrorNotice("請勿重複點擊儲存按鈕")
    return
  }

  // 先驗證表單
  const { valid } = await validForm.value.validate()

  if (!valid) {
    appStore.sendErrorNotice("請填寫所有必填欄位!")
    return
  }

  // 檢查必填欄位
  if (!icount.value.shiftName || !icount.value.employId || !icount.value.groupName) {
    appStore.sendErrorNotice("請填寫所有必填欄位!")
    return
  }

  // 設置儲存中狀態和時間戳
  isSaving.value = true
  lastSaveTime.value = currentTime
  console.log('開始儲存，設置 isSaving = true')

  // 準備儲存資料，不自動填入預設值
  const icountData = {
    ...icount.value,
    typeName: icount.value.typeName || "YARN",
    classDate: icount.value.classDate || new Date().toISOString().slice(0, 10)
  }

  console.log('準備保存的資料:', icountData)

  // 設置標誌防止 watch 覆蓋用戶變更
  isUpdatingFromStore.value = true

  try {
    if (!icountData.id || icountData.id <= 0) {
      // 新增模式
      console.log('執行新增模式儲存')
      await icountsStore.saveICount(icountData)

      // 儲存成功後，更新本地的 icount 資料，包含新的 ID
      const savedICount = icountsStore.currentICount
      if (savedICount && savedICount.id) {
        icount.value = { ...icount.value, id: savedICount.id }
        console.log('新增成功，更新本地 ID:', savedICount.id)
      }

      saveRoute()
    } else {
      // 編輯模式
      console.log('執行編輯模式儲存，ID:', icountData.id)
      await icountsStore.saveICount(icountData)
      appStore.sendSuccessNotice("保存成功!")
    }
  } catch (error: any) {
    console.error("Error:", error.message)
    appStore.sendErrorNotice("保存失敗: " + error.message)
  } finally {
    // 重置標誌
    console.log('儲存完成，設置 isSaving = false')
    isSaving.value = false
    setTimeout(() => {
      isUpdatingFromStore.value = false
    }, 100)
  }
}

const saveRoute = () => {
  icountId.value = icountsStore.icountId
  router.push(`/icount/${icountId.value}`)
}

const getICountById = () => {
  icountsStore.getICountById(route.params.id as string)
}

const getTracksheet = async () => {
  const tracksheetNO = searchFilter.value.contain.tracksheetNO
  const trimmedTracksheetNO = tracksheetNO.trim()

  if (!trimmedTracksheetNO) {
    appStore.sendErrorNotice("請輸入追蹤單號!")
    appStore.closeNoticeWithDelay(3000)
    return ""
  }

  isYarnTrackSheetNO.value = /^G\d{12}-\d{1}$/.test(trimmedTracksheetNO) || /^G\d{12}-\d{1}-S\d{2}$/.test(trimmedTracksheetNO)

  if (!isYarnTrackSheetNO.value) {
    appStore.sendErrorNotice("無效的追蹤單號!")
    appStore.closeNoticeWithDelay(3000)
    return ""
  }

  if (isYarnTrackSheetNO.value) {
    trackId.value = trimmedTracksheetNO.slice(0, 19).trim()
    query.value = trimmedTracksheetNO.slice(1, 13)
    funo.value = trimmedTracksheetNO.slice(16, 19)
    tracks.value = [type.value, query.value, trackId.value]

    try {
      const isDuplicate = await icountsStore.getDuplicateICountlineByCode(trackId.value)
      if (isDuplicate) {
        appStore.sendErrorNotice("重複的追蹤單號!")
        appStore.closeNoticeWithDelay(5000)
        return ""
      }
    } catch (error) {
      console.error('檢查重複 QRCode 失敗:', error)
      // 如果檢查失敗，繼續執行（可能是網路問題）
    }

    try {
      await icountsStore.getTracksheetByCode(tracks.value)

      if (!tracksheet.value || tracksheet.value.length === 0) {
        appStore.sendErrorNotice("查無資料!")
        appStore.closeNoticeWithDelay(5000)
        return ""
      } else {
        if (funo.value) {
          tracksheet.value[0].furnaceName = funo.value
        }
        return tracksheet.value
      }
    } catch (error) {
      console.error('獲取 Tracksheet 資料失敗:', error)
      if (error.code === 'ECONNABORTED') {
        appStore.sendErrorNotice("網路連線超時，請稍後再試!")
      } else if (error.response?.status === 404) {
        appStore.sendErrorNotice("查無資料!")
      } else {
        appStore.sendErrorNotice("獲取 Tracksheet 資料失敗，請稍後再試!")
      }
      appStore.closeNoticeWithDelay(5000)
      return ""
    }
  }
}

const cancel = () => {
  router.push({ name: "icounts" })
}

const remove = (item: any) => {
  selectedICountline.value = item
  dialog.value = true
}

const onConfirm = async () => {
  try {
    await icountsStore.deleteICountline(selectedICountline.value)
    selectedICountline.value = null
    // deleteICountline 已經會重新載入數據，不需要再次調用
    dialog.value = false
  } catch (error) {
    console.error('刪除明細失敗:', error)
  }
}

const onCancel = () => {
  selectedICountline.value = null
  dialog.value = false
}

const addTracksheet = async () => {
  // 如果是新增模式且還沒有 ID，先保存主檔
  if (!icount.value.id || icount.value.id <= 0) {
    try {
      const icountData = {
        ...icount.value,
        shiftName: icount.value.shiftName || "1",
        employId: icount.value.employId,
        groupName: icount.value.groupName || "A",
        typeName: icount.value.typeName || "YARN",
        classDate: icount.value.classDate || new Date().toISOString().slice(0, 10)
      }

      await icountsStore.saveICount(icountData)
      // 更新路由到編輯模式
      const newId = icountsStore.icountId
      router.replace(`/icount/${newId}`)
    } catch (error) {
      console.error("保存主檔失敗:", error)
      appStore.sendErrorNotice("請先完成主檔資料並保存")
      return
    }
  }

  addTracksheetModal.value = true
  isYarnTrackSheetNO.value = null
  query.value = ""
  searchFilter.value.contain.tracksheetNO = ""
  icountId.value = icount.value.id
  icountsStore.clearTracksheets()

  nextTick(() => {
    if (validDetail.value) {
      validDetail.value.validate()
    }
    nextTick(() => {
      if (qrCodeInput.value) {
        qrCodeInput.value.focus()
      }
    })
  })
}

const saveICountline = async () => {
  const ICountId = { icountId: icount.value.id }
  const addTracksheetNO = { tracksheetNO: searchFilter.value.contain.tracksheetNO }
  const addTracksheet = tracksheet.value[0]
  const newICountline = {
    ...ICountId,
    ...addTracksheet,
    ...addTracksheetNO
  }

  try {
    await icountsStore.addICountlineToICount(newICountline)
    icountlineId.value = null
    // addICountlineToICount 已經會重新載入數據，不需要再次調用
    resetForm()
  } catch (error) {
    console.error('保存明細失敗:', error)
  }
}

const resetForm = () => {
  isYarnTrackSheetNO.value = null
  query.value = ""
  searchFilter.value.contain.tracksheetNO = ""
  funo.value = ""
  icountsStore.clearTracksheets()
  nextTick(() => {
    if (qrCodeInput.value) {
      qrCodeInput.value.focus()
    }
  })
}

const cancelAddTracksheet = () => {
  addTracksheetModal.value = false
  query.value = ""
  searchFilter.value.contain.tracksheetNO = ""
  icountsStore.clearTracksheets()
  // 不需要重置 icountsStore 或重新載入數據，保持當前狀態
}

const closeSnackbar = () => {
  appStore.closeNotice()
}

// 處理明細分頁變更
const updateDetailOptions = (options: any) => {
  console.log('明細分頁選項變更:', options)

  // 更新分頁狀態
  if (detailPagination.value.page !== options.page || detailPagination.value.limit !== options.itemsPerPage) {
    detailPagination.value.page = options.page
    detailPagination.value.limit = options.itemsPerPage
    // 由於明細資料是本地的，不需要重新載入
  }
}

// 生命週期
onMounted(async () => {
  // 載入基礎數據
  await icountsStore.getEmployees()

  window.scrollTo(0, 0)

  if (route.params.id) {
    title.value = "QI品檢計數作業(明細)"
    icountsStore.resetState()
    await icountsStore.getICountById(route.params.id as string)
  } else {
    title.value = "QI品檢計數作業(新增)"
    // 新增模式：確保完全重置狀態
    icountsStore.resetState()

    // 設置新的空白資料，不設定預設值
    const newICount = {
      id: null,
      typeName: type.value,
      classDate: new Date().toISOString().slice(0, 10),
      shiftName: null,  // 不設定預設值
      employId: null,
      groupName: null,  // 不設定預設值
      quantity: 0,
      icountlines: []
    }

    // 重置本地狀態
    icount.value = { ...newICount }
    icountsStore.setICount(newICount)
    icountsStore.setICountlines([])

    nextTick(() => {
      if (validForm.value) {
        // 先重置驗證，然後觸發驗證以顯示必填提示
        validForm.value.resetValidation()
        setTimeout(() => {
          validForm.value.validate()
        }, 100)
      }
    })
  }
})
</script>

<style scoped>
/* 自定義分頁按鈕顏色 */
:deep(.colored-pagination .v-data-table-footer .v-btn) {
  color: rgb(33, 150, 243) !important; /* 藍色 */
}

:deep(.colored-pagination .v-data-table-footer .v-btn:hover) {
  background-color: rgba(33, 150, 243, 0.1) !important;
}

:deep(.colored-pagination .v-data-table-footer .v-btn.v-btn--active) {
  background-color: rgb(33, 150, 243) !important;
  color: white !important;
}

:deep(.colored-pagination .v-data-table-footer .v-btn.v-btn--disabled) {
  color: rgba(0, 0, 0, 0.26) !important;
}

/* 分頁選擇器樣式 */
:deep(.colored-pagination .v-data-table-footer .v-select) {
  color: rgb(33, 150, 243) !important;
}

:deep(.colored-pagination .v-data-table-footer .v-select .v-field__input) {
  color: rgb(33, 150, 243) !important;
}
</style>
