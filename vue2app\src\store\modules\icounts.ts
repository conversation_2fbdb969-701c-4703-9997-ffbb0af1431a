import { getData, putData, postData, deleteData } from "@/utils/backend-api";
import {
  Employee,
  ICount,
  Entity,
  Tracksheet,
  ICountline
} from "@/types";
import { getDefaultPagination, getPagination } from "@/utils/store-util";
import { appModule } from "./app";
import { get } from "lodash";
import {
  VuexModule,
  Module,
  Mutation,
  Action,
  getModule
} from "vuex-module-decorators";
import store from "@/store";

export interface QICountState {
  items: Entity[];
  pagination: Pagination;
  loading: boolean;
  employee: string;
  icountId: number;
  icount: ICount;
  icountline: ICountline[];
  tracksheet: Tracksheet;
  employees: Employee[];
}

@Module({ store, dynamic: true, name: "icounts" })
class ICountModule extends VuexModule implements QICountState {
  public items: Entity[] = [];
  public pagination = getDefaultPagination();
  public loading = false;
  public employee: "";
  public icountId = null;
  public icount = {} as ICount;
  public icountline: ICountline[] = [];
  public employees: Employee[] = [];
  public tracksheet = {} as Tracksheet;

  @Action
  getEmployees() {
    getData("employees/tw").then(res => {
      if (res.data) {
        const employees = res.data.map((c: Employee) => {
          c.employName = c.employNO + " " + c.userName;
          c.value = c.id;
          return c;
        });
        this.setEmployees(employees);
      }
    });
  }

  @Action
  getAllICounts() {
    this.setLoading(true);
    getData("icounts").then(res => {
      const icounts = res.data;
      this.setDataTable(icounts);
      this.setLoading(false);
    });
  }

  @Action
  getICountById(id: string) {
    if (id) {
      getData("icounts/" + id).then(
        res => {
          const _icount = res.data;
          const icount = _icount[0];
          icount.icountlines.filter(
            (p: ICount) => p !== null && p !== undefined
          );
          icount.quantity = icount.icountlines?.length;
          this.setICount(icount);
          this.setDataTable(icount.icountlines);
        },
        (err: TODO) => {
          console.log(err);
        }
      );
    } else {
      const icount = {} as ICount;
      icount.icountlines = [];
      this.setICount(icount);
      this.setLoading(false);
    }
  }

  @Action
  async geTracksheetById(id: string) {
    try {
      this.setLoading(true);
      if (id) {
        const res = await getData("tracksheet/yarn/" + id);
        const tracksheet = res.data;
        this.setTracksheet(tracksheet);
      } else {
        this.setTracksheet({} as Tracksheet);
      }
    } catch (err) {
        console.log(err);
    } finally {
        this.setLoading(false);
    }
  }

  @Action
  async getDuplicateICountlineByCode(code: string): Promise<boolean> {
    try {
      this.setLoading(true);
      if (code) {
        const res = await getData("icountlines/duplicate/" + code);
        const data = res.data;
        if (data !== undefined && data !== null) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    } catch (err) {
      console.log(err);
      return false;
    } finally {
      this.setLoading(false);
    }
  }

  @Action
  searchICounts(searchQuery: string) {
    getData("icounts" + searchQuery).then(res => {
      const icounts = res.data;
      icounts.forEach((item: ICount) => {
        item.quantity = item.icountlines?.length;
      });
      this.setDataTable(icounts);
      this.setLoading(false);
    });
  }

  @Action
  quickSearch(headers: TableHeader[], qsFilter: SeachQuery): void {
    getData("icounts").then(res => {
      const icounts = res.data.filter((r: TODO) =>
        headers.some((header: TableHeader) => {
          const val = get(r, [header.value]);
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter)) ||
            false
          );
        })
      );
      icounts.forEach((item: ICount) => {
        item.quantity = item.icountlines?.length;
      });
      this.setDataTable(icounts);
      this.setLoading(false);
    });
  }

  @Action
  saveICount(icount: ICount): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!icount.id) {
        postData("icounts/", icount)
          .then(res => {
            const icount = res.data;
            const ICountId = { id: icount[0].id };
            const addICount = { ...ICountId, ...this.icount};
            this.setICount(addICount);
            this.setICountId(addICount.id);
            appModule.sendSuccessNotice("New record has been added.");
            appModule.closeNoticeWithDelay(3000);
            resolve();
          })
          .catch((err: TODO) => {
            console.log(err);
            appModule.sendErrorNotice("Operation failed! Please try again later.");
            appModule.closeNoticeWithDelay(5000);
            reject(err);
          });
      } else {
        putData("icounts/" + icount.id.toString(), icount)
          .then(() => {
            appModule.sendSuccessNotice("The record has been updated.");
            appModule.closeNoticeWithDelay(3000);
            resolve();
          })
          .catch((err: TODO) => {
            console.log(err);
            appModule.sendErrorNotice("Operation failed! Please try again later.");
            appModule.closeNoticeWithDelay(5000);
            reject(err);
          });
      }
    });
  }

  @Action
  addICountlineToICount(icountline: ICountline) {
    if (icountline) {
      this.saveICountline(icountline);
      const icountId = this.icount.id;
      this.getICountById(icountId.toString());
      const newICount = this.icount;
      this.setICount(newICount);
    }
  }

  @Action
  saveICountline(icountline: ICountline) {
    if (!icountline.id) {
      postData("icountlines/", icountline)
        .then(res => {
          const icountline = res.data;
          this.setICountline(icountline);
          appModule.sendSuccessNotice("New record has been added.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    } else {
      putData("icounts/" + icountline.id.toString(), icountline)
        .then(res => {
          const icount = res.data;
          this.setICount(icount);
          appModule.sendSuccessNotice("The record has been updated.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    }
  }

  @Action
  async deleteICount(id: number) {
    try {
      await deleteData(`icounts/${id.toString()}`);
      appModule.sendSuccessNotice("Operation is done.");
      appModule.closeNoticeWithDelay(3000);
    }
    catch (error) {
      console.error(error);
      appModule.sendErrorNotice("Operation failed! Please try again later.");
      appModule.closeNoticeWithDelay(5000);
    }
    finally {
      this.setLoading(false);
    }
  }

  @Action
  deleteICountline(icountline: ICountline) {
    if (icountline) {
      const icountId = this.icount.id;
      const icountlineId = icountline.id;
      const { icountlines } = this.icount;
      icountlines.splice(
        icountlines.findIndex((p: ICountline) => p.id === icountline.id),1
      );
      this.setICountline(icountlines);
      deleteData(`icountlines/${icountlineId.toString()}`)
        .then(() => {
          this.getICountById(icountId.toString());
          const newICount = this.icount;
          this.setICount(newICount);
          appModule.sendSuccessNotice("Operation is done.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    }
  }

  @Action
  clearICountline() {
    this.setLoading(true);
    const ICountline = [];
    this.setICountline(ICountline);
    this.setLoading(false);
  }

  @Action
  setDataTable(items: ICount[]) {
    const pagination = getPagination(items);
    this.setPagination(pagination);
    this.setItems(items);
  }

  @Mutation
  setEmployees(employees: Employee[]) {
    this.employees = employees;
  }

  @Mutation
  setICountId(id: number | null) {
    this.icountId = id;
  }

  @Mutation
  setICount(icount: ICount) {
    this.icount = icount;
  }

  @Mutation
  setICountline(icountline: ICountline[]) {
    this.icountline = icountline;
  }

  @Mutation
  setTracksheet(tracksheet: Tracksheet) {
    this.tracksheet = tracksheet;
  }

  @Mutation
  setItems(icounts: ICount[]) {
    this.items = icounts;
  }

  @Mutation
  setPagination(pagination: Pagination) {
    this.pagination = pagination;
  }

  @Mutation
  setLoading(loading: boolean) {
    this.loading = loading;
  }
}

export const icountModule = getModule(ICountModule);
