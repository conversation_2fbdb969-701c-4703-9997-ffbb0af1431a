#!/usr/bin/env node

/**
 * 簡單Vue3測試啟動腳本
 * 
 * 此腳本啟動一個最簡單的Vue3測試頁面，避免複雜的依賴問題
 */

const { spawn } = require('child_process')
const { platform } = require('os')

console.log('🎯 啟動簡單Vue3測試...\n')

console.log('📋 這個測試將驗證:')
console.log('✅ Vue3基本響應式數據')
console.log('✅ 計算屬性功能')
console.log('✅ 事件處理機制')
console.log('✅ 生命週期鉤子')
console.log('✅ 表單驗證功能')
console.log('')

console.log('🌐 測試URL:')
console.log('─'.repeat(50))
console.log('簡單測試頁面: http://localhost:3000/simple-vue3-test')
console.log('備用端口:     http://localhost:5173/simple-vue3-test')
console.log('')

const isWindows = platform() === 'win32'
const npmCommand = isWindows ? 'npm.cmd' : 'npm'

console.log('🚀 正在啟動開發服務器...')

// 清除緩存並啟動
const devServer = spawn(npmCommand, ['run', 'dev'], {
  stdio: 'inherit',
  shell: true,
  cwd: __dirname,
  env: {
    ...process.env,
    VITE_FORCE_RELOAD: 'true'
  }
})

// 等待服務器啟動後打開瀏覽器
setTimeout(() => {
  console.log('\n🌐 正在打開瀏覽器...')
  
  const urls = [
    'http://localhost:3000/simple-vue3-test',
    'http://localhost:5173/simple-vue3-test',
    'http://localhost:8080/simple-vue3-test'
  ]
  
  const openBrowser = (url) => {
    const start = isWindows ? 'start' : platform() === 'darwin' ? 'open' : 'xdg-open'
    const command = isWindows ? `${start} ${url}` : `${start} "${url}"`
    
    require('child_process').exec(command, (error) => {
      if (error) {
        console.log(`❌ 無法自動打開瀏覽器: ${error.message}`)
        console.log(`請手動訪問: ${url}`)
      } else {
        console.log(`✅ 已打開瀏覽器: ${url}`)
      }
    })
  }
  
  openBrowser(urls[0])
  
  console.log('\n📝 測試指南:')
  console.log('─'.repeat(50))
  console.log('1. 在"輸入您的姓名"欄位中輸入文字')
  console.log('2. 觀察字元數量是否即時更新')
  console.log('3. 填寫電子郵件和電話號碼')
  console.log('4. 點擊"驗證表單"按鈕')
  console.log('5. 點擊"點擊測試"按鈕多次')
  console.log('6. 移動數值滑桿')
  console.log('7. 檢查測試結果區域')
  console.log('')
  
  console.log('🎯 成功標準:')
  console.log('─'.repeat(50))
  console.log('✅ 所有輸入都有即時響應')
  console.log('✅ 計算屬性正確更新')
  console.log('✅ 按鈕點擊有反應')
  console.log('✅ 表單驗證正常運作')
  console.log('✅ 測試結果顯示"所有測試都通過"')
  console.log('')
  
}, 8000) // 等待8秒讓服務器完全啟動

// 處理進程退出
process.on('SIGINT', () => {
  console.log('\n\n👋 正在關閉開發服務器...')
  devServer.kill('SIGINT')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n\n👋 正在關閉開發服務器...')
  devServer.kill('SIGTERM')
  process.exit(0)
})

devServer.on('close', (code) => {
  console.log(`\n開發服務器已關閉，退出代碼: ${code}`)
  process.exit(code)
})

devServer.on('error', (error) => {
  console.error(`❌ 啟動開發服務器失敗: ${error.message}`)
  console.log('\n請嘗試手動執行:')
  console.log('npm run dev')
  console.log('\n然後在瀏覽器中訪問: http://localhost:3000/simple-vue3-test')
  process.exit(1)
})
