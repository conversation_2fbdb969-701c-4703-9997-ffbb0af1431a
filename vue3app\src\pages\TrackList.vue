<template>
  <v-container fluid>
    <v-flex xs12>
      <v-card>
        <v-card-title>
          <span class="title"
            >{{title}} {{ pagination ? "(" + pagination.totalItems + ")" : "" }}
          </span>
          <v-spacer></v-spacer>
          <table-header-buttons :reloadData="clearData"></table-header-buttons>
        </v-card-title>
        <v-card-text>
          <v-text-field
            v-model="searchFilter.contain.twisterNO"
            append-icon="mdi-magnify"
            label="Search"
            @change="getTrack"
            counter="14"
            variant="outlined"
            single-line
            hide-details
          ></v-text-field>
        </v-card-text>
        <Table
          v-if="loading === false"
          :headers="headers"
          :items="items"
          :pagination="pagination"
          :setSearch="false"
          :setEdit="false"
          :setRemove="false"
          :disableSort="true"
        ></Table>
      </v-card>
    </v-flex>
    <search-panel
      :rightDrawer="rightDrawer"
      @cancelSearch="cancelSearch"
      @searchData="searchTracks"
    >
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="productName"
            label="Track"
            variant="outlined"
            v-model="searchFilter.contain.productName"
          ></v-text-field>
        </v-flex>
      </v-layout>
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="minUnitPrice"
            type="number"
            label="Min Price"
            variant="outlined"
            v-model="searchFilter.greaterThanOrEqual.unitPrice"
          ></v-text-field>
        </v-flex>
      </v-layout>
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="maxUnitPrice"
            type="number"
            label="Max Price"
            variant="outlined"
            v-model="searchFilter.lessThanOrEqual.unitPrice"
          ></v-text-field>
        </v-flex>
      </v-layout>
    </search-panel>
    <confirm-dialog
      :dialog="dialog"
      :dialogTitle="dialogTitle"
      :dialogText="dialogText"
      @onConfirm="onConfirm"
      @onCancel="onCancel"
    ></confirm-dialog>
    <v-snackbar
      v-if="loading === false"
      location="top end"
      :timeout="5000"
      :color="mode"
      :model-value="snackbar"
      @update:model-value="updateSnackbar"
    >
      {{ notice }}
      <template v-slot:actions>
        <v-btn variant="text" @click="closeSnackbar">Close</v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-facing-decorator'
import { useRouter } from 'vue-router'
import Table from '@/components/Table.vue'
import TableHeaderButtons from '@/components/TableHeaderButtons.vue'
import SearchPanel from '@/components/SearchPanel.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import { debounce } from 'lodash'
import {
  buildSearchFilters,
  buildJsonServerQuery,
  clearSearchFilters
} from '@/utils/app-util'
import { trackModule } from '@/store/modules/tracks'
import { appModule } from '@/store/modules/app'

interface Header {
  text: string
  left?: boolean
  value: string
  sortable?: boolean
}

@Component({
  name: 'TrackList',
  components: {
    Table,
    TableHeaderButtons,
    SearchPanel,
    ConfirmDialog
  }
})
export default class TrackList extends Vue {
  private router = useRouter()
  public dialog = false
  public dialogTitle = "捻線機台刪除確認"
  public dialogText = "確定要刪除此記錄?"
  public showSearchPanel = false
  public right = true
  public search = ""
  public headers: Header[] = [
    { text: "Twister NO", value: "twisterNO" },
    { text: "掃瞄日期時間", left: true, value: "trackTime" },
    { text: "爐別", left: true, value: "furnaceName" },
    { text: "品種", left: true, value: "productName" },
    { text: "掃瞄T1個數", value: "trackT1Qty" },
    { text: "掃瞄T2個數", value: "trackT2Qty" },
    { text: "", value: "actions", sortable: false }
  ]

  private searchFilter = {
    contain: {
      twisterNO: "",
      productName: ""
    },
    greaterThanOrEqual: {
      unitPrice: 0
    },
    lessThanOrEqual: {
      unitPrice: 0
    }
  }

  private title = ""
  private trackId = ""
  private query = ""
  private timeout = 5000
  private color = ""
  private quickSearchFilter = ""
  private itemId = -1

  print() {
    window.print()
  }

  edit(item: any) {
    this.router.push({
      name: "Track",
      params: { id: item.id }
    })
  }

  add() {
    this.router.push("NewTrack")
  }

  remove(item: any) {
    this.itemId = item.id
    this.dialog = true
  }

  async onConfirm() {
    await trackModule.deleteTrack(this.itemId)
    this.dialog = false
  }

  onCancel() {
    this.itemId = -1
    this.dialog = false
  }

  async getTrack() {
    buildSearchFilters(this.searchFilter)
    this.query = this.searchFilter.contain.twisterNO
    if (this.query) {
      await trackModule.getTrackById(this.query)
      this.query = ""
      return trackModule.track
    }
    return ""
  }

  searchTracks() {
    this.showSearchPanel = !this.showSearchPanel
    buildSearchFilters(this.searchFilter)
    this.query = buildJsonServerQuery(this.searchFilter)
    this.quickSearch = ""
    trackModule.searchTracks(this.query)
  }

  clearSearchFilters() {
    this.showSearchPanel = !this.showSearchPanel
    clearSearchFilters(this.searchFilter)
    trackModule.getAllTracks()
  }

  reloadData() {
    this.query = ""
    trackModule.getAllTracks()
  }

  clearData() {
    this.query = ""
    this.searchFilter.contain.twisterNO = ""
    trackModule.clearTracks()
  }

  updateSearchPanel() {
    this.rightDrawer = !this.rightDrawer
  }

  cancelSearch() {
    this.showSearchPanel = false
  }

  closeSnackbar() {
    appModule.closeNotice()
  }

  updateSnackbar(value: boolean) {
    if (!value) {
      appModule.closeNotice()
    }
  }

  quickSearchTracks = debounce(function(this: TrackList) {
    trackModule.quickSearch(this.headers, this.quickSearchFilter)
  }, 500)

  get items() {
    return trackModule.items
  }

  get pagination() {
    return trackModule.pagination
  }

  get loading() {
    return appModule.loading
  }

  get mode() {
    return appModule.mode
  }

  get snackbar() {
    return appModule.snackbar
  }

  get notice() {
    return appModule.notice
  }

  get rightDrawer() {
    return this.showSearchPanel
  }

  set rightDrawer(value: boolean) {
    this.showSearchPanel = value
  }

  get quickSearch() {
    return this.quickSearchFilter
  }

  set quickSearch(val: string) {
    this.quickSearchFilter = val
    if (this.quickSearchFilter) {
      this.quickSearchTracks()
    }
  }

  created() {
    trackModule.clearTracks()
  }

  mounted() {
    this.$nextTick(() => {
      this.title = "捻線機台查詢"
    })
  }
}
</script>
