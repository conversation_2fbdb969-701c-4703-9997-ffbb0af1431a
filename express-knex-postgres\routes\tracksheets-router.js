const router = require("express").Router();
const moment = require('moment');

const tracksheetsDB = require("../models/tracksheets-model.js");

// SEARCH TRACKSHEETS
router.post("/search", async (req, res) => {
  try {
    const searchQuery = req.body;
    const tracksheets = await tracksheetsDB.searchTracksheets(searchQuery);
    
    const transformedTracksheets = tracksheets.map(mem => {
      if (mem.tracksheetTime) {
        mem.tracksheetTime = moment(mem.tracksheetTime).format("YYYY-M-D HH:mm");
      }
      
      if (mem.furnaceName && !mem.furnaceName.startsWith('S')) {
        const furnaceNumber = parseInt(mem.furnaceName);
        mem.furnaceName = !isNaN(furnaceNumber) && furnaceNumber >= 1 && furnaceNumber <= 99
          ? `S${furnaceNumber.toString().padStart(2, '0')}`
          : '';
      }
      
      return mem;
    });

    // Calculate totals for QI tracksheets
    if (tracksheets.length > 0 && tracksheets[0].icountT1Qty !== undefined) {
      const totalIcountT1Sum = transformedTracksheets.reduce((acc, mem) => acc + (mem.icountT1Qty || 0), 0);
      const totalIcountT2Sum = transformedTracksheets.reduce((acc, mem) => acc + (mem.icountT2Qty || 0), 0);
      const totalQuantity = transformedTracksheets.length;
      
      transformedTracksheets.forEach(mem => {
        mem.icountT1Sum = totalIcountT1Sum;
        mem.icountT2Sum = totalIcountT2Sum;
        mem.quantity = totalQuantity;
      });
    }

    res.status(200).json(transformedTracksheets);
  } catch (err) {
    console.error('Search error:', err);
    res.status(500).json({ err: err.message });
  }
});

// GET CAKE TRACKSHEET BY ID
router.get("/cake/:id", async (req, res) => {
  const tracksheetId = req.params.id;
  try {
    const tracksheet = await tracksheetsDB.findTrackSheetByFFCode(tracksheetId);
    if (!tracksheet) {
      return res.status(404).json({ err: "The specified id does not exist" });
    }
    const transformedTracksheet = tracksheet.map(mem => {
      mem.tracksheetTime = moment(mem.tracksheetTime).format("YYYY-M-D HH:mm");      
      if (!mem.furnaceName.startsWith('S')) {
        const furnaceNumber = parseInt(mem.furnaceName);
        mem.furnaceName = !isNaN(furnaceNumber) && furnaceNumber >= 1 && furnaceNumber <= 99
          ? `S${furnaceNumber.toString().padStart(2, '0')}`
          : '';
      }
      return mem;
    });
    res.status(200).json(transformedTracksheet);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET YARN TRACKSHEET BY ID
router.get("/yarn/:id", async (req, res) => {
  const tracksheetId = req.params.id;
  try {
    const tracksheet = await tracksheetsDB.findTrackSheetByTWCode(tracksheetId);
    if (!tracksheet) {
      res.status(404).json({ err: "The specified id does not exist" });
    } else {
      res.status(200).json(tracksheet);
    }
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET PACK TRACKSHEET BY ID
router.get("/pack/:id", async (req, res) => {
  const tracksheetId = req.params.id;
  try {
    const tracksheet = await tracksheetsDB.findTrackSheetByTWCode(tracksheetId);
    if (!tracksheet) {
      res.status(404).json({ err: "The specified id does not exist" });
    } else {
      res.status(200).json(tracksheet);
    }
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET QI TRACKSHEET BY ID
router.get("/qi/:id", async (req, res) => {
  const tracksheetId = req.params.id;
  try {
    const tracksheet = await tracksheetsDB.findTrackSheetByQICode(tracksheetId);
    if (!tracksheet) {
      return res.status(404).json({ err: "The specified id does not exist" });
    }   
    const transformedTracksheet = tracksheet.map(mem => {
      mem.tracksheetTime = moment(mem.tracksheetTime).format("YYYY-M-D HH:mm");      
      if (!mem.furnaceName.startsWith('S')) {
        const furnaceNumber = parseInt(mem.furnaceName);
        mem.furnaceName = !isNaN(furnaceNumber) && furnaceNumber >= 1 && furnaceNumber <= 99
          ? `S${furnaceNumber.toString().padStart(2, '0')}`
          : '';
      }
      return mem;
    });
    const totalIcountT1Sum = transformedTracksheet.reduce((acc, mem) => acc + mem.icountT1Qty, 0);
    const totalIcountT2Sum = transformedTracksheet.reduce((acc, mem) => acc + mem.icountT2Qty, 0);
    const totalQuantity = transformedTracksheet.length;
    transformedTracksheet.forEach(mem => {
      mem.icountT1Sum = totalIcountT1Sum;
      mem.icountT2Sum = totalIcountT2Sum;
      mem.quantity = totalQuantity;
    });
    res.status(200).json(transformedTracksheet);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;
