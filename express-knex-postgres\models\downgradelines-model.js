const db = require("../config/dbConfig.js");
const {infoLogger,errorLogger} = require("../config/winston.js");

// GET SPECIFIC DOWNGRADELINE BY ID
const findById = id => {
  return db("aits_downgradelines")
    .select({
    id: "aits_downgradelines.id",
    downgradeId: "aits_downgradelines.downgradeid",
    furnaceName: "aits_downgradelines.furnacename",
    productName: "aits_downgradelines.productname",
    gradeName: "aits_downgradelines.gradename",
    categoryId: "aits_downgradelines.categoryid",
    remarkId: "aits_downgradelines.remarkid",
    workDate: "aits_downgradelines.workdate",
    twisterNO: "aits_downgradelines.twisterno",
    spindleNO: "aits_downgradelines.spindleno",
    codeName: "aits_downgradelines.codename",
    m_product_id: "aits_downgradelines.m_product_id",
    m_twqrcodeline_id: "aits_downgradelines.m_twqrcodeline_id",
    created: "aits_downgradelines.created"
    })
    .where("aits_downgradelines.downgradeid", id)
    .orderBy("id", "desc") 
    .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
    });
};

// GET SPECIFIC DOWNGRADELINE BY CODE
const findByCode = code => {
  return db("aits_downgradelines")
    .select({
    codeName: "aits_downgradelines.codename"    
    }) 
    .where(db.raw("TRIM(aits_downgradelines.codename)"), code)
    .catch(err => {
      console.error(err);
      errorLogger.error(err);
      throw err;
  });
};

// ADD A DOWNGRADELINE
const addDowngradeline = (downgradeline,newProductId) => {
  return db.transaction(trx => {
    return trx("aits_downgradelines")
    .insert({
      downgradeid: downgradeline.downgradeId,
      furnacename: downgradeline.furnaceName,
      productname: downgradeline.productName,
      gradename: downgradeline.gradeName,
      categoryid: downgradeline.categoryId,
      remarkid: downgradeline.remarkId,
      workdate: downgradeline.workDate,
      twisterno: downgradeline.twisterNO,
      spindleno: downgradeline.spindleNO,
      codename: downgradeline.codeName,      
      m_product_id: downgradeline.m_product_id,
      m_twqrcodeline_id: downgradeline.m_twqrcodeline_id,
      created: db.fn.now()
    }, "id")
    .then(() => {
      return trx("m_twqrcodeline")
        .where("m_twqrcodeline_id", downgradeline.m_twqrcodeline_id)
        .update({ 
          m_product_id: newProductId[0].m_product_id,
          updated: db.fn.now()
        });
      })
      .then(trx.commit)
      .catch(trx.rollback);
    })  
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

// REMOVE DOWNGRADELINE
const removeDowngradeline = id => {
  let result;
  return db.transaction(trx => {
    return db("aits_downgradelines")
      .where("id", id)
      .then(downgradeline => {
        result = downgradeline;
        const twqrcodelineIds = downgradeline.map(downgradeline => downgradeline.m_twqrcodeline_id);
        return trx("m_twqrcodeline")
          .whereIn("m_twqrcodeline_id", twqrcodelineIds)
          .update({ 
            m_product_id: downgradeline[0].m_product_id,
            updated: db.fn.now()
          })
          .then(() => {
            return trx("aits_downgradelines")
              .where("id", id)
              .del();
          });
      })
      .then(trx.commit)
      .catch(trx.rollback);
      })
  .then(() => { 
    infoLogger.info(`remove downgradeline content: ${JSON.stringify(result)}`)
  })
  .catch(err => {
    console.error(err);
    errorLogger.error(err);
    throw err;
  });
};

module.exports = {
  findById,
  findByCode,
  addDowngradeline,
  removeDowngradeline
};
