import axios, { InternalAxiosRequestConfig } from 'axios'

const API_URL = process.env.NODE_ENV === 'production'
  ? import.meta.env.VITE_PROD_SERVER_URI || ''
  : import.meta.env.VITE_DEV_SERVER_URI || ''

const instance = axios.create({
  baseURL: API_URL,
  timeout: 10000, // 增加到 10 秒
  headers: {
    'Content-Type': 'application/json'
  }
})

// Setup interceptors
instance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers = config.headers || {}
      config.headers.Authorization = 'Bearer ' + token
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

instance.interceptors.response.use(
  response => response,
  error => {
    if (error.response && error.response.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// API functions
export const getData = (action: string) => {
  let url = `${API_URL}`
  url += action
  return instance.get(url)
}

export const postData = (action: string, data: any) => {
  let url = `${API_URL}`
  url += action
  return instance.post(url, data)
}

export const putData = (action: string, data: any) => {
  let url = `${API_URL}`
  url += action
  return instance.put(url, data)
}

export const deleteData = (action: string) => {
  let url = `${API_URL}`
  url += action
  return instance.delete(url)
}

export const login = (action: string, data: any) => {
  let url = `${API_URL}`
  url += action
  return instance.post(url, data)
}
