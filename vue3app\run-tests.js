#!/usr/bin/env node

/**
 * 🧪 測試運行腳本
 * 這個腳本幫助你快速運行各種測試命令
 */

const { spawn } = require('child_process')
const path = require('path')

// 顏色輸出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    colorLog('cyan', `\n🚀 執行命令: ${command} ${args.join(' ')}`)
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    })

    child.on('close', (code) => {
      if (code === 0) {
        colorLog('green', `✅ 命令執行成功`)
        resolve(code)
      } else {
        colorLog('red', `❌ 命令執行失敗，退出碼: ${code}`)
        reject(new Error(`Command failed with exit code ${code}`))
      }
    })

    child.on('error', (error) => {
      colorLog('red', `❌ 命令執行錯誤: ${error.message}`)
      reject(error)
    })
  })
}

async function showMenu() {
  colorLog('bright', '\n🧪 Vue 3 測試運行器')
  colorLog('bright', '='.repeat(50))
  
  console.log('\n請選擇要執行的測試：')
  console.log('1. 運行所有測試')
  console.log('2. 運行測試並生成覆蓋率報告')
  console.log('3. 監視模式運行測試')
  console.log('4. 運行範例測試')
  console.log('5. 運行 Store 測試')
  console.log('6. 運行 Composables 測試')
  console.log('7. 運行工具函數測試')
  console.log('8. 運行測試 UI')
  console.log('9. 檢查測試配置')
  console.log('0. 退出')
  
  console.log('\n輸入選項編號：')
}

async function handleChoice(choice) {
  try {
    switch (choice) {
      case '1':
        colorLog('blue', '📊 運行所有測試...')
        await runCommand('npm', ['run', 'test:unit'])
        break
        
      case '2':
        colorLog('blue', '📈 運行測試並生成覆蓋率報告...')
        await runCommand('npm', ['run', 'test:unit', '--', '--coverage'])
        colorLog('green', '📊 覆蓋率報告已生成在 coverage/ 目錄')
        break
        
      case '3':
        colorLog('blue', '👀 啟動監視模式...')
        colorLog('yellow', '💡 提示：文件變更時會自動重新運行測試，按 q 退出')
        await runCommand('npm', ['run', 'test:unit', '--', '--watch'])
        break
        
      case '4':
        colorLog('blue', '🎯 運行範例測試...')
        await runCommand('npm', ['run', 'test:unit', 'src/tests/example.test.ts'])
        break
        
      case '5':
        colorLog('blue', '🏪 運行 Store 測試...')
        await runCommand('npm', ['run', 'test:unit', 'src/tests/stores/'])
        break
        
      case '6':
        colorLog('blue', '🔧 運行 Composables 測試...')
        await runCommand('npm', ['run', 'test:unit', 'src/tests/composables/'])
        break
        
      case '7':
        colorLog('blue', '🛠️ 運行工具函數測試...')
        await runCommand('npm', ['run', 'test:unit', 'src/tests/utils/'])
        break
        
      case '8':
        colorLog('blue', '🖥️ 啟動測試 UI...')
        colorLog('yellow', '💡 提示：將在瀏覽器中打開測試界面')
        await runCommand('npm', ['run', 'test:unit', '--', '--ui'])
        break
        
      case '9':
        colorLog('blue', '⚙️ 檢查測試配置...')
        await checkTestSetup()
        break
        
      case '0':
        colorLog('green', '👋 再見！')
        process.exit(0)
        break
        
      default:
        colorLog('red', '❌ 無效選項，請重新選擇')
        break
    }
  } catch (error) {
    colorLog('red', `❌ 執行失敗: ${error.message}`)
  }
}

async function checkTestSetup() {
  colorLog('cyan', '\n🔍 檢查測試環境設置...')
  
  const fs = require('fs')
  const checks = [
    {
      name: 'package.json 中的測試腳本',
      check: () => {
        const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'))
        return pkg.scripts && pkg.scripts['test:unit']
      }
    },
    {
      name: 'vitest.config.ts 配置文件',
      check: () => fs.existsSync('vitest.config.ts')
    },
    {
      name: '測試設置文件',
      check: () => fs.existsSync('src/tests/setup.ts')
    },
    {
      name: '範例測試文件',
      check: () => fs.existsSync('src/tests/example.test.ts')
    },
    {
      name: 'Store 測試文件',
      check: () => fs.existsSync('src/tests/stores/inspects.test.ts')
    },
    {
      name: 'Vitest 依賴',
      check: () => {
        const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'))
        return pkg.devDependencies && pkg.devDependencies.vitest
      }
    },
    {
      name: 'Vue Test Utils 依賴',
      check: () => {
        const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'))
        return pkg.devDependencies && pkg.devDependencies['@vue/test-utils']
      }
    }
  ]
  
  let allPassed = true
  
  for (const check of checks) {
    const passed = check.check()
    const status = passed ? '✅' : '❌'
    const color = passed ? 'green' : 'red'
    colorLog(color, `${status} ${check.name}`)
    
    if (!passed) {
      allPassed = false
    }
  }
  
  if (allPassed) {
    colorLog('green', '\n🎉 測試環境設置完整！')
    colorLog('yellow', '💡 你可以開始運行測試了')
  } else {
    colorLog('red', '\n⚠️ 測試環境設置不完整')
    colorLog('yellow', '💡 請檢查缺失的文件或依賴')
  }
  
  // 顯示測試文件統計
  colorLog('cyan', '\n📊 測試文件統計：')
  try {
    const testFiles = getAllTestFiles('src/tests')
    colorLog('blue', `📁 找到 ${testFiles.length} 個測試文件：`)
    testFiles.forEach(file => {
      console.log(`   📄 ${file}`)
    })
  } catch (error) {
    colorLog('yellow', '⚠️ 無法統計測試文件')
  }
}

function getAllTestFiles(dir) {
  const fs = require('fs')
  const path = require('path')
  
  if (!fs.existsSync(dir)) {
    return []
  }
  
  let files = []
  const items = fs.readdirSync(dir)
  
  for (const item of items) {
    const fullPath = path.join(dir, item)
    const stat = fs.statSync(fullPath)
    
    if (stat.isDirectory()) {
      files = files.concat(getAllTestFiles(fullPath))
    } else if (item.endsWith('.test.ts') || item.endsWith('.test.js')) {
      files.push(fullPath)
    }
  }
  
  return files
}

// 主程序
async function main() {
  const readline = require('readline')
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  })
  
  while (true) {
    await showMenu()
    
    const choice = await new Promise(resolve => {
      rl.question('', resolve)
    })
    
    await handleChoice(choice.trim())
    
    if (choice.trim() === '0') {
      break
    }
    
    // 等待用戶按鍵繼續
    await new Promise(resolve => {
      rl.question('\n按 Enter 鍵繼續...', resolve)
    })
  }
  
  rl.close()
}

// 如果直接運行此腳本
if (require.main === module) {
  main().catch(error => {
    colorLog('red', `❌ 程序錯誤: ${error.message}`)
    process.exit(1)
  })
}

module.exports = { runCommand, checkTestSetup }
