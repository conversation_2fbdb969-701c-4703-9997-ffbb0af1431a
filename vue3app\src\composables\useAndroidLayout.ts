/**
 * Android Layout Composable
 * 提供統一的Android優化功能，所有表單都可以使用
 */

import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useDisplay } from 'vuetify'
import { getCurrentConfig, mergeConfig, type AndroidLayoutConfig } from '@/config/android-layout-config'

export interface UseAndroidLayoutOptions {
  // 自定義配置覆蓋
  config?: Partial<AndroidLayoutConfig>
  
  // 是否啟用設備信息顯示
  showDeviceInfo?: boolean
  
  // 是否啟用自動聚焦
  enableAutoFocus?: boolean
  
  // 自定義載入時間
  customLoadingTime?: {
    mobile: number
    desktop: number
  }
}

export const useAndroidLayout = (options: UseAndroidLayoutOptions = {}) => {
  // 獲取基礎配置
  const baseConfig = getCurrentConfig()
  const config = options.config ? mergeConfig(baseConfig, options.config) : baseConfig
  
  // Vuetify display composable
  const { mobile, smAndDown, mdAndUp, name, width, height } = useDisplay()
  
  // 響應式數據
  const screenWidth = ref(window.innerWidth)
  const screenHeight = ref(window.innerHeight)
  const touchSupported = ref('ontouchstart' in window)
  const showDevInfo = ref(options.showDeviceInfo ?? config.features.deviceInfo)
  
  // 計算屬性 - 設備類型檢測
  const isMobile = computed(() => {
    if (!config.enabled) return false
    return mobile.value || width.value < config.device.mobileBreakpoint
  })
  
  const isTablet = computed(() => {
    if (!config.enabled) return false
    return !isMobile.value && width.value < config.device.tabletBreakpoint
  })
  
  const isDesktop = computed(() => {
    if (!config.enabled) return false
    return width.value >= config.device.desktopBreakpoint
  })
  
  const currentBreakpoint = computed(() => name.value)
  
  const deviceType = computed(() => {
    if (isMobile.value) return 'mobile'
    if (isTablet.value) return 'tablet'
    return 'desktop'
  })
  
  // 計算屬性 - 布局設定
  const layoutConfig = computed(() => {
    const device = deviceType.value
    return config.layout[device as keyof typeof config.layout]
  })
  
  // 計算屬性 - 組件設定
  const buttonConfig = computed(() => {
    const device = isMobile.value ? 'mobile' : 'desktop'
    return config.components.button[device]
  })
  
  const textFieldConfig = computed(() => {
    const device = isMobile.value ? 'mobile' : 'desktop'
    return config.components.textField[device]
  })
  
  const cardConfig = computed(() => {
    const device = isMobile.value ? 'mobile' : 'desktop'
    return config.components.card[device]
  })
  
  const dataTableConfig = computed(() => {
    const device = isMobile.value ? 'mobile' : 'desktop'
    return config.components.dataTable[device]
  })
  
  const snackbarConfig = computed(() => {
    const device = isMobile.value ? 'mobile' : 'desktop'
    return config.components.snackbar[device]
  })
  
  // 計算屬性 - CSS類別
  const containerClass = computed(() => {
    const classes = [layoutConfig.value.containerPadding]
    if (isMobile.value && config.styles.cssClasses.mobileContainer) {
      classes.push(config.styles.cssClasses.mobileContainer)
    }
    return classes.join(' ')
  })
  
  const cardClass = computed(() => {
    const classes = [layoutConfig.value.cardMargin]
    if (isMobile.value && config.styles.cssClasses.mobileCard) {
      classes.push(config.styles.cssClasses.mobileCard)
    }
    return classes.join(' ')
  })
  
  const buttonClass = computed(() => {
    const classes = []
    if (isMobile.value && config.styles.cssClasses.mobileButton) {
      classes.push(config.styles.cssClasses.mobileButton)
    }
    return classes.join(' ')
  })
  
  const textFieldClass = computed(() => {
    const classes = []
    if (isMobile.value && config.styles.cssClasses.mobileTextField) {
      classes.push(config.styles.cssClasses.mobileTextField)
    }
    return classes.join(' ')
  })
  
  const tableClass = computed(() => {
    const classes = []
    if (isMobile.value && config.styles.cssClasses.mobileTable) {
      classes.push(config.styles.cssClasses.mobileTable)
    }
    return classes.join(' ')
  })
  
  // 方法 - 獲取優化的載入時間
  const getOptimizedLoadingTime = () => {
    if (options.customLoadingTime) {
      return isMobile.value ? options.customLoadingTime.mobile : options.customLoadingTime.desktop
    }
    
    if (!config.features.loadingOptimization) {
      return 1500 // 預設時間
    }
    
    return isMobile.value ? 1000 : 1500
  }
  
  // 方法 - 自動聚焦處理
  const handleAutoFocus = (inputRef: any) => {
    if (!config.features.autoFocus || isMobile.value) return
    
    nextTick(() => {
      if (inputRef?.value?.focus) {
        inputRef.value.focus()
      }
    })
  }
  
  // 方法 - 表格標題簡化
  const simplifyHeaders = (headers: any[]) => {
    if (!isMobile.value || !layoutConfig.value.simplifyHeaders) {
      return headers
    }
    
    // 移動端只保留前3個最重要的欄位
    return headers.slice(0, 3)
  }
  
  // 方法 - 獲取響應式欄位數量
  const getResponsiveCols = (mobileCols = 12, tabletCols = 6, desktopCols = 4) => {
    if (isMobile.value) return mobileCols
    if (isTablet.value) return tabletCols
    return desktopCols
  }
  
  // 方法 - 更新螢幕尺寸
  const updateScreenSize = () => {
    screenWidth.value = window.innerWidth
    screenHeight.value = window.innerHeight
  }
  
  // 方法 - 切換設備信息顯示
  const toggleDeviceInfo = () => {
    showDevInfo.value = !showDevInfo.value
  }
  
  // 方法 - 獲取狀態顏色（通用）
  const getStatusColor = (value: any, thresholds = { high: 50, medium: 25 }) => {
    const numValue = typeof value === 'number' ? value : parseInt(value) || 0
    
    if (numValue >= thresholds.high) return 'success'
    if (numValue >= thresholds.medium) return 'warning'
    return 'error'
  }
  
  // 方法 - 格式化日期時間（響應式）
  const formatDateTime = (dateTime: string | Date) => {
    if (!dateTime) return ''
    
    const date = new Date(dateTime)
    
    if (isMobile.value) {
      // 移動端顯示簡化格式
      return date.toLocaleDateString('zh-TW', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } else {
      // 桌面端顯示完整格式
      return date.toLocaleString('zh-TW')
    }
  }
  
  // 生命週期處理
  onMounted(() => {
    if (config.enabled) {
      window.addEventListener('resize', updateScreenSize)
      
      // 注入自定義CSS
      if (config.styles.customCSS) {
        const styleElement = document.createElement('style')
        styleElement.textContent = config.styles.customCSS
        styleElement.setAttribute('data-android-layout', 'true')
        document.head.appendChild(styleElement)
      }
    }
  })
  
  onUnmounted(() => {
    if (config.enabled) {
      window.removeEventListener('resize', updateScreenSize)
      
      // 清理自定義CSS
      const styleElements = document.querySelectorAll('style[data-android-layout="true"]')
      styleElements.forEach(el => el.remove())
    }
  })
  
  return {
    // 配置
    config,
    
    // 設備檢測
    isMobile,
    isTablet,
    isDesktop,
    deviceType,
    currentBreakpoint,
    screenWidth,
    screenHeight,
    touchSupported,
    
    // 設備信息
    showDevInfo,
    toggleDeviceInfo,
    
    // 布局配置
    layoutConfig,
    
    // 組件配置
    buttonConfig,
    textFieldConfig,
    cardConfig,
    dataTableConfig,
    snackbarConfig,
    
    // CSS類別
    containerClass,
    cardClass,
    buttonClass,
    textFieldClass,
    tableClass,
    
    // 工具方法
    getOptimizedLoadingTime,
    handleAutoFocus,
    simplifyHeaders,
    getResponsiveCols,
    getStatusColor,
    formatDateTime,
    updateScreenSize
  }
}
