<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            <span class="text-h4">Vue3編輯功能測試頁面</span>
          </v-card-title>

          <v-card-text>
            <v-alert type="info" class="mb-4">
              <strong>測試說明：</strong>
              此頁面用於測試Vue3重構後的編輯功能。您可以選擇不同的測試模式來驗證功能是否正常運作。
            </v-alert>

            <v-row>
              <v-col cols="12" md="6">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-form-select</v-icon>
                    表單測試
                  </v-card-title>
                  <v-card-text>
                    <p>測試Vue3表單的基本功能，包括驗證、數據綁定等。</p>
                    <v-btn
                      color="primary"
                      @click="showInspectForm = true"
                      block
                    >
                      開啟品檢表單測試
                    </v-btn>
                  </v-card-text>
                </v-card>
              </v-col>

              <v-col cols="12" md="6">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-test-tube</v-icon>
                    功能測試
                  </v-card-title>
                  <v-card-text>
                    <p>執行自動化測試來驗證各項功能是否正常。</p>
                    <v-btn
                      color="success"
                      @click="showFunctionTest = true"
                      block
                    >
                      開啟功能測試
                    </v-btn>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>

            <v-row class="mt-4">
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-link</v-icon>
                    直接連結測試
                  </v-card-title>
                  <v-card-text>
                    <p>使用以下連結直接測試不同的編輯模式：</p>
                    <v-chip-group column>
                      <v-chip
                        color="primary"
                        @click="navigateTo('/test-vue3/inspects/new')"
                        prepend-icon="mdi-plus"
                      >
                        新增品檢記錄
                      </v-chip>
                      <v-chip
                        color="secondary"
                        @click="navigateTo('/test-vue3/inspect/1')"
                        prepend-icon="mdi-pencil"
                      >
                        編輯品檢記錄 (ID: 1)
                      </v-chip>
                      <v-chip
                        color="info"
                        @click="navigateTo('/test-vue3/functions')"
                        prepend-icon="mdi-cog"
                      >
                        功能測試頁面
                      </v-chip>
                    </v-chip-group>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>

            <v-row class="mt-4">
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-title>
                    <v-icon class="mr-2">mdi-information</v-icon>
                    測試指南
                  </v-card-title>
                  <v-card-text>
                    <v-list>
                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="primary">mdi-numeric-1-circle</v-icon>
                        </template>
                        <v-list-item-title>表單驗證測試</v-list-item-title>
                        <v-list-item-subtitle>
                          嘗試提交空表單，檢查驗證訊息是否正確顯示
                        </v-list-item-subtitle>
                      </v-list-item>

                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="primary">mdi-numeric-2-circle</v-icon>
                        </template>
                        <v-list-item-title>數據綁定測試</v-list-item-title>
                        <v-list-item-subtitle>
                          輸入數據並檢查是否正確響應和更新
                        </v-list-item-subtitle>
                      </v-list-item>

                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="primary">mdi-numeric-3-circle</v-icon>
                        </template>
                        <v-list-item-title>CRUD操作測試</v-list-item-title>
                        <v-list-item-subtitle>
                          測試新增、編輯、刪除功能是否正常運作
                        </v-list-item-subtitle>
                      </v-list-item>

                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="primary">mdi-numeric-4-circle</v-icon>
                        </template>
                        <v-list-item-title>鍵盤快捷鍵測試</v-list-item-title>
                        <v-list-item-subtitle>
                          測試 Ctrl+S (儲存)、Esc (取消) 等快捷鍵
                        </v-list-item-subtitle>
                      </v-list-item>

                      <v-list-item>
                        <template v-slot:prepend>
                          <v-icon color="primary">mdi-numeric-5-circle</v-icon>
                        </template>
                        <v-list-item-title>主檔明細關聯測試</v-list-item-title>
                        <v-list-item-subtitle>
                          測試主檔儲存後明細檔的新增、編輯、刪除功能
                        </v-list-item-subtitle>
                      </v-list-item>
                    </v-list>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 品檢表單對話框 -->
    <v-dialog v-model="showInspectForm" fullscreen>
      <v-card>
        <v-card-title>
          <span class="text-h5">Vue3品檢表單測試</span>
          <v-spacer></v-spacer>
          <v-btn
            icon="mdi-close"
            @click="showInspectForm = false"
          ></v-btn>
        </v-card-title>

        <v-card-text>
          <InspectFormVue3 />
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- 功能測試對話框 -->
    <v-dialog v-model="showFunctionTest" fullscreen>
      <v-card>
        <v-card-title>
          <span class="text-h5">Vue3功能測試</span>
          <v-spacer></v-spacer>
          <v-btn
            icon="mdi-close"
            @click="showFunctionTest = false"
          ></v-btn>
        </v-card-title>

        <v-card-text>
          <EditFunctionTest />
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- 測試結果通知 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          關閉
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import InspectFormVue3 from './InspectFormVue3Simple.vue'
import EditFunctionTest from '../tests/EditFunctionTest.vue'

const router = useRouter()

// 響應式數據
const showInspectForm = ref(false)
const showFunctionTest = ref(false)
const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

// 方法
const navigateTo = (path: string) => {
  try {
    router.push(path)
    showMessage('正在導航到測試頁面...', 'info')
  } catch (error) {
    showMessage('導航失敗，請檢查路由配置', 'error')
  }
}

const showMessage = (message: string, color: string = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}
</script>

<style scoped>
.v-card {
  transition: all 0.3s ease;
}

.v-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.12);
}

.v-chip {
  cursor: pointer;
  transition: all 0.2s ease;
}

.v-chip:hover {
  transform: scale(1.05);
}
</style>
