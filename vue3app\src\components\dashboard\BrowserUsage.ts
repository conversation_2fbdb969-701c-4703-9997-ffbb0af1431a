import { Component, Vue, Ref } from 'vue-facing-decorator'
import { Doughnut } from 'vue-chartjs'
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js'

ChartJS.register(ArcElement, Tooltip, Legend)

@Component({
  template: '<canvas ref="chart"></canvas>',
  name: 'BrowserUsageChart'
})
export default class BrowserUsageChart extends Vue {
  @Ref('chart') readonly chartRef!: HTMLCanvasElement

  mounted() {
    const chart = new ChartJS(this.chartRef, {
      type: 'doughnut',
      data: {
        labels: ["Firefox", "Safari", "Chrome", "IE"],
        datasets: [
          {
            backgroundColor: [
              "#41B883",
              "#E46651",
              "#00D8FF",
              "#DD1B16"
            ],
            data: [40, 20, 80, 10]
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    })
  }
}
