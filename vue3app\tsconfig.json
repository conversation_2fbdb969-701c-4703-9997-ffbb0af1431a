{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "noEmit": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["node", "vite/client"], "typeRoots": ["./node_modules/@types", "./src"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/vuetify.d.ts"], "references": [{"path": "./tsconfig.node.json"}]}