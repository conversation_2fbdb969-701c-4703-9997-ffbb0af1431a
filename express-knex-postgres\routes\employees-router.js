const router = require("express").Router();

const employeesDB = require("../models/employees-model.js");

// GET ALL EMPLOYEES
router.get("/", async (req, res) => {
  try {
    const employees = await employeesDB.find();
    res.status(200).json(employees);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET TW EMPLOYEES
router.get("/tw", async (req, res) => {
  try {
      const employees = await employeesDB.findOfTW();
      const validatedEmployees = employees.filter(employee => /^[A-Za-z0-9]\d{5}$/.test(employee.employNO));  
      res.status(200).json(validatedEmployees);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET QI EMPLOYEES
router.get("/qi", async (req, res) => {
  try {
    const employees = await employeesDB.findOfQI();
    const validatedEmployees = employees.filter(employee => /^[A-Za-z0-9]\d{5}$/.test(employee.employNO));
    res.status(200).json(validatedEmployees);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

router.get("/pk", async (req, res) => {
  try {
    const employees = await employeesDB.findOfPK();
    const validatedEmployees = employees.filter(employee => /^[A-Za-z0-9]\d{5}$/.test(employee.employNO));
    res.status(200).json(validatedEmployees);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET EMPLOYEE BY ID
router.get("/:id", async (req, res) => {
  const employeeId = req.params.id;
  try {
    const employee = await employeesDB.findById(employeeId);
    if (!employee) {
      res.status(404).json({ err: "The employee with the specified id does not exist" });
    } else {
      res.status(200).json(employee);
    }
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// INSERT EMPLOYEE INTO DB
router.post("/", async (req, res) => {
  const newEmployee = req.body;
  if (!newEmployee.employNO) {
    res.status(404).json({ err: "Please provide the id" });
  } else {
    try {
      const employee = await employeesDB.addEmployee(newEmployee);
      res.status(201).json(employee);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

router.put("/:id", async (req, res) => {
  const employeeId = req.params.id;
  const newChanges = req.body;
  if (!newChanges.firstname) {
    res.status(404).json({ err: "You are missing information" });
  } else {
    try {
      const addChanges = await employeesDB.updateEmployee(employeeId, newChanges);
      res.status(200).json(addChanges);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

router.delete("/:id", async (req, res) => {
  const employeeId = req.params.id;
  try {
    const deleting = await employeesDB.removeEmployee(employeeId);
    res.status(204).json(deleting);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;
