import { getData, putData, postData, deleteData } from "@/utils/backend-api";
import {
  Employee,
  Relabel,
  Entity,
  Product,
  Category,
  Relabelline
} from "@/types";
import { getDefaultPagination, getPagination } from "@/utils/store-util";
import { appModule } from "./app";
import { get } from "lodash";
import {
  VuexModule,
  Module,
  Mutation,
  Action,
  getModule
} from "vuex-module-decorators";
import store from "@/store";

export interface RelabelState {
  items: Entity[];
  pagination: Pagination;
  loading: boolean;
  employee: string;
  relabelId: number;
  relabel: Relabel;
  relabelline: Relabelline[];
  product: Product;
  employees: Employee[];
  categories: Category[];
}

@Module({ store, dynamic: true, name: "relabels" })
class RelabelModule extends VuexModule implements RelabelState {
  public items: Entity[] = [];
  public pagination = getDefaultPagination();
  public loading = false;
  public employee = "";
  public relabelId = null;
  public relabel = {} as Relabel;
  public relabelline: Relabelline[] = [];
  public product = {} as Product;
  public employees: Employee[] = [];
  public categories: Category[] = [];

  @Action
  getEmployees() {
    getData("employees/tw").then(res => {
      if (res.data) {
        const employees = res.data.map((c: Employee) => {
          c.employName = c.employNO + " " + c.userName;
          c.value = c.id;
          return c;
        });
        this.setEmployees(employees);
      }
    });
  }

  @Action
  async getAllRelabelsByType(type: string) {
    this.setLoading(true);
    try {
      const res = await getData(`relabels/${type}`);
      const relabels = res.data;
      this.setRelabel(relabels);
      this.setDataTable(relabels);
    } catch (error) {
      console.error(error);
    } finally {
      this.setLoading(false);
    }
  }

  @Action
  getRelabelById(id: string) {
    if (id) {
      getData("relabels/" + id).then(
        res => {
          const _relabel = res.data;
          const relabel = _relabel[0];
          relabel.relabellines.filter(
            (p: Relabel) => p !== null && p !== undefined
          );
            relabel.quantity = relabel.relabellines?.length;
          this.setRelabel(relabel);
          this.setDataTable(relabel.relabellines);
        },
        (err: TODO) => {
          console.log(err);
        }
      );
    } else {
      const relabel = {} as Relabel;
      relabel.relabellines = [];
      this.setRelabel(relabel);
      this.setLoading(false);
    }
  }

  @Action
  async getProductById(id: string) {
    try {
      this.setLoading(true);
      if (id) {
        const res = await getData("products/" + id);
        const product = res.data;
        this.setProduct(product);
      } else {
        this.setProduct({} as Product);
      }
    } catch (err) {
        console.log(err);
    } finally {
      this.setLoading(false);
    }
  }

  @Action async getDuplicateRelabellineByCode(code: string): Promise<boolean> {
    try {
      this.setLoading(true);
      if (code) {
        const res = await getData("relabellines/duplicate/" + code);
        const data = res.data;
        if (data !== undefined && data !== null) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    } catch (err) {
      console.log(err);
      return false;
    } finally {
      this.setLoading(false);
    }
  }

  @Action
  searchRelabels(searchQuery: string) {
    getData("relabels" + searchQuery).then(res => {
      const relabels = res.data;
      relabels.forEach((item: Relabel) => {
        item.quantity = item.relabellines?.length;
      });
      this.setDataTable(relabels);
      this.setLoading(false);
    });
  }

  @Action
  quickSearch(headers: TableHeader[], qsFilter: SeachQuery): void {
    getData("relabels").then(res => {
      const relabels = res.data.filter((r: TODO) =>
        headers.some((header: TableHeader) => {
          const val = get(r, [header.value]);
          return (
            (val &&
              val
              .toString()
              .toLowerCase()
              .includes(qsFilter)) ||
              false
          );
        })
      );
      relabels.forEach((item: Relabel) => {
        item.quantity = item.relabellines?.length;
      });
      this.setDataTable(relabels);
      this.setLoading(false);
    });
  }

  @Action
  saveRelabel(relabel: Relabel): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!relabel.id) {
        postData("relabels/", relabel)
          .then(res => {
            const relabel = res.data;
            const relabelId = {id : relabel[0].id};
            const addRelabel = { ...relabelId, ...this.relabel};
            this.setRelabel(addRelabel);
            this.setRelabelId(addRelabel.id);
            appModule.sendSuccessNotice("New record has been added.");
            appModule.closeNoticeWithDelay(3000);
            resolve();
          })
          .catch((err: TODO) => {
            console.log(err);
            appModule.sendErrorNotice("Operation failed! Please try again later.");
            appModule.closeNoticeWithDelay(5000);
            reject(err);
          });
      } else {
        putData("relabels/" + relabel.id.toString(), relabel)
          .then(() => {
            appModule.sendSuccessNotice("The record has been updated.");
            appModule.closeNoticeWithDelay(3000);
            resolve();
          })
          .catch((err: TODO) => {
            console.log(err);
            appModule.sendErrorNotice("Operation failed! Please try again later.");
            appModule.closeNoticeWithDelay(5000);
            reject(err);
          });
      }
    });
  }

  @Action
  addRelabellineToRelabel(relabelline: Relabelline) {
    if (relabelline) {
      this.saveRelabelline(relabelline);
      const relabelId = this.relabel.id;
      this.getRelabelById(relabelId.toString());
      const newRelabel = this.relabel;
      this.setRelabel(newRelabel);
    }
  }

  @Action
  saveRelabelline(relabelline: Relabelline) {
    if (!relabelline.id) {
      postData("relabellines/", relabelline)
        .then(res => {
          const relabelline = res.data;
          this.setRelabelline(relabelline);
          appModule.sendSuccessNotice("New record has been added.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(3000);
        });
    } else {
      putData("relabellines/" + relabelline.id.toString(), relabelline)
        .then(res => {
          const relabel = res.data;
          this.setRelabel(relabel);
          appModule.sendSuccessNotice("The record has been updated.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    }
  }

  @Action
  async deleteRelabel(id: number) {
    try {
      await deleteData(`relabels/${id.toString()}`);
      appModule.sendSuccessNotice("Operation is done.");
      appModule.closeNoticeWithDelay(3000);
    }
    catch (error) {
      console.error(error);
      appModule.sendErrorNotice("Operation failed! Please try again later.");
      appModule.closeNoticeWithDelay(5000);
    }
    finally {
      this.setLoading(false);
    }
  }

  @Action
  deleteRelabelline(relabelline: Relabelline) {
    if (relabelline) {
      const relabelId = this.relabel.id;
      const relabellineId = relabelline.id;
      const { relabellines } = this.relabel;
      relabellines.splice(
        relabellines.findIndex((p: Relabelline) => p.id === relabelline.id),1
      );
      this.setRelabelline(relabellines);
      deleteData(`relabellines/${relabellineId.toString()}`)
        .then(() => {
          this.getRelabelById(relabelId.toString());
          const newRelabel = this.relabel;
          this.setRelabel(newRelabel);
          appModule.sendSuccessNotice("Operation is done.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    }
  }

  @Action
  clearRelabelline() {
    this.setLoading(true);
    const relabelline = [];
    this.setRelabelline(relabelline);
    this.setLoading(false);
  }

  @Action
  setDataTable(items: Relabel[]) {
    const pagination = getPagination(items);
    this.setPagination(pagination);
    this.setItems(items);
  }

  @Mutation
  setEmployees(employees: Employee[]) {
    this.employees = employees;
  }

  @Mutation
  setCategories(categories: Category[]) {
    this.categories = categories;
  }

  @Mutation
  setRelabelId(id: number | null) {
    this.relabelId = id;
  }

  @Mutation
  setRelabel(relabel: Relabel) {
    this.relabel = relabel;
  }

  @Mutation
  setRelabelline(relabelline: Relabelline[]) {
    this.relabelline = relabelline;
  }

  @Mutation
  setProduct(product: Product) {
    this.product = product;
  }

  @Mutation
  setItems(relabels: Relabel[]) {
    this.items = relabels;
  }

  @Mutation
  setPagination(pagination: Pagination) {
    this.pagination = pagination;
  }

  @Mutation
  setLoading(loading: boolean) {
    this.loading = loading;
  }
}

export const relabelModule = getModule(RelabelModule); // Relabels;
