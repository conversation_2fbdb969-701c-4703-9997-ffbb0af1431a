const express = require("express");
const server = express();
const morgan = require("morgan");
const cors = require("cors");
const { infoLogger, errorLogger } = require("./config/winston.js");

const usersRouter = require("./routes/users-router.js");
const employeesRouter = require("./routes/employees-router.js");
const inspectsRouter = require("./routes/inspects-router.js");
const inspectlinesRouter = require("./routes/inspectlines-router.js");
const worksRouter = require("./routes/works-router.js");
const worklinesRouter = require("./routes/worklines-router.js");
const downgradesRouter = require("./routes/downgrades-router.js");
const downgradelinesRouter = require("./routes/downgradelines-router.js");
const relabelsRouter = require("./routes/relabels-router.js");
const relabellinesRouter = require("./routes/relabellines-router.js");
const icountsRouter = require("./routes/icounts-router.js");
const icountlinesRouter = require("./routes/icountlines-router.js");
const pinventoriesRouter = require("./routes/pinventories-router.js");
const pinventorylinesRouter = require("./routes/pinventorylines-router.js");
const productsRouter = require("./routes/products-router.js");
const tracksRouter = require("./routes/tracks-router.js");
const tracksheetsRouter = require("./routes/tracksheets-router.js");
const categoriesRouter = require("./routes/categories-router.js");
const remarksRouter = require("./routes/remarks-router.js");

// Enhanced CORS configuration
const corsOptions = {
  origin: process.env.NODE_ENV === 'production' 
    ? process.env.FRONTEND_URL 
    : true, // Allow all origins in development
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Access-Control-Allow-Origin']
};

// Middleware
server.use(cors(corsOptions));
server.use(morgan("dev"));
server.use(express.json({ limit: '10mb' }));
server.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware
server.use((req, res, next) => {
  infoLogger.info(`${req.method} ${req.url}`);
  next();
});

// Add CORS headers for all responses in development
if (process.env.NODE_ENV === 'development') {
  server.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    next();
  });
}

// Routers
server.use("/api/users", usersRouter);
server.use("/api/employees", employeesRouter);
server.use("/api/inspects", inspectsRouter);
server.use("/api/inspectlines", inspectlinesRouter);
server.use("/api/works", worksRouter);
server.use("/api/worklines", worklinesRouter);
server.use("/api/downgrades", downgradesRouter);
server.use("/api/downgradelines", downgradelinesRouter);
server.use("/api/relabels", relabelsRouter);
server.use("/api/relabellines", relabellinesRouter);
server.use("/api/icounts", icountsRouter);
server.use("/api/icountlines", icountlinesRouter);
server.use("/api/pinventories", pinventoriesRouter);
server.use("/api/pinventorylines", pinventorylinesRouter);
server.use("/api/products", productsRouter);
server.use("/api/tracks", tracksRouter);
server.use("/api/tracksheets", tracksheetsRouter);
server.use("/api/categories", categoriesRouter);
server.use("/api/remarks", remarksRouter);

// Base route
server.get("/", (req, res) => {
  res.status(200).json({ message: "API is running" });
});

// Error handling middleware
server.use((err, req, res, next) => {
  errorLogger.error(err.stack);
  
  if (err.name === 'UnauthorizedError') {
    return res.status(401).json({ message: 'Invalid token' });
  }
  
  res.status(500).json({
    message: 'An unexpected error occurred',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// 404 handler
server.use((req, res) => {
  res.status(404).json({ message: 'Route not found' });
});

module.exports = server;
