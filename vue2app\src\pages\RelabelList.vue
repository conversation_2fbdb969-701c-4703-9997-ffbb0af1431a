<template>
  <v-container fluid>
    <v-flex xs12>
      <v-card>
        <v-card-title>
          <span class="title"
            >{{title}} {{ pagination ? "(" + pagination.totalItems + ")" : "" }}
          </span>
          <v-spacer></v-spacer>
          <table-header-buttons
            :add="add"
            :reloadData="reloadData"
          ></table-header-buttons>
        </v-card-title>
        <Table
          v-if="loading === false"
          :headers="headers"
          :items="items"
          :pagination="pagination"
          :setSearch="true"
          :setEdit="true"
          :setRemove="true"
          @edit="edit"
          @remove="remove"
        ></Table>
      </v-card>
    </v-flex>
    <search-panel
      :rightDrawer="rightDrawer"
      @cancelSearch="cancelSearch"
      @searchData="searchRelabels"
    >
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="reference"
            label="Reference"
            light
            v-model="searchFilter.contain.reference"
          ></v-text-field>
        </v-flex>
      </v-layout>
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="customer"
            label="Customer"
            light
            v-model="searchFilter.contain.customer"
          ></v-text-field>
        </v-flex>
      </v-layout>
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="minAmount"
            type="number"
            label="Min Amount"
            light
            v-model="searchFilter.greaterThanOrEqual.amount"
          ></v-text-field>
        </v-flex>
      </v-layout>
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="maxAmount"
            type="number"
            label="Max Amount"
            light
            v-model="searchFilter.lessThanOrEqual.amount"
          ></v-text-field>
        </v-flex>
      </v-layout>
    </search-panel>
    <confirm-dialog
      :dialog="dialog"
      :dialogTitle="dialogTitle"
      :dialogText="dialogText"
      @onConfirm="onConfirm"
      @onCancel="onCancel"
    ></confirm-dialog>
    <v-snackbar
      v-if="loading === false"
      :top="'top'"
      :right="true"
      :timeout="5000"
      :color="mode"
      v-model="snackbar"
    >
    <div class="text-center">
      {{ notice }}
      <v-btn dark text @click.native="closeSnackbar">Close</v-btn>
    </div>
    </v-snackbar>
  </v-container>
</template>
<script lang="ts">
import Table from "@/components/Table.vue";
import TableHeaderButtons from "@/components/TableHeaderButtons.vue";
import SearchPanel from "@/components/SearchPanel.vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
import { debounce } from "lodash";
import {
  buildSearchFilters,
  buildJsonServerQuery,
  clearSearchFilters
} from "@/utils/app-util";
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { relabelModule } from "@/store/modules/relabels";
import { appModule } from "@/store/modules/app";

@Component({
  components: {
    Table,
    TableHeaderButtons,
    SearchPanel,
    ConfirmDialog
  }
})
export default class RelabelList extends Vue {
  public dialog = false;
  public dialogTitle = "Relabel NO MFD(主檔)刪除確認";
  public dialogText = "刪除該筆記錄?";
  public showSearchPanel = false;
  public right = true;
  public search = "";
  public headers = [
    { text: "單號", left: true, value: "id" },
    { text: "日期", value: "classDate" },
    { text: "勤別", value: "shiftName" },
    { text: "人員", value: "employName" },
    { text: "組別", value: "groupName" },
    { text: "小計", value: "quantity" },
    { text: "", value: "actions", sortable: false }
  ];
  private searchFilter = {
    contain: {
      reference: "",
      customer: ""
    },
    greaterThanOrEqual: {
      amount: 0
    },
    lessThanOrEqual: {
      amount: 0
    }
  };
  private title = "";
  private type = "NOMFD";
  private query = "";
  private color = "";
  private quickSearchFilter = "";
  private itemId = -1;
  private dateFormat: "DD-MM-YYYY";

  edit(item) {
    this.$router.push(`relabel/${item.id}`);
  }
  add() {
    this.$router.push("newrelabel");
  }
  remove(item) {
    this.itemId = item.id;
    this.dialog = true;
  }
  onConfirm() {
    relabelModule.deleteRelabel(this.itemId).then(() => {
    this.dialog = false;
    this.$nextTick(() => {
      relabelModule.getAllRelabelsByType(this.type);
      });
    });
  }
  onCancel() {
    this.itemId = -1;
    this.dialog = false;
  }
  searchRelabels() {
    this.showSearchPanel = !this.showSearchPanel;
    buildSearchFilters(this.searchFilter);
    this.query = buildJsonServerQuery(this.searchFilter);
    this.quickSearch = "";
    relabelModule.searchRelabels(this.query);
  }

  clearSearchFilters() {
    this.showSearchPanel = !this.showSearchPanel;
    clearSearchFilters(this.searchFilter);
    relabelModule.getAllRelabelsByType(this.type);
  }

  reloadData() {
    this.query = "";
    relabelModule.getAllRelabelsByType(this.type);
  }

  updateSearchPanel() {
    this.rightDrawer = !this.rightDrawer;
  }

  cancelSearch() {
    this.showSearchPanel = false;
  }

  closeSnackbar() {
    appModule.closeNotice();
  }

  quickSearchRelabels = debounce(function() {
    relabelModule.quickSearch(this.headers, this.quickSearchFilter);
  }, 500);

  get items() {
    return relabelModule.items;
  }
  get pagination() {
    return relabelModule.pagination;
  }
  get loading() {
    return appModule.loading;
  }
  get setSearch() {
    return true;
  }

  get mode() {
    return appModule.mode;
  }
  get snackbar() {
    return appModule.snackbar;
  }
  get notice() {
    return appModule.notice;
  }

  get rightDrawer() {
    return this.showSearchPanel;
  }

  set rightDrawer(_showSearchPanel: boolean) {
    this.showSearchPanel = _showSearchPanel;
  }

  get quickSearch() {
    return this.quickSearchFilter;
  }
  set quickSearch(val) {
    this.quickSearchFilter = val;
    this.quickSearchFilter && this.quickSearchRelabels();
  }

  created() {
    relabelModule.getAllRelabelsByType(this.type);
  }
  mounted() {
    this.$nextTick(() => {
      this.title = "Relabel NO MFD";
    });
  }
}
</script>
