import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getData } from '@/utils/backend-api'
import type { Tracksheet } from '@/types'

export const useTracksheetsStore = defineStore('tracksheets', () => {
  // State
  const tracksheets = ref<Tracksheet[]>([])
  const tracksheet = ref<Tracksheet | null>(null)
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    rowsPerPage: 10,
    totalItems: 0,
    totalPages: 0
  })

  // Getters
  const isLoading = computed(() => loading.value)
  const items = computed(() => tracksheets.value)

  // Actions
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setTracksheets = (value: Tracksheet[]) => {
    tracksheets.value = value
    pagination.value.totalItems = value.length
    pagination.value.totalPages = Math.ceil(value.length / pagination.value.rowsPerPage)
  }

  const setTracksheet = (value: Tracksheet | null) => {
    tracksheet.value = value
  }

  // 根據代碼查詢追蹤傳票
  const getTracksheetByCode = async (tracks: string[]) => {
    try {
      setLoading(true)
      const type = tracks[0].toString()
      const id = tracks[1].toString()
      const trackId = tracks[2].toString()

      console.log(`正在查詢追蹤傳票: type=${type}, id=${id}, trackId=${trackId}`)

      if (id) {
        const res = await getData(`tracksheets/${type}/${id}`)
        let tracksheetData = res.data

        if (type === "CAKE" && tracksheetData && tracksheetData.length > 0) {
          tracksheetData[0].tracksheetNO = trackId
        }

        setTracksheets(Array.isArray(tracksheetData) ? tracksheetData : [tracksheetData])
        // 設置單個 tracksheet，如果是數組則取第一個
        setTracksheet(Array.isArray(tracksheetData) ? tracksheetData[0] : tracksheetData)
        return tracksheetData
      } else {
        setTracksheet(null)
        setTracksheets([])
        return null
      }
    } catch (error) {
      console.error('根據代碼獲取追蹤傳票失敗:', error)
      setTracksheets([])
      setTracksheet(null)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 搜尋追蹤傳票
  const searchTracksheets = async (searchQuery: string) => {
    try {
      setLoading(true)
      const res = await getData(`tracksheets${searchQuery}`)
      const tracksheetsData = res.data || []
      setTracksheets(tracksheetsData)
      return tracksheetsData
    } catch (error) {
      console.error('搜尋追蹤傳票失敗:', error)
      setTracksheets([])
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 快速搜尋
  const quickSearch = async (headers: any[], qsFilter: string) => {
    try {
      setLoading(true)
      const res = await getData("tracksheets")
      const allTracksheets = res.data || []

      const filteredTracksheets = allTracksheets.filter((tracksheet: any) =>
        headers.some((header: any) => {
          const val = tracksheet[header.value]
          return val && val.toString().toLowerCase().includes(qsFilter.toLowerCase())
        })
      )

      setTracksheets(filteredTracksheets)
      return filteredTracksheets
    } catch (error) {
      console.error('快速搜尋失敗:', error)
      setTracksheets([])
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 清除追蹤傳票
  const clearTracksheets = () => {
    setTracksheets([])
    setTracksheet(null)
  }

  // 重置狀態
  const resetState = () => {
    tracksheets.value = []
    tracksheet.value = null
    loading.value = false
    pagination.value = {
      page: 1,
      rowsPerPage: 10,
      totalItems: 0,
      totalPages: 0
    }
  }

  return {
    // State
    tracksheets,
    tracksheet,
    loading,
    pagination,

    // Getters
    isLoading,
    items,

    // Actions
    setLoading,
    setTracksheets,
    setTracksheet,
    getTracksheetByCode,
    searchTracksheets,
    quickSearch,
    clearTracksheets,
    resetState
  }
})
