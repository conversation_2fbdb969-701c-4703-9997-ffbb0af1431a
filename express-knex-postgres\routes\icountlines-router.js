const router = require("express").Router();
const moment = require('moment');

const icountlinesDB = require("../models/icountlines-model.js");

// GET ICOUNTLINE BY ID
router.get("/:id", async (req, res) => {
  const icountlineId = req.params.id;
  try {
    const icountline = await icountlinesDB.findById(icountlineId);
      icountline.forEach(mem => {
      mem.tracksheetTime = moment(mem.tracksheetTime).format("YYYY-M-D HH:mm");
    });
    if (!icountline) {
      res.status(404).json({ err: "The icountline with the specified id does not exist" });
    } else {
      res.status(200).json(icountline);
    }
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

// GET ICOUNTLINE BY CODE
router.get("/duplicate/:id", async (req, res) => {
  const tracksheetNO = req.params.id;
  try {
    const exist = await icountlinesDB.findByCode(tracksheetNO);
    if (Array.isArray(exist) && exist.length > 0) {
      res.status(200).json(exist);
    } else {
      res.status(404).json({ error: "Data not found" });
    }
  } catch (err) {
    console.error(err);
    res.status(500).json({ err: err.message });
  }
});

// INSRT ICOUNTLINE INTO DB
router.post("/", async (req, res) => {
  const newICountline = req.body;
  if (!newICountline.icountId) {
    res.status(404).json({ err: "Please provide the id" });
  } else {
    try {
      const icountline = await icountlinesDB.addICountline(newICountline);
      res.status(201).json(icountline);
    } catch (err) {
      res.status(500).json({ err: err.message });
    }
  }
});

// REMOVE ICOUNTLINE INTO DB
router.delete("/:id", async (req, res) => {
  const icountlineId = req.params.id;
  try {
    const deleting = await icountlinesDB.removeICountline(icountlineId);
    res.status(204).json(deleting);
  } catch (err) {
    res.status(500).json({ err: err.message });
  }
});

module.exports = router;