import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getData, postData, putData, deleteData } from '@/utils/backend-api'

export interface FormEditOptions {
  apiEndpoint: string
  listRoute: string
  defaultData?: any
  onSaveSuccess?: (data: any) => void
  onSaveError?: (error: any) => void
  onLoadSuccess?: (data: any) => void
  onLoadError?: (error: any) => void
}

export function useFormEdit(options: FormEditOptions) {
  const router = useRouter()
  const route = useRoute()

  // 響應式數據
  const loading = ref(false)
  const formValid = ref(false)
  const formData = ref(options.defaultData || {})
  const formRef = ref()

  // 通知訊息
  const snackbar = ref({
    show: false,
    message: '',
    color: 'success'
  })

  // 計算屬性
  const isEditMode = computed(() => !!route.params.id)
  const formTitle = computed(() => 
    isEditMode.value ? '編輯' : '新增'
  )

  // 顯示訊息
  const showMessage = (message: string, color: string = 'success') => {
    snackbar.value = {
      show: true,
      message,
      color
    }
  }

  // 載入數據
  const loadData = async (id?: string) => {
    if (!id) return

    loading.value = true
    try {
      const response = await getData(`${options.apiEndpoint}/${id}`)
      formData.value = response.data
      
      if (options.onLoadSuccess) {
        options.onLoadSuccess(response.data)
      }
    } catch (error) {
      console.error('Load data error:', error)
      showMessage('載入數據失敗', 'error')
      
      if (options.onLoadError) {
        options.onLoadError(error)
      }
    } finally {
      loading.value = false
    }
  }

  // 儲存數據
  const save = async () => {
    if (!formRef.value?.validate()) {
      showMessage('請檢查表單內容', 'warning')
      return false
    }

    loading.value = true
    try {
      let response
      if (isEditMode.value) {
        response = await putData(`${options.apiEndpoint}/${formData.value.id}`, formData.value)
        showMessage('更新成功')
      } else {
        response = await postData(options.apiEndpoint, formData.value)
        showMessage('新增成功')
      }

      if (options.onSaveSuccess) {
        options.onSaveSuccess(response.data)
      }

      return true
    } catch (error) {
      console.error('Save error:', error)
      showMessage('儲存失敗，請稍後再試', 'error')
      
      if (options.onSaveError) {
        options.onSaveError(error)
      }
      
      return false
    } finally {
      loading.value = false
    }
  }

  // 取消編輯
  const cancel = () => {
    router.push(options.listRoute)
  }

  // 重置表單
  const resetForm = () => {
    formData.value = options.defaultData || {}
    if (formRef.value) {
      formRef.value.resetValidation()
    }
  }

  // 驗證表單
  const validateForm = () => {
    return formRef.value?.validate() || false
  }

  // 生命週期
  onMounted(async () => {
    if (isEditMode.value && route.params.id) {
      await loadData(route.params.id as string)
    }
  })

  return {
    // 響應式數據
    loading,
    formValid,
    formData,
    formRef,
    snackbar,
    
    // 計算屬性
    isEditMode,
    formTitle,
    
    // 方法
    showMessage,
    loadData,
    save,
    cancel,
    resetForm,
    validateForm
  }
}

// 明細檔編輯組合式函數
export interface DetailEditOptions {
  apiEndpoint: string
  parentIdField: string
  defaultDetailData?: any
  onSaveSuccess?: (data: any) => void
  onSaveError?: (error: any) => void
  onDeleteSuccess?: (id: any) => void
  onDeleteError?: (error: any) => void
}

export function useDetailEdit(options: DetailEditOptions) {
  // 響應式數據
  const detailItems = ref([])
  const detailData = ref(options.defaultDetailData || {})
  const detailDialog = ref(false)
  const detailFormValid = ref(false)
  const detailFormRef = ref()
  const editingDetailId = ref(null)
  const detailLoading = ref(false)

  // 確認刪除對話框
  const confirmDialog = ref(false)
  const confirmTitle = ref('確認刪除')
  const confirmText = ref('確定要刪除此項目嗎？')
  const deleteItemId = ref(null)

  // 通知訊息
  const snackbar = ref({
    show: false,
    message: '',
    color: 'success'
  })

  // 計算屬性
  const detailTitle = computed(() => 
    editingDetailId.value ? '編輯明細' : '新增明細'
  )

  // 顯示訊息
  const showMessage = (message: string, color: string = 'success') => {
    snackbar.value = {
      show: true,
      message,
      color
    }
  }

  // 載入明細數據
  const loadDetailData = async (parentId: string | number) => {
    if (!parentId) return

    detailLoading.value = true
    try {
      const response = await getData(`${options.apiEndpoint}?${options.parentIdField}=${parentId}`)
      detailItems.value = response.data
    } catch (error) {
      console.error('Load detail data error:', error)
      showMessage('載入明細數據失敗', 'error')
    } finally {
      detailLoading.value = false
    }
  }

  // 新增明細
  const addDetail = () => {
    detailData.value = options.defaultDetailData || {}
    editingDetailId.value = null
    detailDialog.value = true
    
    nextTick(() => {
      if (detailFormRef.value) {
        detailFormRef.value.resetValidation()
      }
    })
  }

  // 編輯明細
  const editDetail = (item: any) => {
    detailData.value = { ...item }
    editingDetailId.value = item.id
    detailDialog.value = true
  }

  // 儲存明細
  const saveDetail = async (parentId: string | number) => {
    if (!detailFormRef.value?.validate()) {
      showMessage('請檢查明細表單內容', 'warning')
      return false
    }

    detailLoading.value = true
    try {
      const dataToSave = {
        ...detailData.value,
        [options.parentIdField]: parentId
      }

      let response
      if (editingDetailId.value) {
        response = await putData(`${options.apiEndpoint}/${editingDetailId.value}`, dataToSave)
        showMessage('明細更新成功')
      } else {
        response = await postData(options.apiEndpoint, dataToSave)
        showMessage('明細新增成功')
      }

      detailDialog.value = false
      await loadDetailData(parentId)

      if (options.onSaveSuccess) {
        options.onSaveSuccess(response.data)
      }

      return true
    } catch (error) {
      console.error('Save detail error:', error)
      showMessage('明細儲存失敗，請稍後再試', 'error')
      
      if (options.onSaveError) {
        options.onSaveError(error)
      }
      
      return false
    } finally {
      detailLoading.value = false
    }
  }

  // 取消明細編輯
  const cancelDetail = () => {
    detailDialog.value = false
    detailData.value = options.defaultDetailData || {}
    editingDetailId.value = null
  }

  // 刪除明細
  const removeDetail = (item: any) => {
    deleteItemId.value = item.id
    confirmDialog.value = true
  }

  // 確認刪除
  const onConfirmDelete = async (parentId: string | number) => {
    if (!deleteItemId.value) return

    detailLoading.value = true
    try {
      await deleteData(`${options.apiEndpoint}/${deleteItemId.value}`)
      showMessage('刪除成功')
      confirmDialog.value = false
      await loadDetailData(parentId)

      if (options.onDeleteSuccess) {
        options.onDeleteSuccess(deleteItemId.value)
      }
    } catch (error) {
      console.error('Delete error:', error)
      showMessage('刪除失敗，請稍後再試', 'error')
      
      if (options.onDeleteError) {
        options.onDeleteError(error)
      }
    } finally {
      detailLoading.value = false
      deleteItemId.value = null
    }
  }

  // 取消刪除
  const onCancelDelete = () => {
    confirmDialog.value = false
    deleteItemId.value = null
  }

  return {
    // 響應式數據
    detailItems,
    detailData,
    detailDialog,
    detailFormValid,
    detailFormRef,
    editingDetailId,
    detailLoading,
    confirmDialog,
    confirmTitle,
    confirmText,
    snackbar,
    
    // 計算屬性
    detailTitle,
    
    // 方法
    showMessage,
    loadDetailData,
    addDetail,
    editDetail,
    saveDetail,
    cancelDetail,
    removeDetail,
    onConfirmDelete,
    onCancelDelete
  }
}
