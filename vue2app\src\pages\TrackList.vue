<template>
  <v-container fluid>
    <v-flex xs12>
      <v-card>
        <v-card-title>
          <span class="title"
            >{{title}}
            {{ pagination ? "(" + pagination.totalItems + ")" : "" }}
          </span>
          <v-spacer></v-spacer>
          <table-header-buttons :reloadData="clearData"></table-header-buttons>
        </v-card-title>
        <v-card-text>
          <v-text-field
            v-model="searchFilter.contain.twisterNO"
            append-icon="mdi-magnify"
            label="Search"
            @change="getTrack"
            counter="14"
            single-line
            hide-details
          ></v-text-field>
        </v-card-text>
        <Table
          v-if="loading === false"
          :headers="headers"
          :items="items"
          :pagination="pagination"
          :setSearch="false"
          :setEdit="false"
          :setRemove="false"
          :disableSort="true"

        ></Table>
        <!--  
        <Table v-if="loading === false" :headers="headers" :items="items" :pagination="pagination" @edit="edit" @remove="remove"></Table>
      -->
      </v-card>
    </v-flex>
    <search-panel
      :rightDrawer="rightDrawer"
      @cancelSearch="cancelSearch"
      @searchData="searchTracks"
    >
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="productName"
            label="Track"
            light
            v-model="searchFilter.contain.productName"
          ></v-text-field>
        </v-flex>
      </v-layout>
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="minUnitPrice"
            type="number"
            label="Min Price"
            light
            v-model="searchFilter.greaterThanOrEqual.unitPrice"
          ></v-text-field>
        </v-flex>
      </v-layout>
      <v-layout row>
        <v-flex xs11 offset-xs1>
          <v-text-field
            name="maxUnitPrice"
            type="number"
            label="Max Price"
            light
            v-model="searchFilter.lessThanOrEqual.unitPrice"
          ></v-text-field>
        </v-flex>
      </v-layout>
    </search-panel>
    <confirm-dialog
      :dialog="dialog"
      :dialogTitle="dialogTitle"
      :dialogText="dialogText"
      @onConfirm="onConfirm"
      @onCancel="onCancel"
    ></confirm-dialog>
    <v-snackbar
      v-if="loading === false"
      :right="true"
      :timeout="timeout"
      :color="mode"
      v-model="snackbar"
    >
      {{ notice }}
      <v-btn dark text @click.native="closeSnackbar">Close</v-btn>
    </v-snackbar>
  </v-container>
</template>
<script lang="ts">
import Table from "@/components/Table.vue";
import TableHeaderButtons from "@/components/TableHeaderButtons.vue";
import SearchPanel from "@/components/SearchPanel.vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
import { debounce } from "lodash";
import {
  buildSearchFilters,
  buildJsonServerQuery,
  clearSearchFilters
} from "@/utils/app-util";
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { trackModule } from "@/store/modules/tracks";
import { appModule } from "@/store/modules/app";

@Component({
  components: {
    Table,
    TableHeaderButtons,
    SearchPanel,
    ConfirmDialog
  }
})
export default class TrackList extends Vue {
  dialog = false;
  dialogTitle = "Customer Delete Dialog";
  dialogText = "Do you want to delete this customer?";
  showSearchPanel = false;
  right = true;
  search = ""; 
  headers = [
    { text: "Twister NO", value: "twisterNO" },
    { text: "掃瞄日期時間", left: true, value: "trackTime" },
    { text: "爐別", left: true, value: "furnaceName" },
    { text: "品種", left: true, value: "productName" },
    { text: "掃瞄T1個數", value: "trackT1Qty" }, 
    { text: "掃瞄T2個數", value: "trackT2Qty" },
    { text: "", value: "actions", sortable: false }
  ];
  searchFilter = {
    contain: {
      twisterNO: ""
    },
    greaterThanOrEqual: {
      unitPrice: 0
    },
    lessThanOrEqual: {
      unitPrice: 0
    }
  };
  private title = "";
  private trackId = "";
  private query = "";
  private snackbarStatus = false;
  private timeout = 2000;
  private color = "";
  private quickSearchFilter = "";
  private itemId = -1;

  print() {
    window.print();
  }
  edit(item) {
    this.$router.push({
      name: "Track",
      params: { id: item.id }
    });
  }
  add() {
    this.$router.push("NewTrack");
  }
  remove(item) {
    this.itemId = item.id;
    this.dialog = true;
  }

  onConfirm() {
    trackModule.deleteTrack(this.itemId);
    this.dialog = false;
  }
  onCancel() {
    this.itemId = -1;
    this.dialog = false;
  }
  getTrack() {
    buildSearchFilters(this.searchFilter);  
      this.query =this.searchFilter.contain.twisterNO;
    if (this.query != "") {
      //console.log("this.query is ", this.query);
      trackModule.getTrackById(this.query);
      this.query = "";
      //console.log("trackModule.track is ", trackModule.track);
      return trackModule.track;
    }
    return "";
  }

  searchTracks() {
    this.showSearchPanel = !this.showSearchPanel;
    buildSearchFilters(this.searchFilter);
    this.query = buildJsonServerQuery(this.searchFilter);
    this.quickSearch = "";
    trackModule.searchTracks(this.query);
  }

  clearSearchFilters() {
    this.showSearchPanel = !this.showSearchPanel;
    clearSearchFilters(this.searchFilter);
    trackModule.getAllTracks();
  }

  reloadData() {
    this.query = "";
    trackModule.getAllTracks();
  }

  clearData() {
    this.query = "";
    this.searchFilter.contain.twisterNO = "";
    trackModule.clearTracks();
  }

  updateSearchPanel() {
    this.rightDrawer = !this.rightDrawer;
  }

  cancelSearch() {
    this.showSearchPanel = false;
  }

  closeSnackbar() {
    appModule.closeNotice();
  }

  quickSearchCustomers = debounce(function() {
    trackModule.quickSearch(this.headers, this.quickSearchFilter);
  }, 500);

  get items() {
    return trackModule.items;
  }
  get pagination() {
    return trackModule.pagination;
  }
  get loading() {
    return appModule.loading;
  }
  get mode() {
    return appModule.mode;
  }
  get snackbar() {
    return appModule.snackbar;
  }
  get notice() {
    return appModule.notice;
  }

  get rightDrawer() {
    return this.showSearchPanel;
  }

  set rightDrawer(rightDrawer: boolean) {
    this.showSearchPanel = rightDrawer;
  }

  get quickSearch() {
    return this.quickSearchFilter;
  }
  set quickSearch(val) {
    this.quickSearchFilter = val;
    this.quickSearchFilter && this.quickSearchCustomers();
  }

  created() {
    trackModule.clearTracks();
  }

  mounted() {
    this.$nextTick(() => {
      //console.log(this.headers);
      this.title = "捻線機台查詢";
    });
  }
}
</script>
