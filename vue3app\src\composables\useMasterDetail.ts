import { ref, computed, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getData, postData, putData, deleteData } from '@/utils/backend-api'
import { useFormData, useListData } from './useReactiveData'
import { useFormValidation } from './useFormValidation'

// 主檔與明細檔關聯選項
export interface MasterDetailOptions<TMaster, TDetail> {
  // API 端點
  masterEndpoint: string
  detailEndpoint: string
  
  // 關聯欄位
  masterKeyField: keyof TMaster
  detailForeignKeyField: keyof TDetail
  
  // 預設數據
  defaultMasterData: TMaster
  defaultDetailData: TDetail
  
  // 路由
  listRoute: string
  
  // 回調函數
  onMasterSaved?: (master: TMaster) => void
  onMasterLoaded?: (master: TMaster) => void
  onDetailSaved?: (detail: TDetail) => void
  onDetailDeleted?: (detail: TDetail) => void
  onError?: (error: any, operation: string) => void
  
  // 驗證配置
  masterValidation?: any
  detailValidation?: any
  
  // 其他選項
  autoLoadDetails?: boolean
  cascadeDelete?: boolean
}

export function useMasterDetail<TMaster extends Record<string, any>, TDetail extends Record<string, any>>(
  options: MasterDetailOptions<TMaster, TDetail>
) {
  const router = useRouter()
  const route = useRoute()

  const {
    masterEndpoint,
    detailEndpoint,
    masterKeyField,
    detailForeignKeyField,
    defaultMasterData,
    defaultDetailData,
    listRoute,
    onMasterSaved,
    onMasterLoaded,
    onDetailSaved,
    onDetailDeleted,
    onError,
    masterValidation,
    detailValidation,
    autoLoadDetails = true,
    cascadeDelete = false
  } = options

  // 主檔數據管理
  const {
    formData: masterData,
    isDirty: isMasterDirty,
    updateFields: updateMasterFields,
    saveAsOriginal: saveMasterAsOriginal,
    resetData: resetMasterData
  } = useFormData(defaultMasterData)

  // 明細檔數據管理
  const {
    items: detailItems,
    loading: detailLoading,
    addItem: addDetailItem,
    updateItemByKey: updateDetailItem,
    removeItemByKey: removeDetailItem,
    setItems: setDetailItems,
    selectedItems: selectedDetailItems
  } = useListData<TDetail>()

  // 狀態管理
  const masterLoading = ref(false)
  const saving = ref(false)
  const deleting = ref(false)
  const initialized = ref(false)

  // 通知訊息
  const snackbar = ref({
    show: false,
    message: '',
    color: 'success'
  })

  // 計算屬性
  const isEditMode = computed(() => !!route.params.id)
  const masterId = computed(() => masterData.value[masterKeyField])
  const hasUnsavedChanges = computed(() => isMasterDirty.value)
  const canAddDetails = computed(() => !!masterId.value)

  // 顯示訊息
  const showMessage = (message: string, color: string = 'success') => {
    snackbar.value = {
      show: true,
      message,
      color
    }
  }

  // 錯誤處理
  const handleError = (error: any, operation: string) => {
    console.error(`${operation} error:`, error)
    if (onError) {
      onError(error, operation)
    } else {
      showMessage(`${operation}失敗，請稍後再試`, 'error')
    }
  }

  // 載入主檔數據
  const loadMasterData = async (id: string | number) => {
    if (!id) return

    masterLoading.value = true
    try {
      const response = await getData(`${masterEndpoint}/${id}`)
      updateMasterFields(response.data)
      saveMasterAsOriginal()
      
      if (onMasterLoaded) {
        onMasterLoaded(response.data)
      }

      if (autoLoadDetails) {
        await loadDetailData(id)
      }
    } catch (error) {
      handleError(error, '載入主檔數據')
    } finally {
      masterLoading.value = false
    }
  }

  // 載入明細檔數據
  const loadDetailData = async (masterId: string | number) => {
    if (!masterId) return

    detailLoading.value = true
    try {
      const response = await getData(`${detailEndpoint}?${String(detailForeignKeyField)}=${masterId}`)
      setDetailItems(response.data || [])
    } catch (error) {
      handleError(error, '載入明細數據')
    } finally {
      detailLoading.value = false
    }
  }

  // 儲存主檔
  const saveMaster = async (): Promise<boolean> => {
    saving.value = true
    try {
      let response
      if (isEditMode.value) {
        response = await putData(`${masterEndpoint}/${masterId.value}`, masterData.value)
        showMessage('主檔更新成功')
      } else {
        response = await postData(masterEndpoint, masterData.value)
        showMessage('主檔新增成功')
        
        // 新增成功後更新主檔ID
        if (response.data && response.data[masterKeyField]) {
          updateMasterFields({ [masterKeyField]: response.data[masterKeyField] } as Partial<TMaster>)
        }
      }

      saveMasterAsOriginal()
      
      if (onMasterSaved) {
        onMasterSaved(response.data)
      }

      return true
    } catch (error) {
      handleError(error, '儲存主檔')
      return false
    } finally {
      saving.value = false
    }
  }

  // 儲存明細
  const saveDetail = async (detailData: TDetail): Promise<boolean> => {
    if (!masterId.value) {
      showMessage('請先儲存主檔', 'warning')
      return false
    }

    saving.value = true
    try {
      const dataToSave = {
        ...detailData,
        [detailForeignKeyField]: masterId.value
      }

      let response
      const detailId = (detailData as any).id
      if (detailId) {
        response = await putData(`${detailEndpoint}/${detailId}`, dataToSave)
        updateDetailItem(detailId, response.data)
        showMessage('明細更新成功')
      } else {
        response = await postData(detailEndpoint, dataToSave)
        addDetailItem(response.data)
        showMessage('明細新增成功')
      }

      if (onDetailSaved) {
        onDetailSaved(response.data)
      }

      return true
    } catch (error) {
      handleError(error, '儲存明細')
      return false
    } finally {
      saving.value = false
    }
  }

  // 刪除明細
  const deleteDetail = async (detailId: string | number): Promise<boolean> => {
    deleting.value = true
    try {
      await deleteData(`${detailEndpoint}/${detailId}`)
      removeDetailItem(detailId)
      showMessage('明細刪除成功')

      if (onDetailDeleted) {
        const deletedItem = detailItems.value.find(item => (item as any).id === detailId)
        if (deletedItem) {
          onDetailDeleted(deletedItem)
        }
      }

      return true
    } catch (error) {
      handleError(error, '刪除明細')
      return false
    } finally {
      deleting.value = false
    }
  }

  // 刪除主檔
  const deleteMaster = async (): Promise<boolean> => {
    if (!masterId.value) return false

    deleting.value = true
    try {
      if (cascadeDelete && detailItems.value.length > 0) {
        // 先刪除所有明細
        for (const detail of detailItems.value) {
          await deleteData(`${detailEndpoint}/${(detail as any).id}`)
        }
      }

      await deleteData(`${masterEndpoint}/${masterId.value}`)
      showMessage('刪除成功')
      router.push(listRoute)
      return true
    } catch (error) {
      handleError(error, '刪除主檔')
      return false
    } finally {
      deleting.value = false
    }
  }

  // 取消編輯
  const cancel = () => {
    router.push(listRoute)
  }

  // 重置表單
  const resetForm = () => {
    resetMasterData()
    setDetailItems([])
  }

  // 初始化
  const initialize = async () => {
    if (initialized.value) return

    try {
      if (isEditMode.value && route.params.id) {
        await loadMasterData(route.params.id as string)
      } else {
        updateMasterFields(defaultMasterData)
      }
      initialized.value = true
    } catch (error) {
      handleError(error, '初始化')
    }
  }

  // 監聽路由變化
  watch(() => route.params.id, async (newId, oldId) => {
    if (newId !== oldId && initialized.value) {
      if (newId) {
        await loadMasterData(newId as string)
      } else {
        resetForm()
      }
    }
  })

  return {
    // 數據
    masterData,
    detailItems,
    selectedDetailItems,
    snackbar,
    
    // 狀態
    masterLoading,
    detailLoading,
    saving,
    deleting,
    initialized,
    
    // 計算屬性
    isEditMode,
    masterId,
    hasUnsavedChanges,
    canAddDetails,
    
    // 方法
    showMessage,
    loadMasterData,
    loadDetailData,
    saveMaster,
    saveDetail,
    deleteDetail,
    deleteMaster,
    cancel,
    resetForm,
    initialize,
    
    // 數據操作
    updateMasterFields,
    saveMasterAsOriginal,
    resetMasterData,
    addDetailItem,
    updateDetailItem,
    removeDetailItem,
    setDetailItems
  }
}
