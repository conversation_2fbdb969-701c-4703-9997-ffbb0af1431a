
const winston = require('winston');
const { combine, timestamp, printf } = winston.format;
const fs = require('fs');
const path = require('path');

const logDirectory = path.join(process.env.HOME, 'log');
fs.existsSync(logDirectory) || fs.mkdirSync(logDirectory);

const myFormat = printf(({ level, message, timestamp }) => {
    return `${timestamp} [${level.toUpperCase()}]: ${message}`;
});

const currentDate = new Date().toISOString().slice(0, 10);

const errorLogger = winston.createLogger({
    format: combine(
        timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        myFormat
    ),
    transports: [
        new winston.transports.File({ filename: path.join(logDirectory, `${currentDate}-error.log`), level: 'error' })
    ]
});

const infoLogger = winston.createLogger({
    format: combine(
        timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        myFormat
    ),
    transports: [
        new winston.transports.File({ filename: path.join(logDirectory, `${currentDate}-info.log`), level: 'info' })
    ]
});

module.exports = {
    errorLogger,
    infoLogger
};