import { Component, Vue } from 'vue-facing-decorator'
import { h, createApp } from 'vue'

export interface AppState {
  loading: boolean
  mode: string
  snackbar: boolean
  notice: string
}

const SUCCESS = "success"
const ERROR = "error"

@Component({
  name: 'AppModule'
})
export default class AppModule extends Vue implements AppState {
  loading = true
  mode = ""
  snackbar = false
  notice = ""

  render() {
    return h('div')
  }

  closeNotice(): void {
    this.setNotice("")
    this.setMode("")
    this.setSnackbar(false)
  }

  closeNoticeWithDelay(timeout = 2000): void {
    setTimeout(() => {
      this.setNotice("")
      this.setMode("")
      this.setSnackbar(false)
    }, timeout)
  }

  sendSuccessNotice(notice: string): void {
    this.setNotice(notice)
    this.setMode(SUCCESS)
    this.setSnackbar(true)
    this.closeNoticeWithDelay()
  }

  sendErrorNotice(notice: string): void {
    this.setNotice(notice)
    this.setMode(ERROR)
    this.setSnackbar(true)
    this.closeNoticeWithDelay()
  }

  setLoading(loading: boolean): void {
    this.loading = loading
  }

  setNotice(notice: string): void {
    this.notice = notice
  }

  setSnackbar(snackbar: boolean): void {
    this.snackbar = snackbar
  }

  setMode(mode: string): void {
    this.mode = mode
  }
}

// Create and export a singleton instance
const app = createApp(AppModule)
const vm = app.mount(document.createElement('div'))
export const appModule = vm as InstanceType<typeof AppModule>
