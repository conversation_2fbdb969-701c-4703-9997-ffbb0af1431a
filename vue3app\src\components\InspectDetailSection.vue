<template>
  <v-card class="mt-4">
    <v-card-title>
      <span class="text-h6">明細記錄</span>
      <v-spacer></v-spacer>
      <v-btn
        color="primary"
        @click="openDetailDialog"
        prepend-icon="mdi-plus"
        :disabled="!inspectId"
      >
        新增明細
      </v-btn>
    </v-card-title>

    <v-card-text>
      <!-- 載入指示器 -->
      <v-progress-linear
        v-if="loading"
        indeterminate
        class="mb-4"
      ></v-progress-linear>

      <!-- 明細表格 -->
      <v-data-table
        :headers="detailHeaders"
        :items="detailItems"
        :items-per-page="10"
        class="elevation-1"
        :loading="loading"
      >
        <template v-slot:item.actions="{ item }">
          <v-btn
            size="small"
            color="primary"
            variant="text"
            @click="editDetailItem(item)"
            class="mr-2"
          >
            編輯
          </v-btn>
          <v-btn
            size="small"
            color="error"
            variant="text"
            @click="deleteDetailItem(item)"
          >
            刪除
          </v-btn>
        </template>

        <template v-slot:no-data>
          <v-alert type="info" variant="tonal">
            尚無明細記錄
          </v-alert>
        </template>
      </v-data-table>
    </v-card-text>

    <!-- 明細編輯對話框 -->
    <v-dialog v-model="detailDialog" max-width="600px">
      <v-card>
        <v-card-title>
          <span class="text-h6">{{ isEditing ? '編輯' : '新增' }}明細</span>
        </v-card-title>

        <v-card-text>
          <v-form ref="detailForm" v-model="detailFormValid" lazy-validation>
            <v-text-field
              v-model="detailData.codeName"
              label="Cap QRCode"
              variant="outlined"
              :rules="validationRules.required"
              required
              @blur="handleQRCodeInput"
              @keyup.enter="handleQRCodeInput"
              :loading="qrCodeLoading"
              append-inner-icon="mdi-qrcode-scan"
            ></v-text-field>

            <!-- 產品資料顯示區域 -->
            <v-card
              v-if="productInfo && Object.keys(productInfo).length > 0"
              variant="outlined"
              class="mb-4 pa-3"
            >
              <v-card-title class="text-h6 pa-0 mb-2">產品資料</v-card-title>
              <v-row dense>
                <v-col cols="6" v-if="productInfo.productName">
                  <v-text-field
                    :model-value="productInfo.productName"
                    label="產品名稱"
                    variant="outlined"
                    density="compact"
                    readonly
                  ></v-text-field>
                </v-col>
                <v-col cols="6" v-if="productInfo.categoryName">
                  <v-text-field
                    :model-value="productInfo.categoryName"
                    label="分類"
                    variant="outlined"
                    density="compact"
                    readonly
                  ></v-text-field>
                </v-col>
                <v-col cols="6" v-if="productInfo.gradeName">
                  <v-text-field
                    :model-value="productInfo.gradeName"
                    label="等級"
                    variant="outlined"
                    density="compact"
                    readonly
                  ></v-text-field>
                </v-col>
                <v-col cols="6" v-if="productInfo.furnaceName">
                  <v-text-field
                    :model-value="productInfo.furnaceName"
                    label="爐號"
                    variant="outlined"
                    density="compact"
                    readonly
                  ></v-text-field>
                </v-col>
                <v-col cols="6" v-if="productInfo.bushingNO">
                  <v-text-field
                    :model-value="productInfo.bushingNO"
                    label="Bushing NO"
                    variant="outlined"
                    density="compact"
                    readonly
                  ></v-text-field>
                </v-col>
                <v-col cols="6" v-if="productInfo.cakeWeight">
                  <v-text-field
                    :model-value="productInfo.cakeWeight"
                    label="餅重"
                    variant="outlined"
                    density="compact"
                    readonly
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-card>

            <v-select
              v-model="detailData.remarkName"
              :items="remarkOptions"
              item-title="text"
              item-value="remarkName"
              label="異常原因"
              variant="outlined"
              :rules="validationRules.required"
              required
              clearable
            ></v-select>

            <v-text-field
              v-model="detailData.quantity"
              label="數量"
              type="number"
              variant="outlined"
              :rules="validationRules.number"
              required
            ></v-text-field>
          </v-form>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey"
            variant="text"
            @click="closeDetailDialog"
          >
            取消
          </v-btn>
          <v-btn
            color="primary"
            variant="text"
            @click="saveDetailItem"
            :disabled="!detailFormValid"
            :loading="loading"
          >
            儲存
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 訊息提示 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="3000"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          關閉
        </v-btn>
      </template>
    </v-snackbar>
  </v-card>
</template>

<script setup lang="ts">
import { watch, onMounted, computed, ref } from 'vue'
import { useInspectDetail } from '@/composables/useInspectDetail'
import { useProductsStore } from '@/stores/products'
import type { Inspectline } from '@/types'

interface Props {
  inspectId: number | null
  detailItems?: Inspectline[]
  detailHeaders?: any[]
}

interface Emits {
  (e: 'detail-saved', detail: Inspectline): void
  (e: 'detail-deleted', detail: Inspectline): void
  (e: 'error', error: any, operation: string): void
}

const props = withDefaults(defineProps<Props>(), {
  detailItems: () => [],
  detailHeaders: () => []
})

const emit = defineEmits<Emits>()

// 使用 products store 獲取異常原因選項和產品資料
const productsStore = useProductsStore()

// QRCode 相關狀態
const qrCodeLoading = ref(false)
const productInfo = ref({})

// 使用明細檔組合式函數
const {
  // 響應式數據
  detailItems: localDetailItems,
  detailDialog,
  detailForm,
  detailFormValid,
  editingDetailId,
  loading,
  detailData,
  snackbar,

  // 配置
  detailHeaders: defaultHeaders,
  validationRules,

  // 計算屬性
  isEditing,

  // 方法
  openDetailDialog,
  editDetailItem,
  deleteDetailItem,
  saveDetailItem,
  closeDetailDialog,
  loadDetailData
} = useInspectDetail({
  inspectId: props.inspectId,
  onDetailSaved: (detail: Inspectline) => {
    emit('detail-saved', detail)
  },
  onDetailDeleted: (detail: Inspectline) => {
    emit('detail-deleted', detail)
  },
  onError: (error: any, operation: string) => {
    emit('error', error, operation)
  }
})

// 使用傳入的 headers 或默認 headers
const detailHeaders = props.detailHeaders.length > 0 ? props.detailHeaders : defaultHeaders

// 異常原因選項
const remarkOptions = computed(() => {
  return productsStore.allRemarks.map(remark => ({
    text: remark.remarkName || remark.name || remark.text,
    remarkName: remark.remarkName || remark.name || remark.text,
    value: remark.id
  }))
})

// 監聽 inspectId 變化，重新載入數據
watch(() => props.inspectId, (newId) => {
  if (newId) {
    loadDetailData()
  }
}, { immediate: true })

// 監聽外部 detailItems 變化
watch(() => props.detailItems, (newItems) => {
  if (newItems && newItems.length > 0) {
    localDetailItems.value = [...newItems]
  }
}, { deep: true, immediate: true })

// 處理 QRCode 輸入
const handleQRCodeInput = async () => {
  const qrCode = detailData.value.codeName?.trim()
  if (!qrCode) {
    productInfo.value = {}
    return
  }

  try {
    qrCodeLoading.value = true
    const productData = await productsStore.getProductByQRCode(qrCode)

    if (productData && productData.length > 0) {
      // 取第一筆資料
      const product = productData[0]
      productInfo.value = {
        productName: product.productName,
        categoryName: product.categoryName,
        gradeName: product.gradeName,
        furnaceName: product.furnaceName,
        bushingNO: product.bushingNO,
        cakeWeight: product.cakeWeight,
        positionName: product.positionName,
        workDate: product.workDate,
        twisterNO: product.twisterNO,
        spindleNO: product.spindleNO,
        texName: product.texName,
        biName: product.biName,
        batchName: product.batchName
      }

      // 自動填入產品相關資料到明細表單
      if (product.productName) {
        // 可以根據需要自動填入其他欄位
      }

      showMessage('產品資料載入成功', 'success')
    } else {
      productInfo.value = {}
      showMessage('找不到對應的產品資料', 'warning')
    }
  } catch (error) {
    console.error('查詢產品資料失敗:', error)
    productInfo.value = {}
    showMessage('查詢產品資料失敗', 'error')
  } finally {
    qrCodeLoading.value = false
  }
}

// 載入異常原因數據
onMounted(async () => {
  try {
    await productsStore.getRemarks()
  } catch (error) {
    console.error('載入異常原因失敗:', error)
  }
})
</script>

<style scoped>
.v-card {
  margin-top: 16px;
}

.v-data-table {
  border-radius: 4px;
}

.v-btn {
  margin-left: 8px;
}

.text-h6 {
  font-weight: 500;
}
</style>
