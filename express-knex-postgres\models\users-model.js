const db = require("../config/dbConfig.js");
const {infoLogger, errorLogger} = require("../config/winston.js");

// GET ALL USERS
const find = () => {
  return db("aits_users");
};

// GET SPECIFIC USER BY ID
const findById = id => {
  return db("aits_users")
    .where("id", id)
    .first();
};

// FIND USER BY EMAIL
const findByEmail = email => {
  infoLogger.info(`Attempting to find user with email: ${email}`);
  return db("aits_users")
    .where("email", email)
    .first()
    .then(user => {
      if (user) {
        infoLogger.info(`User found with email: ${email}`);
      } else {
        infoLogger.info(`No user found with email: ${email}`);
      }
      return user;
    })
    .catch(error => {
      errorLogger.error(`Error finding user by email: ${error.message}`);
      throw error;
    });
};

// FIND USER BY USERNAME
const findByUsername = username => {
  infoLogger.info(`Attempting to find user with username: ${username}`);
  return db("aits_users")
    .where("username", username)
    .first()
    .then(user => {
      if (user) {
        infoLogger.info(`User found with username: ${username}`);
      } else {
        infoLogger.info(`No user found with username: ${username}`);
      }
      return user;
    })
    .catch(error => {
      errorLogger.error(`Error finding user by username: ${error.message}`);
      throw error;
    });
};

// ADD A USER
const addUser = async user => {
  try {
    infoLogger.info(`Adding new user: ${user.email}`);
    const [id] = await db("aits_users").insert(user, "id");
    return id;
  } catch (error) {
    errorLogger.error(`Error adding user: ${error.message}`);
    throw error;
  }
};

// UPDATE USER
const updateUser = async (id, updates) => {
  try {
    infoLogger.info(`Updating user with id: ${id}`);
    const count = await db("aits_users")
      .where("id", id)
      .update(updates);
    return count;
  } catch (error) {
    errorLogger.error(`Error updating user: ${error.message}`);
    throw error;
  }
};

// REMOVE USER
const removeUser = async id => {
  try {
    infoLogger.info(`Removing user with id: ${id}`);
    return await db("aits_users")
      .where("id", id)
      .del();
  } catch (error) {
    errorLogger.error(`Error removing user: ${error.message}`);
    throw error;
  }
};

module.exports = {
  find,
  findById,
  findByEmail,
  findByUsername,
  addUser,
  updateUser,
  removeUser
};
