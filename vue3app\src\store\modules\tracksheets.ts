import { Component, Vue } from 'vue-facing-decorator'
import { createApp } from 'vue'
import { getData } from '@/utils/backend-api'
import { Entity, Tracksheet, Pagination, TableHeader } from '@/types'
import { getDefaultPagination, getPagination } from '@/utils/store-util'
import { appModule } from '@/stores/app'
import { get } from 'lodash'

export interface TracksheetState {
  items: Entity[]
  pagination: Pagination
  loading: boolean
  tracksheet: Tracksheet
}

@Component({
  name: 'TracksheetModule'
})
class TracksheetModule extends Vue implements TracksheetState {
  items: Entity[] = []
  pagination = getDefaultPagination()
  loading = false
  tracksheet = {} as Tracksheet

  getTracksheetByCode = async (tracks: string[]) => {
    this.setLoading(true)
    const type = tracks[0].toString()
    const id = tracks[1].toString()
    const trackId = tracks[2].toString()
    try {
      if (id) {
        const res = await getData(`tracksheets/${type}/${id}`)
        const tracksheets = res.data
        if (tracksheets && tracksheets.length > 0) {
          if (type === "CAKE") {
            tracksheets[0].tracksheetNO = trackId
          }
          this.setDataTable(tracksheets)
          this.setTracksheet(tracksheets[0])
        }
      } else {
        this.setTracksheet({} as Tracksheet)
      }
    } catch (err) {
      console.log(err)
      appModule.sendErrorNotice("Operation failed! Please try again later.")
      appModule.closeNoticeWithDelay(5000)
    } finally {
      this.setLoading(false)
    }
  }

  clearTracksheets = () => {
    this.setLoading(true)
    const tracksheets: Tracksheet[] = []
    const tracksheet = {} as Tracksheet
    this.setDataTable(tracksheets)
    this.setTracksheet(tracksheet)
    this.setLoading(false)
  }

  searchTracksheets = (searchQuery: string) => {
    getData("tracksheets" + searchQuery).then(res => {
      const tracksheets = res.data
      this.setDataTable(tracksheets)
      this.setLoading(false)
    })
  }

  quickSearch = (headers: TableHeader[], qsFilter: string): void => {
    getData("tracksheets").then(res => {
      const tracksheets = res.data.filter((r: any) =>
        headers.some((header: TableHeader) => {
          const val = get(r, [header.value])
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter)) ||
            false
          )
        })
      )
      this.setDataTable(tracksheets)
      this.setLoading(false)
    })
  }

  setDataTable = (items: Tracksheet[]) => {
    const pagination = getPagination(items)
    this.setPagination(pagination)
    this.setItems(items)
  }

  setItems = (tracksheets: Tracksheet[]) => {
    this.items = tracksheets
  }

  setPagination = (pagination: Pagination) => {
    this.pagination = pagination
  }

  setLoading = (loading: boolean) => {
    this.loading = loading
  }

  setTracksheet = (tracksheet: Tracksheet) => {
    this.tracksheet = tracksheet
  }
}

// Create and export a singleton instance
const app = createApp(TracksheetModule)
const vm = app.mount(document.createElement('div'))
export const tracksheetModule = vm as InstanceType<typeof TracksheetModule>
