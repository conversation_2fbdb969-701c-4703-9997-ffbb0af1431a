/**
 * Android Layout 統一配置檔
 * 用於控制所有表單的Android優化設定，避免逐一修改表單
 */

export interface AndroidLayoutConfig {
  // 全域開關
  enabled: boolean
  
  // 設備檢測設定
  device: {
    mobileBreakpoint: number
    tabletBreakpoint: number
    desktopBreakpoint: number
    touchMinSize: number
  }
  
  // 響應式布局設定
  layout: {
    mobile: {
      containerPadding: string
      cardMargin: string
      rowSpacing: string
      colSpacing: string
      useCardList: boolean
      simplifyHeaders: boolean
    }
    tablet: {
      containerPadding: string
      cardMargin: string
      rowSpacing: string
      colSpacing: string
      useCardList: boolean
      simplifyHeaders: boolean
    }
    desktop: {
      containerPadding: string
      cardMargin: string
      rowSpacing: string
      colSpacing: string
      useCardList: boolean
      simplifyHeaders: boolean
    }
  }
  
  // 組件設定
  components: {
    button: {
      mobile: {
        size: 'small' | 'default' | 'large' | 'x-large'
        block: boolean
        minHeight: number
        ripple: boolean
      }
      desktop: {
        size: 'small' | 'default' | 'large' | 'x-large'
        block: boolean
        minHeight: number
        ripple: boolean
      }
    }
    textField: {
      mobile: {
        variant: 'filled' | 'outlined' | 'underlined' | 'solo' | 'solo-inverted' | 'solo-filled'
        density: 'default' | 'comfortable' | 'compact'
        fontSize: string
        minHeight: number
      }
      desktop: {
        variant: 'filled' | 'outlined' | 'underlined' | 'solo' | 'solo-inverted' | 'solo-filled'
        density: 'default' | 'comfortable' | 'compact'
        fontSize: string
        minHeight: number
      }
    }
    card: {
      mobile: {
        elevation: number
        variant: 'elevated' | 'flat' | 'tonal' | 'outlined' | 'text' | 'plain'
        rounded: boolean | string
      }
      desktop: {
        elevation: number
        variant: 'elevated' | 'flat' | 'tonal' | 'outlined' | 'text' | 'plain'
        rounded: boolean | string
      }
    }
    dataTable: {
      mobile: {
        density: 'default' | 'comfortable' | 'compact'
        itemsPerPage: number
        hideHeaders: boolean
        useCardList: boolean
      }
      desktop: {
        density: 'default' | 'comfortable' | 'compact'
        itemsPerPage: number
        hideHeaders: boolean
        useCardList: boolean
      }
    }
    snackbar: {
      mobile: {
        location: 'top' | 'bottom' | 'left' | 'right' | 'center'
        timeout: number
        multiLine: boolean
      }
      desktop: {
        location: 'top' | 'bottom' | 'left' | 'right' | 'center'
        timeout: number
        multiLine: boolean
      }
    }
  }
  
  // 功能開關
  features: {
    deviceInfo: boolean
    fab: boolean
    safeArea: boolean
    touchOptimization: boolean
    autoFocus: boolean
    loadingOptimization: boolean
  }
  
  // 樣式覆蓋
  styles: {
    cssClasses: {
      mobileContainer: string
      mobileCard: string
      mobileButton: string
      mobileTextField: string
      mobileTable: string
    }
    customCSS: string
  }
}

// 預設Android Layout配置
export const defaultAndroidLayoutConfig: AndroidLayoutConfig = {
  enabled: true,
  
  device: {
    mobileBreakpoint: 600,
    tabletBreakpoint: 960,
    desktopBreakpoint: 1280,
    touchMinSize: 44
  },
  
  layout: {
    mobile: {
      containerPadding: 'pa-2',
      cardMargin: 'mb-3',
      rowSpacing: 'dense',
      colSpacing: 'pa-2',
      useCardList: true,
      simplifyHeaders: true
    },
    tablet: {
      containerPadding: 'pa-3',
      cardMargin: 'mb-4',
      rowSpacing: 'normal',
      colSpacing: 'pa-3',
      useCardList: false,
      simplifyHeaders: false
    },
    desktop: {
      containerPadding: 'pa-4',
      cardMargin: 'mb-4',
      rowSpacing: 'normal',
      colSpacing: 'pa-4',
      useCardList: false,
      simplifyHeaders: false
    }
  },
  
  components: {
    button: {
      mobile: {
        size: 'large',
        block: true,
        minHeight: 44,
        ripple: true
      },
      desktop: {
        size: 'default',
        block: false,
        minHeight: 36,
        ripple: true
      }
    },
    textField: {
      mobile: {
        variant: 'outlined',
        density: 'comfortable',
        fontSize: '16px',
        minHeight: 48
      },
      desktop: {
        variant: 'outlined',
        density: 'default',
        fontSize: '14px',
        minHeight: 40
      }
    },
    card: {
      mobile: {
        elevation: 1,
        variant: 'outlined',
        rounded: true
      },
      desktop: {
        elevation: 2,
        variant: 'elevated',
        rounded: true
      }
    },
    dataTable: {
      mobile: {
        density: 'compact',
        itemsPerPage: 5,
        hideHeaders: false,
        useCardList: true
      },
      desktop: {
        density: 'default',
        itemsPerPage: 10,
        hideHeaders: false,
        useCardList: false
      }
    },
    snackbar: {
      mobile: {
        location: 'bottom',
        timeout: 3000,
        multiLine: true
      },
      desktop: {
        location: 'top',
        timeout: 3000,
        multiLine: false
      }
    }
  },
  
  features: {
    deviceInfo: true,
    fab: true,
    safeArea: true,
    touchOptimization: true,
    autoFocus: false, // 移動端不自動聚焦
    loadingOptimization: true
  },
  
  styles: {
    cssClasses: {
      mobileContainer: 'android-mobile-container',
      mobileCard: 'android-mobile-card',
      mobileButton: 'android-mobile-button',
      mobileTextField: 'android-mobile-textfield',
      mobileTable: 'android-mobile-table'
    },
    customCSS: `
      /* Android Layout 自定義樣式 */
      .android-mobile-container {
        padding: 8px !important;
      }
      
      .android-mobile-card {
        margin-bottom: 12px !important;
        border-radius: 12px !important;
      }
      
      .android-mobile-button {
        min-height: 44px !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
      }
      
      .android-mobile-textfield input {
        font-size: 16px !important;
      }
      
      .android-mobile-table {
        font-size: 14px !important;
      }
      
      .android-mobile-table .v-data-table__td {
        padding: 8px 4px !important;
      }
    `
  }
}

// 環境特定配置
export const environmentConfigs = {
  development: {
    ...defaultAndroidLayoutConfig,
    features: {
      ...defaultAndroidLayoutConfig.features,
      deviceInfo: true // 開發環境顯示設備信息
    }
  },
  
  production: {
    ...defaultAndroidLayoutConfig,
    features: {
      ...defaultAndroidLayoutConfig.features,
      deviceInfo: false // 生產環境隱藏設備信息
    }
  },
  
  testing: {
    ...defaultAndroidLayoutConfig,
    features: {
      ...defaultAndroidLayoutConfig.features,
      deviceInfo: true,
      loadingOptimization: false // 測試環境不優化載入時間
    }
  }
}

// 獲取當前環境配置
export const getCurrentConfig = (): AndroidLayoutConfig => {
  const env = process.env.NODE_ENV || 'development'
  return environmentConfigs[env as keyof typeof environmentConfigs] || defaultAndroidLayoutConfig
}

// 配置合併函數
export const mergeConfig = (baseConfig: AndroidLayoutConfig, overrides: Partial<AndroidLayoutConfig>): AndroidLayoutConfig => {
  return {
    ...baseConfig,
    ...overrides,
    device: { ...baseConfig.device, ...overrides.device },
    layout: {
      mobile: { ...baseConfig.layout.mobile, ...overrides.layout?.mobile },
      tablet: { ...baseConfig.layout.tablet, ...overrides.layout?.tablet },
      desktop: { ...baseConfig.layout.desktop, ...overrides.layout?.desktop }
    },
    components: {
      button: {
        mobile: { ...baseConfig.components.button.mobile, ...overrides.components?.button?.mobile },
        desktop: { ...baseConfig.components.button.desktop, ...overrides.components?.button?.desktop }
      },
      textField: {
        mobile: { ...baseConfig.components.textField.mobile, ...overrides.components?.textField?.mobile },
        desktop: { ...baseConfig.components.textField.desktop, ...overrides.components?.textField?.desktop }
      },
      card: {
        mobile: { ...baseConfig.components.card.mobile, ...overrides.components?.card?.mobile },
        desktop: { ...baseConfig.components.card.desktop, ...overrides.components?.card?.desktop }
      },
      dataTable: {
        mobile: { ...baseConfig.components.dataTable.mobile, ...overrides.components?.dataTable?.mobile },
        desktop: { ...baseConfig.components.dataTable.desktop, ...overrides.components?.dataTable?.desktop }
      },
      snackbar: {
        mobile: { ...baseConfig.components.snackbar.mobile, ...overrides.components?.snackbar?.mobile },
        desktop: { ...baseConfig.components.snackbar.desktop, ...overrides.components?.snackbar?.desktop }
      }
    },
    features: { ...baseConfig.features, ...overrides.features },
    styles: {
      cssClasses: { ...baseConfig.styles.cssClasses, ...overrides.styles?.cssClasses },
      customCSS: overrides.styles?.customCSS || baseConfig.styles.customCSS
    }
  }
}
