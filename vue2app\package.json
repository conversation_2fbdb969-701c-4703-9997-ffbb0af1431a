{"name": "vue2app", "version": "2.2.4", "private": true, "scripts": {"start": "vue-cli-service serve", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.21.3", "chart.js": "2.9.3", "core-js": "^3.6.4", "moment": "^2.29.4", "npm": "^8.3.1", "register-service-worker": "^1.7.1", "tslib": "^2.4.0", "vue": "^2.6.11", "vue-chartjs": "^3.5.0", "vue-class-component": "^7.2.3", "vue-progressbar": "^0.7.5", "vue-property-decorator": "^8.4.1", "vue-router": "^3.1.6", "vuetify": "^2.6.12", "vuex": "^3.1.3"}, "devDependencies": {"@mdi/font": "^5.1.45", "@types/chai": "^4.2.11", "@types/chart.js": "^2.9.19", "@types/lodash": "^4.14.150", "@types/mocha": "^5.2.4", "@types/node": "^13.13.5", "@typescript-eslint/eslint-plugin": "^2.26.0", "@typescript-eslint/parser": "^2.26.0", "@vue/cli-plugin-babel": "~4.3.0", "@vue/cli-plugin-e2e-cypress": "~4.3.0", "@vue/cli-plugin-eslint": "~4.3.0", "@vue/cli-plugin-pwa": "^4.3.1", "@vue/cli-plugin-router": "~4.3.0", "@vue/cli-plugin-typescript": "~4.3.0", "@vue/cli-plugin-unit-mocha": "~4.3.0", "@vue/cli-plugin-vuex": "~4.3.0", "@vue/cli-service": "~4.3.0", "@vue/compiler-dom": "^3.2.41", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "@vue/runtime-dom": "^3.3.4", "@vue/test-utils": "1.0.0-beta.31", "chai": "^4.1.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-vue": "^6.2.2", "prettier": "^1.19.1", "sass": "^1.19.0", "sass-loader": "^8.0.0", "typescript": "~3.8.3", "vue-cli-plugin-vuetify": "~2.0.5", "vue-cli-plugin-vuetify-preset-fortnightly": "^1.0.3", "vue-loader": "^15.9.2", "vue-template-compiler": "^2.6.11", "vuetify-loader": "^1.3.0", "vuex-class": "^0.3.2", "vuex-module-decorators": "^0.17.0"}}