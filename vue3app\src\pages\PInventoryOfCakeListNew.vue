<template>
  <v-container fluid>
    <v-card>
      <v-card-title>
        <span class="title">Cake盤點</span>
        <v-spacer></v-spacer>
        <v-btn
          elevation="4"
          color="green"
          size="small"
          icon
          class="mr-2"
          @click="add"
        >
          <v-icon>mdi-plus</v-icon>
        </v-btn>
        <v-btn
          elevation="4"
          color="brown-lighten-1"
          size="small"
          icon
          class="mr-2"
          @click="reloadData"
        >
          <v-icon>mdi-refresh</v-icon>
        </v-btn>
      </v-card-title>

      <!-- Loading indicator -->
      <v-card-text v-if="loading">
        <v-progress-linear indeterminate></v-progress-linear>
        <p class="text-center mt-2">載入中...</p>
      </v-card-text>

      <!-- Results table -->
      <v-card-text v-if="!loading && items.length > 0">
        <v-data-table
          :headers="headers"
          :items="items"
          :items-per-page="10"
          class="elevation-1"
        >
          <template v-slot:item.actions="{ item }">
            <v-btn
              size="small"
              color="primary"
              variant="text"
              @click="edit(item)"
            >
              編輯
            </v-btn>
            <v-btn
              size="small"
              color="error"
              variant="text"
              @click="remove(item)"
            >
              刪除
            </v-btn>
          </template>
        </v-data-table>
      </v-card-text>

      <!-- No results message -->
      <v-card-text v-if="!loading && items.length === 0">
        <v-alert type="info" variant="tonal">
          沒有找到相關資料
        </v-alert>
      </v-card-text>
    </v-card>

    <!-- Delete confirmation dialog -->
    <v-dialog v-model="dialog" max-width="400">
      <v-card>
        <v-card-title>Cake盤點刪除確認</v-card-title>
        <v-card-text>刪除該筆記錄?</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey" variant="text" @click="onCancel">取消</v-btn>
          <v-btn color="error" variant="text" @click="onConfirm">確認</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Snackbar for notifications -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
      location="top end"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn variant="text" @click="snackbar.show = false">Close</v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getData, deleteData } from '@/utils/backend-api'

interface Header {
  title: string
  key: string
  align?: string
  sortable?: boolean
}

interface CakeItem {
  id: number
  classDate: string
  shiftName: string
  employName: string
  groupName: string
  quantity: number
  [key: string]: any
}

// Reactive state
const router = useRouter()
const loading = ref(false)
const items = ref<CakeItem[]>([])
const dialog = ref(false)
const itemId = ref(-1)

const snackbar = ref({
  show: false,
  message: "",
  color: "error"
})

// Headers for the data table
const headers: Header[] = [
  { title: "單號", key: "id", align: "start" },
  { title: "日期", key: "classDate", align: "start" },
  { title: "勤別", key: "shiftName", align: "start" },
  { title: "人員", key: "employName", align: "start" },
  { title: "組別", key: "groupName", align: "start" },
  { title: "個數", key: "quantity", align: "start" },
  { title: "操作", key: "actions", sortable: false }
]

// Methods
const showError = (message: string) => {
  snackbar.value = {
    show: true,
    message,
    color: "error"
  }
}

const showSuccess = (message: string) => {
  snackbar.value = {
    show: true,
    message,
    color: "success"
  }
}

const edit = (item: CakeItem) => {
  router.push(`/pinventoryofcake/${item.id}`)
}

const add = () => {
  router.push("/pinventoriesofcake/new")
}

const remove = (item: CakeItem) => {
  itemId.value = item.id
  dialog.value = true
}

const onConfirm = async () => {
  try {
    await deleteData(`pinventoriesofcake/${itemId.value}`)
    dialog.value = false
    showSuccess("刪除成功")
    await loadCakes()
  } catch (error) {
    console.error('Delete error:', error)
    showError("刪除失敗，請稍後再試")
  }
}

const onCancel = () => {
  itemId.value = -1
  dialog.value = false
}

const loadCakes = async () => {
  loading.value = true
  try {
    const response = await getData('pinventoriesofcake')
    if (response.data && Array.isArray(response.data)) {
      items.value = response.data
    } else {
      items.value = []
    }
  } catch (error) {
    console.error('API Error:', error)
    showError("載入資料失敗，請稍後再試")
    items.value = []
  } finally {
    loading.value = false
  }
}

const reloadData = () => {
  loadCakes()
}

// Lifecycle
onMounted(() => {
  loadCakes()
})
</script>
