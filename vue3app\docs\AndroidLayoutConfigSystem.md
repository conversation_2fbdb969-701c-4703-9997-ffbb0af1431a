# Android Layout 統一配置系統

## 📱 概述

Android Layout 統一配置系統是一個完整的解決方案，讓您可以通過設定檔來統一控制所有表單的Android優化，避免逐一修改每個表單。

## 🎯 主要優勢

### 1. 統一配置管理
- **單一配置檔**: 所有Android優化設定集中管理
- **環境特定配置**: 支援開發、測試、生產環境的不同配置
- **動態配置合併**: 支援基礎配置與自定義配置的合併

### 2. 組件化設計
- **即插即用組件**: 提供預製的Android優化組件
- **統一API**: 所有組件使用相同的配置接口
- **自動響應式**: 根據設備類型自動調整組件行為

### 3. 開發效率提升
- **無需逐一修改**: 現有表單只需替換組件即可獲得Android優化
- **快速部署**: 新表單可以直接使用預配置的組件
- **維護簡單**: 修改配置即可影響所有使用該配置的表單

## 🔧 系統架構

### 核心文件結構
```
src/
├── config/
│   └── android-layout-config.ts     # 配置定義和預設值
├── composables/
│   └── useAndroidLayout.ts          # 核心Composable
├── components/
│   └── AndroidLayout/
│       ├── index.ts                 # 統一導出
│       ├── AndroidContainer.vue     # 容器組件
│       ├── AndroidButton.vue        # 按鈕組件
│       ├── AndroidTextField.vue     # 文字輸入框組件
│       └── AndroidDataTable.vue     # 數據表格組件
└── pages/
    └── EMProductionQueryWithConfig.vue  # 使用示例
```

## 🚀 快速開始

### 1. 基本使用

```vue
<template>
  <AndroidContainer>
    <!-- 使用Android優化的按鈕 -->
    <AndroidButton 
      color="primary" 
      @click="handleClick"
      prepend-icon="mdi-search"
    >
      搜尋
    </AndroidButton>
    
    <!-- 使用Android優化的文字輸入框 -->
    <AndroidTextField
      v-model="searchQuery"
      label="搜尋關鍵字"
      clearable
    />
    
    <!-- 使用Android優化的數據表格 -->
    <AndroidDataTable
      :headers="headers"
      :items="items"
      card-title-key="name"
      card-chip-key="status"
    />
  </AndroidContainer>
</template>

<script setup lang="ts">
import { 
  AndroidContainer, 
  AndroidButton, 
  AndroidTextField, 
  AndroidDataTable 
} from '@/components/AndroidLayout'

// 其他邏輯...
</script>
```

### 2. 使用Composable

```vue
<script setup lang="ts">
import { useAndroidLayout } from '@/components/AndroidLayout'

// 使用預設配置
const {
  isMobile,
  cardClass,
  containerClass,
  buttonConfig,
  getResponsiveCols
} = useAndroidLayout()

// 使用自定義配置
const {
  isMobile,
  formatDateTime,
  getStatusColor
} = useAndroidLayout({
  config: {
    features: {
      deviceInfo: true,
      fab: true
    }
  },
  showDeviceInfo: true
})
</script>
```

## ⚙️ 配置選項

### 1. 基本配置

```typescript
import { createAndroidLayoutConfig } from '@/components/AndroidLayout'

const customConfig = createAndroidLayoutConfig({
  enabled: true,
  device: {
    mobileBreakpoint: 600,
    touchMinSize: 44
  },
  features: {
    deviceInfo: true,
    fab: true,
    touchOptimization: true
  }
})
```

### 2. 快速配置預設

```typescript
import { quickConfigs } from '@/components/AndroidLayout'

// 僅移動端優化
const mobileOnlyConfig = quickConfigs.mobileOnly

// 完整功能
const fullConfig = quickConfigs.full

// 開發模式
const devConfig = quickConfigs.development

// 生產模式
const prodConfig = quickConfigs.production
```

### 3. 環境特定配置

```typescript
// 自動根據環境選擇配置
import { getCurrentConfig } from '@/components/AndroidLayout'

const config = getCurrentConfig()
// 開發環境: 顯示設備信息和調試功能
// 生產環境: 隱藏調試功能，優化性能
```

## 📋 組件詳細說明

### AndroidContainer
容器組件，提供統一的Android優化環境

**主要功能:**
- 自動設備檢測和響應式布局
- 可選的設備信息顯示面板
- 浮動操作按鈕(FAB)支援
- 安全區域適配

**使用方式:**
```vue
<AndroidContainer 
  :android-options="{ showDeviceInfo: true }"
  :show-fab="true"
  fab-color="primary"
>
  <!-- 內容 -->
</AndroidContainer>
```

### AndroidButton
Android優化的按鈕組件

**主要功能:**
- 自動響應式尺寸調整
- 觸摸目標最小尺寸保證
- 移動端自動block布局
- 觸摸反饋優化

**使用方式:**
```vue
<AndroidButton
  color="primary"
  prepend-icon="mdi-search"
  :loading="isLoading"
>
  搜尋
</AndroidButton>
```

### AndroidTextField
Android優化的文字輸入框組件

**主要功能:**
- 防止iOS自動縮放
- 觸摸友好的輸入區域
- 響應式密度調整
- 自動聚焦控制

**使用方式:**
```vue
<AndroidTextField
  v-model="value"
  label="輸入內容"
  :auto-focus="!isMobile"
  clearable
/>
```

### AndroidDataTable
Android優化的數據表格組件

**主要功能:**
- 移動端自動切換為卡片列表
- 響應式表格標題簡化
- 觸摸友好的操作按鈕
- 自定義卡片顯示邏輯

**使用方式:**
```vue
<AndroidDataTable
  :headers="headers"
  :items="items"
  card-title-key="name"
  card-chip-key="status"
  :show-actions="true"
  @edit="handleEdit"
  @delete="handleDelete"
/>
```

## 🎨 樣式自定義

### 1. CSS類別覆蓋

```typescript
const customConfig = createAndroidLayoutConfig({
  styles: {
    cssClasses: {
      mobileContainer: 'my-mobile-container',
      mobileButton: 'my-mobile-button'
    }
  }
})
```

### 2. 自定義CSS

```typescript
const customConfig = createAndroidLayoutConfig({
  styles: {
    customCSS: `
      .my-mobile-container {
        padding: 12px !important;
        background: #f5f5f5;
      }
      
      .my-mobile-button {
        border-radius: 12px !important;
        font-weight: 600 !important;
      }
    `
  }
})
```

## 🔄 遷移指南

### 從現有表單遷移

1. **替換容器**:
```vue
<!-- 原來 -->
<v-container fluid>
  <!-- 內容 -->
</v-container>

<!-- 改為 -->
<AndroidContainer>
  <!-- 內容 -->
</AndroidContainer>
```

2. **替換按鈕**:
```vue
<!-- 原來 -->
<v-btn 
  :block="isMobile"
  :size="isMobile ? 'large' : 'default'"
  color="primary"
>
  按鈕
</v-btn>

<!-- 改為 -->
<AndroidButton color="primary">
  按鈕
</AndroidButton>
```

3. **替換輸入框**:
```vue
<!-- 原來 -->
<v-text-field
  v-model="value"
  :density="isMobile ? 'comfortable' : 'default'"
  variant="outlined"
/>

<!-- 改為 -->
<AndroidTextField v-model="value" />
```

4. **替換數據表格**:
```vue
<!-- 原來 -->
<v-data-table
  :headers="isMobile ? mobileHeaders : allHeaders"
  :items="items"
/>

<!-- 改為 -->
<AndroidDataTable
  :headers="allHeaders"
  :items="items"
/>
```

## 📊 性能優化

### 1. 載入時間優化
```typescript
const config = createAndroidLayoutConfig({
  features: {
    loadingOptimization: true
  }
})

// 在組件中使用
const { getOptimizedLoadingTime } = useAndroidLayout({ config })
const loadingTime = getOptimizedLoadingTime() // 移動端1000ms，桌面端1500ms
```

### 2. 組件懶加載
```typescript
// 按需導入組件
import { AndroidButton } from '@/components/AndroidLayout'

// 或使用動態導入
const AndroidDataTable = defineAsyncComponent(
  () => import('@/components/AndroidLayout/AndroidDataTable.vue')
)
```

## 🧪 測試和調試

### 1. 開發模式
```typescript
const devConfig = quickConfigs.development
// 啟用設備信息顯示、FAB按鈕、調試功能
```

### 2. 設備模擬測試
- 使用Chrome DevTools的設備模擬功能
- 測試不同螢幕尺寸下的響應式行為
- 驗證觸摸操作的友好性

### 3. 配置驗證
```typescript
import { mergeConfig, defaultAndroidLayoutConfig } from '@/components/AndroidLayout'

// 驗證配置合併結果
const finalConfig = mergeConfig(defaultAndroidLayoutConfig, customConfig)
console.log('最終配置:', finalConfig)
```

## 📚 最佳實踐

1. **統一使用配置**: 所有新表單都應使用Android Layout組件
2. **環境特定配置**: 開發和生產環境使用不同的配置
3. **漸進式遷移**: 現有表單可以逐步遷移到新的組件系統
4. **性能監控**: 定期檢查移動端的性能表現
5. **用戶反饋**: 收集移動端用戶的使用反饋，持續優化配置

---

*最後更新: 2024年6月29日*
