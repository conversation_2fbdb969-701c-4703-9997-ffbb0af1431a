import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { getData, deleteData } from '@/utils/backend-api'

export interface ListOptions<T> {
  endpoint: string
  editRoute: string
  newRoute: string
  idField?: keyof T
  searchFields?: (keyof T)[]
  defaultSort?: {
    field: keyof T
    direction: 'asc' | 'desc'
  }
  pageSize?: number
  onLoadSuccess?: (data: T[]) => void
  onLoadError?: (error: any) => void
  onDeleteSuccess?: (id: any) => void
  onDeleteError?: (error: any) => void
}

export interface ListState<T> {
  items: T[]
  loading: boolean
  deleting: boolean
  selectedItems: T[]
  searchQuery: string
  currentPage: number
  itemsPerPage: number
  totalItems: number
  sortBy: string
  sortDirection: 'asc' | 'desc'
}

export function useGenericList<T extends Record<string, any>>(options: ListOptions<T>) {
  const router = useRouter()

  const {
    endpoint,
    editRoute,
    newRoute,
    idField = 'id' as keyof T,
    searchFields = [],
    defaultSort,
    pageSize = 10,
    onLoadSuccess,
    onLoadError,
    onDeleteSuccess,
    onDeleteError
  } = options

  // 狀態管理
  const state = ref<ListState<T>>({
    items: [],
    loading: false,
    deleting: false,
    selectedItems: [],
    searchQuery: '',
    currentPage: 1,
    itemsPerPage: pageSize,
    totalItems: 0,
    sortBy: defaultSort?.field as string || idField as string,
    sortDirection: defaultSort?.direction || 'asc'
  })

  // 對話框狀態
  const deleteDialog = ref(false)
  const itemToDelete = ref<T | null>(null)

  // 通知訊息
  const snackbar = ref({
    show: false,
    message: '',
    color: 'success'
  })

  // 計算屬性
  const filteredItems = computed(() => {
    if (!state.value.searchQuery) return state.value.items

    const query = state.value.searchQuery.toLowerCase()
    return state.value.items.filter(item => {
      if (searchFields.length === 0) {
        // 如果沒有指定搜索字段，搜索所有字符串字段
        return Object.values(item).some(value => 
          typeof value === 'string' && value.toLowerCase().includes(query)
        )
      } else {
        // 搜索指定字段
        return searchFields.some(field => {
          const value = item[field]
          return typeof value === 'string' && value.toLowerCase().includes(query)
        })
      }
    })
  })

  const sortedItems = computed(() => {
    const items = [...filteredItems.value]
    const field = state.value.sortBy as keyof T
    
    return items.sort((a, b) => {
      const aValue = a[field]
      const bValue = b[field]
      
      let comparison = 0
      if (aValue < bValue) comparison = -1
      if (aValue > bValue) comparison = 1
      
      return state.value.sortDirection === 'desc' ? -comparison : comparison
    })
  })

  const paginatedItems = computed(() => {
    const start = (state.value.currentPage - 1) * state.value.itemsPerPage
    const end = start + state.value.itemsPerPage
    return sortedItems.value.slice(start, end)
  })

  const totalPages = computed(() => 
    Math.ceil(filteredItems.value.length / state.value.itemsPerPage)
  )

  const hasSelection = computed(() => state.value.selectedItems.length > 0)

  // 通用方法
  const showMessage = (message: string, color: string = 'success') => {
    snackbar.value = {
      show: true,
      message,
      color
    }
  }

  const setLoading = (loading: boolean) => {
    state.value.loading = loading
  }

  const setDeleting = (deleting: boolean) => {
    state.value.deleting = deleting
  }

  // 數據操作
  const loadItems = async (params?: Record<string, any>) => {
    try {
      setLoading(true)
      const response = await getData(endpoint, params)
      
      if (response.data) {
        const items = Array.isArray(response.data) ? response.data : [response.data]
        state.value.items = items
        state.value.totalItems = items.length
        onLoadSuccess?.(items)
      }
    } catch (error) {
      console.error('載入數據失敗:', error)
      showMessage('載入資料失敗，請稍後再試', 'error')
      onLoadError?.(error)
    } finally {
      setLoading(false)
    }
  }

  const reloadItems = () => {
    loadItems()
  }

  // 導航操作
  const addItem = () => {
    router.push(newRoute)
  }

  const editItem = (item: T) => {
    const id = item[idField]
    router.push(`${editRoute}/${id}`)
  }

  const viewItem = (item: T) => {
    editItem(item) // 默認與編輯相同，可以根據需要覆蓋
  }

  // 刪除操作
  const confirmDelete = (item: T) => {
    itemToDelete.value = item
    deleteDialog.value = true
  }

  const cancelDelete = () => {
    itemToDelete.value = null
    deleteDialog.value = false
  }

  const executeDelete = async () => {
    if (!itemToDelete.value) return

    try {
      setDeleting(true)
      const id = itemToDelete.value[idField]
      await deleteData(`${endpoint}/${id}`)
      
      showMessage('刪除成功', 'success')
      onDeleteSuccess?.(id)
      
      // 重新載入數據
      await loadItems()
      
      cancelDelete()
    } catch (error) {
      console.error('刪除失敗:', error)
      showMessage('刪除失敗，請稍後再試', 'error')
      onDeleteError?.(error)
    } finally {
      setDeleting(false)
    }
  }

  // 批量刪除
  const deleteSelectedItems = async () => {
    if (state.value.selectedItems.length === 0) {
      showMessage('請選擇要刪除的項目', 'warning')
      return
    }

    if (!confirm(`確定要刪除選中的 ${state.value.selectedItems.length} 個項目嗎？`)) {
      return
    }

    try {
      setDeleting(true)
      
      for (const item of state.value.selectedItems) {
        const id = item[idField]
        await deleteData(`${endpoint}/${id}`)
      }
      
      showMessage(`成功刪除 ${state.value.selectedItems.length} 個項目`, 'success')
      state.value.selectedItems = []
      
      // 重新載入數據
      await loadItems()
    } catch (error) {
      console.error('批量刪除失敗:', error)
      showMessage('批量刪除失敗', 'error')
    } finally {
      setDeleting(false)
    }
  }

  // 搜索和排序
  const updateSearch = (query: string) => {
    state.value.searchQuery = query
    state.value.currentPage = 1 // 重置到第一頁
  }

  const updateSort = (field: keyof T, direction?: 'asc' | 'desc') => {
    if (state.value.sortBy === field && !direction) {
      // 切換排序方向
      state.value.sortDirection = state.value.sortDirection === 'asc' ? 'desc' : 'asc'
    } else {
      state.value.sortBy = field as string
      state.value.sortDirection = direction || 'asc'
    }
  }

  // 分頁操作
  const updatePage = (page: number) => {
    state.value.currentPage = page
  }

  const updateItemsPerPage = (itemsPerPage: number) => {
    state.value.itemsPerPage = itemsPerPage
    state.value.currentPage = 1 // 重置到第一頁
  }

  // 選擇操作
  const toggleSelection = (item: T) => {
    const index = state.value.selectedItems.findIndex(selected => 
      selected[idField] === item[idField]
    )
    
    if (index > -1) {
      state.value.selectedItems.splice(index, 1)
    } else {
      state.value.selectedItems.push(item)
    }
  }

  const selectAll = () => {
    state.value.selectedItems = [...paginatedItems.value]
  }

  const clearSelection = () => {
    state.value.selectedItems = []
  }

  const isSelected = (item: T) => {
    return state.value.selectedItems.some(selected => 
      selected[idField] === item[idField]
    )
  }

  return {
    // 狀態
    state: computed(() => state.value),
    deleteDialog,
    itemToDelete,
    snackbar,
    
    // 計算屬性
    filteredItems,
    sortedItems,
    paginatedItems,
    totalPages,
    hasSelection,
    
    // 方法
    showMessage,
    setLoading,
    setDeleting,
    loadItems,
    reloadItems,
    addItem,
    editItem,
    viewItem,
    confirmDelete,
    cancelDelete,
    executeDelete,
    deleteSelectedItems,
    updateSearch,
    updateSort,
    updatePage,
    updateItemsPerPage,
    toggleSelection,
    selectAll,
    clearSelection,
    isSelected
  }
}
