import { Entity, Pagination } from "@/types";
export const DEFAULT_ROW_PER_PAGE = 10;

export function getDefaultPagination(): Pagination {
  return {
    page: 1,
    totalItems: 0,
    rowsPerPage: DEFAULT_ROW_PER_PAGE,
    pages: 0,
    sortBy: [],
    descending: [],
    search: ""
  };
}

export function getPagination(items: Entity[]): Pagination {
  const totalItems = items ? items.length : 0;
  const pages = Math.ceil(totalItems / DEFAULT_ROW_PER_PAGE);
  return {
    totalItems,
    pages,
    page: 1,
    rowsPerPage: DEFAULT_ROW_PER_PAGE,
    sortBy: [],
    descending: [],
    search: ""
  };
}
