import { getData, putData, postData, deleteData } from "@/utils/backend-api";
import { Employee, Work, Entity, Track, Workline } from "@/types";
import { getDefaultPagination, getPagination } from "@/utils/store-util";
import { appModule } from "./app";
import { get } from "lodash";
import {
  VuexModule,
  Module,
  Mutation,
  Action,
  getModule
} from "vuex-module-decorators";
import store from "@/store";

export interface WorkState {
  items: Entity[];
  pagination: Pagination;
  loading: boolean;
  work: Work;
  customer: string;
  workline: Workline[];
  track: Track;
  employees: Employee[];
}

@Module({ store, dynamic: true, name: "works" })
class WorkModule extends VuexModule implements WorkState {
  public items: Entity[] = [];
  public pagination = getDefaultPagination();
  public loading = false;
  public work = {} as Work;
  public customer = "";
  public workline: Workline[] = [];
  public track = {} as Track;
  public employees: Employee[] = [];

  @Action
  getEmployees() {
    getData("employees/tw").then(res => {
      if (res.data) {
        const employees = res.data.map((c: Employee) => {
          c.employName = c.employNO + " " + c.userName;
          c.value = c.id;
          return c;
        });
        this.setEmployees(employees);
      }
    });
  }

  @Action
  getWorkById(id: string) {
    if (id) {
      getData("works/" + id).then(
        res => {
          const _work = res.data;
          const work = _work[0];
          work.worklines.filter((p: Work) => p !== null && p !== undefined);
          this.setWork(work);
        },
        (err: TODO) => {
          console.log(err);
        }
      );
    } else {
      const work = {} as Work;
      work.worklines = [];
      this.setWork(work);
      this.setLoading(false);
    }
  }

  @Action getTrackById(id: string) {
    this.setLoading(true);
    if (id) {
      getData("tracks/" + id).then(
        res => {
          const track = res.data;
          this.setTrack(track);
          this.setLoading(false);
        },
        (err: TODO) => {
          console.log(err);
          this.setLoading(false);
        }
      );
    } else {
      this.setTrack({} as Track);
      this.setLoading(false);
    }
  }

  @Action
  getAllWorks() {
    this.setLoading(true);
    getData("works").then(res => {
      const works = res.data;
      works.forEach((item: Work) => {
        item.quantity = item.worklines?.length;
      });
      this.setDataTable(works);
      this.setLoading(false);
    });
  }

  @Action
  searchWorks(searchQuery: string) {
    getData("works" + searchQuery).then(res => {
      const works = res.data;
      works.forEach((item: Work) => {
        item.amount = item.worklines.reduce(
          (p: TODO, c: TODO) => p + c.unitPrice,
          0
        );
        item.quantity = item.worklines?.length;
      });
      this.setDataTable(works);
      this.setLoading(false);
    });
  }

  @Action
  quickSearch(headers: TableHeader[], qsFilter: SeachQuery): void {
    getData("works").then(res => {
      const works = res.data.filter((r: TODO) =>
        headers.some((header: TableHeader) => {
          const val = get(r, [header.value]);
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter)) ||
            false
          );
        })
      );
      works.forEach((item: Work) => {
        item.quantity = item.worklines?.length;
      });
      this.setDataTable(works);
      this.setLoading(false);
    });
  }
  @Action
  deleteWork(id: number) {
    deleteData(`works/${id.toString()}`)
      .then(() => {
        appModule.sendSuccessNotice("Operation is done.");
        appModule.closeNoticeWithDelay(3000);
        this.getAllWorks();
      })
      .catch((err: TODO) => {
        console.log(err);
        appModule.sendErrorNotice("Operation failed! Please try again later.");
        appModule.closeNoticeWithDelay(3000);
      });
  }
  @Action
  addWorklineToWork(workline: Workline) {
    if (workline) {
      this.saveWorkline(workline);
      const workId = this.work.id;
      this.getWorkById(workId.toString());
      const newWork = this.work;
      this.setWork(newWork);
    }
  }
  @Action
  deleteWorkline(workline: Workline) {
    if (workline) {
      const workId = this.work.id;
      const worklineId = workline.id;
      deleteData(`worklines/${worklineId.toString()}`)
        .then(() => {
          this.getWorkById(workId.toString());
          const newWork = this.work;
          this.setWork(newWork);
          appModule.sendSuccessNotice("Operation is done.");
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice(
            "Operation failed! Please try again later."
          );
          appModule.closeNoticeWithDelay(3000);
        });
    }
  }
  @Action
  saveWork(work: Work) {
    if (!work.id) {
      postData("works/", work)
        .then(res => {
          const work = res.data;
          const WorkId = { id: work[0] };
          const addWork = { ...WorkId, ...this.work};
          this.setWork(addWork);
          appModule.sendSuccessNotice("New work has been added.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice(
            "Operation failed! Please try again later. "
          );
          appModule.closeNoticeWithDelay(3000);
        });
    } else {
      putData("works/" + work.id.toString(), work)
        .then(res => {
          const work = res.data;
          this.setWork(work);
          appModule.sendSuccessNotice("Work has been updated.");
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice(
            "Operation failed! Please try again later. "
          );
          appModule.closeNoticeWithDelay(3000);
        });
    }
  }

  @Action
  saveWorkline(workline: Workline) {
    if (!workline.id) {
      postData("worklines/", workline)
        .then(() => {
          appModule.sendSuccessNotice("New workline has been added.");
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(3000);
        });
    } else {
      putData("works/" + workline.id.toString(), workline)
        .then(res => {
          const work = res.data;
          this.setWork(work);
          appModule.sendSuccessNotice("Workline has been updated.");
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(3000);
        });
    }
  }

  @Action
  clearWorkline() {
    this.setLoading(true)
    const workline = [];
    this.setWorkline(workline);
    this.setLoading(false);
  }

  @Action
  setDataTable(items: Work[]) {
    const pagination = getPagination(items);
    this.setPagination(pagination);
    this.setItems(items);
  }

  @Mutation
  setEmployees(employees: Employee[]) {
    this.employees = employees;
  }

  @Mutation
  setWorkline(workline: Workline[]) {
    this.workline = workline;
  }
  @Mutation
  setItems(works: Work[]) {
    this.items = works;
  }
  @Mutation
  setPagination(pagination: Pagination) {
    this.pagination = pagination;
  }
  @Mutation
  setLoading(loading: boolean) {
    this.loading = loading;
  }
  @Mutation
  setWork(work: Work) {
    this.work = work;
  }

  @Mutation
  setTrack(track: Track) {
    this.track = track;
  }
}

export const workModule = getModule(WorkModule); // Works;
