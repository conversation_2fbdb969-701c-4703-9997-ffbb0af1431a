import moment from 'moment'

interface SearchFilter {
  [key: string]: any
  equal?: { [key: string]: any }
  greaterThan?: { [key: string]: any }
  lessThan?: { [key: string]: any }
  greaterThanOrEqual?: { [key: string]: any }
  lessThanOrEqual?: { [key: string]: any }
  contain?: { [key: string]: any }
  startsWith?: { [key: string]: any }
  endsWith?: { [key: string]: any }
  filters?: Array<{
    property: string
    op: string
    val: any
  }>
}

const SearchFilterOps = {
  equal: "=",
  greaterThan: ">",
  lessThan: "<",
  greaterThanOrEqual: ">=",
  lessThanOrEqual: "<=",
  contain: "like",
  startsWith: "startsWith",
  endsWith: "endsWith"
}

export const clearSearchFilters = (searchFilter: SearchFilter) => {
  Object.keys(searchFilter).forEach(filter => {
    if (searchFilter[filter]) {
      if (filter !== "filters") {
        Object.keys(searchFilter[filter]!).forEach(prop => {
          searchFilter[filter]![prop] = null
        })
      }
    }
  })
}

export const buildSearchFilters = (searchFilter: SearchFilter) => {
  searchFilter.filters = []
  Object.keys(searchFilter).forEach(filter => {
    if (filter !== "filters") {
      Object.keys(searchFilter[filter]!).forEach(propName => {
        if (propName && searchFilter[filter] && searchFilter[filter]![propName]) {
          searchFilter.filters!.push({
            property: propName,
            op: SearchFilterOps[filter as keyof typeof SearchFilterOps],
            val: searchFilter[filter]![propName]
          })
        }
      })
    }
  })
}

export const buildJsonServerQuery = (searchFilter: SearchFilter): string => {
  let query = ''
  if (searchFilter.filters && searchFilter.filters.length > 0) {
    searchFilter.filters.forEach(filter => {
      if (query) query += '&'
      query += `${filter.property}${filter.op}${filter.val}`
    })
  }
  return query
}

export const formatDate = (date?: string | Date) => {
  if (!date) return moment().format('YYYY-MM-DD')
  return moment(date).format('YYYY-MM-DD')
}

export const formatDateTime = (date?: string | Date) => {
  if (!date) return moment().format('YYYY-MM-DD HH:mm:ss')
  return moment(date).format('YYYY-MM-DD HH:mm:ss')
}

export const getISOClassDate = (date?: string | Date) => {
  if (!date) return moment().toISOString()
  return moment(date).toISOString()
}

// Export SearchFilter type for use in other files
export type { SearchFilter }
