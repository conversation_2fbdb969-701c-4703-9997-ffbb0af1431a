# 🚀 AITS 開發規範指南

## 📋 概述

本文檔定義了 AITS 專案的開發規範，確保代碼品質和團隊協作效率。

## 🎯 Vue 3 組件開發規範

### 1. 組件結構標準

```vue
<template>
  <!-- 使用語義化的 HTML 結構 -->
  <v-container>
    <v-row>
      <v-col>
        <!-- 組件內容 -->
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
// 1. 導入 Vue 核心功能
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 2. 導入組合式函數
import { useMasterDetail } from '@/composables/useMasterDetail'
import { useFormValidation } from '@/composables/useFormValidation'

// 3. 導入類型定義
import type { YourDataType } from '@/types'

// 4. 定義 Props 和 Emits
interface Props {
  id?: string
  mode?: 'create' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'create'
})

const emit = defineEmits<{
  save: [data: YourDataType]
  cancel: []
}>()

// 5. 響應式數據
const formData = ref<YourDataType>({})
const loading = ref(false)

// 6. 計算屬性
const isEditMode = computed(() => props.mode === 'edit')

// 7. 組合式函數
const { validate, formRef } = useFormValidation(formData, {
  // 驗證規則
})

// 8. 方法定義
const save = async () => {
  if (validate()) {
    // 保存邏輯
  }
}

// 9. 生命週期
onMounted(() => {
  // 初始化邏輯
})
</script>

<style scoped>
/* 組件特定樣式 */
</style>
```

### 2. 命名規範

#### 文件命名
- **組件文件**: PascalCase (例: `InspectForm.vue`)
- **組合式函數**: camelCase with use prefix (例: `useFormValidation.ts`)
- **類型文件**: camelCase (例: `inspectTypes.ts`)

#### 變數命名
```typescript
// ✅ 正確
const formData = ref({})
const isLoading = ref(false)
const userList = ref([])

// ❌ 錯誤
const form_data = ref({})
const IsLoading = ref(false)
const user_list = ref([])
```

### 3. 組合式函數規範

```typescript
// composables/useYourFeature.ts
import { ref, computed } from 'vue'

export interface UseYourFeatureOptions {
  // 選項類型定義
}

export function useYourFeature(options: UseYourFeatureOptions = {}) {
  // 響應式狀態
  const state = ref({})
  
  // 計算屬性
  const computedValue = computed(() => {
    // 計算邏輯
  })
  
  // 方法
  const method = () => {
    // 方法邏輯
  }
  
  // 返回公開的 API
  return {
    // 狀態
    state,
    
    // 計算屬性
    computedValue,
    
    // 方法
    method
  }
}
```

## 🔧 代碼品質規範

### 1. TypeScript 使用規範

```typescript
// ✅ 正確：明確的類型定義
interface User {
  id: number
  name: string
  email: string
}

const user = ref<User>({
  id: 1,
  name: 'John',
  email: '<EMAIL>'
})

// ❌ 錯誤：使用 any
const user = ref<any>({})
```

### 2. 錯誤處理規範

```typescript
// ✅ 正確：統一的錯誤處理
const save = async () => {
  try {
    loading.value = true
    await api.save(formData.value)
    // 成功處理
  } catch (error) {
    console.error('保存失敗:', error)
    // 錯誤處理
  } finally {
    loading.value = false
  }
}
```

### 3. 註釋規範

```typescript
/**
 * 保存表單數據
 * @param data 表單數據
 * @returns Promise<boolean> 保存是否成功
 */
const save = async (data: FormData): Promise<boolean> => {
  // 實現邏輯
}
```

## 🧪 測試規範

### 1. 單元測試

```typescript
// tests/composables/useFormValidation.test.ts
import { describe, it, expect } from 'vitest'
import { useFormValidation } from '@/composables/useFormValidation'

describe('useFormValidation', () => {
  it('should validate required fields', () => {
    // 測試邏輯
  })
})
```

### 2. 組件測試

```typescript
// tests/components/InspectForm.test.ts
import { mount } from '@vue/test-utils'
import InspectForm from '@/pages/InspectForm.vue'

describe('InspectForm', () => {
  it('should render correctly', () => {
    const wrapper = mount(InspectForm)
    expect(wrapper.exists()).toBe(true)
  })
})
```

## 📝 提交規範

### Git Commit 格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

#### Type 類型
- `feat`: 新功能
- `fix`: 修復 bug
- `docs`: 文檔更新
- `style`: 代碼格式調整
- `refactor`: 重構
- `test`: 測試相關
- `chore`: 構建過程或輔助工具的變動

#### 範例
```
feat(inspect): 添加品檢表單驗證功能

- 實現即時驗證
- 添加錯誤訊息顯示
- 優化用戶體驗

Closes #123
```

## 🔍 代碼審查清單

### 提交前檢查
- [ ] TypeScript 編譯無錯誤
- [ ] ESLint 檢查通過
- [ ] 單元測試通過
- [ ] 功能測試完成
- [ ] 代碼註釋完整
- [ ] 符合命名規範

### 審查要點
- [ ] 代碼邏輯正確
- [ ] 性能考量合理
- [ ] 錯誤處理完善
- [ ] 可讀性良好
- [ ] 可維護性高

## 📚 參考資源

- [Vue 3 官方文檔](https://vuejs.org/)
- [TypeScript 官方文檔](https://www.typescriptlang.org/)
- [Vuetify 3 文檔](https://vuetifyjs.com/)
- [Vitest 測試框架](https://vitest.dev/)

---

**更新日期**: 2025-06-29  
**版本**: 1.0  
**維護者**: AITS 開發團隊
