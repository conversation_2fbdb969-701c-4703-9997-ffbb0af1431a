import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useInspectsStore } from '@/stores/inspects'
import { useProductsStore } from '@/stores/products'
import { useAppStore } from '@/stores/app'
import type { Inspect } from '@/types'

export interface InspectFormOptions {
  onSaveSuccess?: (data: Inspect) => void
  onSaveError?: (error: any) => void
  onLoadSuccess?: (data: Inspect) => void
  onLoadError?: (error: any) => void
}

export function useInspectForm(options: InspectFormOptions = {}) {
  const route = useRoute()
  const router = useRouter()
  
  // Store 實例
  const inspectsStore = useInspectsStore()
  const productsStore = useProductsStore()
  const appStore = useAppStore()

  // 響應式數據
  const form = ref()
  const formValid = ref(false)
  const loading = ref(false)

  // 表單數據
  const formData = ref<Partial<Inspect>>({
    id: null,
    classDate: new Date().toISOString().slice(0, 10),
    shiftName: '',
    employId: 0,
    groupName: '',
    typeName: 'YARN',
    quantity: 0
  })

  // 通知訊息
  const snackbar = ref({
    show: false,
    message: '',
    color: 'success'
  })

  // 選項數據
  const typeOptions = ['YARN', 'CAKE', 'PACK']
  const employeeOptions = ref([])

  // 驗證規則
  const validationRules = {
    required: [(value: any) => !!value || '此欄位為必填'],
    number: [(value: any) => !isNaN(Number(value)) || '請輸入有效數字']
  }

  // 計算屬性
  const isEditMode = computed(() => !!route.params.id)
  const formTitle = computed(() => isEditMode.value ? '編輯' : '新增')

  // 從 store 獲取數據
  const inspect = computed(() => inspectsStore.currentInspect)
  const employees = computed(() => inspectsStore.employees)

  // 方法
  const showMessage = (message: string, color: string = 'success') => {
    snackbar.value = {
      show: true,
      message,
      color
    }
  }

  const saveForm = async () => {
    try {
      loading.value = true
      
      if (isEditMode.value) {
        await inspectsStore.updateInspect(formData.value as Inspect)
        showMessage('更新成功！', 'success')
        options.onSaveSuccess?.(formData.value as Inspect)
      } else {
        await inspectsStore.saveInspect(formData.value as Inspect)
        const newId = inspectsStore.inspectId
        if (newId) {
          router.push(`/inspect/${newId}`)
        }
        showMessage('儲存成功！', 'success')
        options.onSaveSuccess?.(formData.value as Inspect)
      }
    } catch (error) {
      console.error('保存失敗:', error)
      showMessage('操作失敗，請重試', 'error')
      options.onSaveError?.(error)
    } finally {
      loading.value = false
    }
  }

  const resetForm = () => {
    formData.value = {
      id: null,
      classDate: new Date().toISOString().slice(0, 10),
      shiftName: '',
      employId: 0,
      groupName: '',
      typeName: 'YARN',
      quantity: 0
    }
    form.value?.resetValidation()
    showMessage('表單已重設', 'info')
  }

  const goBack = () => {
    router.push('/inspects')
  }

  // 載入數據
  const loadInspectData = async () => {
    if (route.params.id) {
      try {
        loading.value = true
        await inspectsStore.getInspectById(route.params.id as string)

        const inspectData = inspectsStore.currentInspect
        if (inspectData) {
          const processedData = { ...inspectData }
          processedData.classDate = formatDateForInput(inspectData.classDate || inspectData.date)
          formData.value = processedData
          options.onLoadSuccess?.(inspectData)
        }
        
        showMessage('數據載入成功', 'success')
      } catch (error) {
        console.error('載入數據錯誤:', error)
        showMessage('載入數據失敗', 'error')
        options.onLoadError?.(error)
      } finally {
        loading.value = false
      }
    }
  }

  const loadEmployees = async () => {
    try {
      await inspectsStore.getEmployees()
      employeeOptions.value = inspectsStore.employees
    } catch (error) {
      console.error('載入員工數據失敗:', error)
    }
  }

  // 日期格式化函數
  const formatDateForInput = (dateValue: any): string => {
    if (!dateValue) return new Date().toISOString().slice(0, 10)
    
    const date = new Date(dateValue)
    if (isNaN(date.getTime())) {
      console.warn('無效的日期值:', dateValue)
      return new Date().toISOString().slice(0, 10)
    }
    
    return date.toISOString().slice(0, 10)
  }

  // 初始化
  const initialize = async () => {
    await loadEmployees()
    await productsStore.getCategories()
    await productsStore.getRemarks()

    if (isEditMode.value) {
      await loadInspectData()
    } else {
      const newInspect = {
        typeName: 'YARN',
        classDate: new Date().toISOString().slice(0, 10)
      }
      inspectsStore.setInspect(newInspect as Inspect)
    }
  }

  return {
    // 響應式數據
    form,
    formValid,
    loading,
    formData,
    snackbar,
    employeeOptions,
    
    // 選項
    typeOptions,
    validationRules,
    
    // 計算屬性
    isEditMode,
    formTitle,
    inspect,
    employees,
    
    // 方法
    showMessage,
    saveForm,
    resetForm,
    goBack,
    loadInspectData,
    loadEmployees,
    initialize
  }
}
