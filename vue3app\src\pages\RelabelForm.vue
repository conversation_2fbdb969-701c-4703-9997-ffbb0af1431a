<template>
  <v-container fluid>
    <v-card class="grey lighten-4 elevation-0">
      <v-form ref="validForm" v-model="formValid" lazy-validation>
        <v-card-title class="title">
          {{ title }}
          <v-spacer></v-spacer>
          <v-btn
            :elevation="4"
            icon
            size="small"
            color="grey"
            class="mr-2"
            @click="cancel()"
          >
            <v-icon>mdi-close-circle-outline</v-icon>
          </v-btn>
          <v-btn
            :elevation="4"
            icon
            size="small"
            color="purple"
            class="mr-2"
            @click="save()"
            :disabled="isSaving || !formValid"
            :loading="isSaving"
          >
            <v-icon>mdi-content-save-all</v-icon>
          </v-btn>
          <v-btn
            :elevation="4"
            icon
            size="small"
            color="blue"
            @click="addProduct()"
          >
            <v-icon>mdi-plus</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-container fluid grid-list-md>
            <v-row>
              <v-col md="4" cols="12">
                <v-text-field
                  name="id"
                  label="單號"
                  type="number"
                  hint="RelabelID is required"
                  v-model="relabel.id"
                  class="input-group--focused"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col md="4" cols="12">
                <v-menu
                  :close-on-content-click="false"
                  v-model="classDateMenu"
                  transition="v-scale-transition"
                  offset-y
                  :nudge-left="40"
                  max-width="290px"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      v-bind="props"
                      label="日期"
                      v-model="relabel.classDate"
                      prepend-icon="mdi-calendar"
                      readonly
                    ></v-text-field>
                  </template>
                  <v-date-picker
                    v-model="relabel.classDate"
                    no-title
                    scrollable
                  >
                  </v-date-picker>
                </v-menu>
              </v-col>
              <v-col md="4" cols="12">
                <v-radio-group
                  name="shiftName"
                  label="勤別"
                  v-model="relabel.shiftName"
                  :rules="[value => !!value || '必要!!請選擇']"
                  required
                  row
                >
                  <v-radio label="I" value="1"></v-radio>
                  <v-radio label="II" value="2"></v-radio>
                  <v-radio label="III" value="3"></v-radio>
                </v-radio-group>
              </v-col>

              <v-col md="6" cols="12">
                <v-autocomplete
                  :items="employees"
                  label="人員"
                  item-title="employName"
                  item-value="employId"
                  v-model="relabel.employId"
                  :rules="[value => !!value || '必要!!請選擇']"
                  required
                ></v-autocomplete>
              </v-col>
              <v-col md="4" cols="12">
                <v-radio-group
                  name="groupName"
                  label="組別"
                  v-model="relabel.groupName"
                  :rules="[value => !!value || '必要!!請選擇']"
                  required
                  row
                >
                  <v-radio label="A" value="A"></v-radio>
                  <v-radio label="B" value="B"></v-radio>
                  <v-radio label="C" value="C"></v-radio>
                  <v-radio label="D" value="D"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col md="4" cols="12">
                <v-text-field
                  name="quantity"
                  label="小計"
                  type="number"
                  v-model="relabel.quantity"
                  class="input-group--focused"
                  readonly
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-card>
                  <v-data-table
                    v-if="loading === false"
                    :headers="headers"
                    :items="relabellinesWithIndex"
                    :items-per-page="10"
                    class="elevation-1 colored-pagination"
                  >
                    <template v-slot:item.countdown="{ index }">
                      {{ index + 1 }}
                    </template>
                    <template v-slot:item.actions="{ item }">
                      <v-btn
                        :elevation="4"
                        icon
                        size="x-small"
                        color="red"
                        @click="remove(item)"
                      >
                        <v-icon>mdi-delete</v-icon>
                      </v-btn>
                    </template>
                  </v-data-table>
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
      </v-form>
    </v-card>

    <!-- 明細編輯對話框 -->
    <v-dialog v-model="addProductModal" width="700" persistent>
      <v-form ref="validDetail" v-model="detailValid" lazy-validation>
        <v-card>
          <v-card-title>
            {{ title }}
            <v-spacer></v-spacer>
            <v-card-actions>
              <v-btn
                class="green lighten-1"
                text
                :disabled="!detailValid"
                @click="saveRelabelline"
              >
                Confirm
              </v-btn>
              <v-btn
                class="orange lighten-1"
                text
                @click="cancelAddProduct"
              >
                Cancel
              </v-btn>
            </v-card-actions>
          </v-card-title>
          <v-card-text>
            1.請掃Cap QRCode
            <v-text-field
              ref="qrCodeInput"
              v-model="searchFilter.contain.codeName"
              append-icon="mdi-magnify"
              label="Cap QRCode"
              @keydown.enter.prevent="getProduct"
              :counter="14"
              :rules="[value => !!value || '必要!!請選擇']"
              required
            ></v-text-field>
            <v-container fluid grid-list-md>
              <v-row>
                <v-col
                  md="6"
                  cols="12"
                  v-for="(item, index) in product"
                  :key="index"
                >
                  <v-text-field
                    v-model="item.productName"
                    label="品種"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.gradeName"
                    label="定長別"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.workDate"
                    label="開機日期"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.twisterNO"
                    label="Twister NO"
                    readonly
                  ></v-text-field>
                  <v-text-field
                    v-model="item.spindleNO"
                    label="Spindle NO"
                    readonly
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-container>
          </v-card-text>
        </v-card>
      </v-form>
    </v-dialog>

    <!-- 確認對話框 -->
    <v-dialog v-model="dialog" max-width="400">
      <v-card>
        <v-card-title>{{ dialogTitle }}</v-card-title>
        <v-card-text>{{ dialogText }}</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey" variant="text" @click="onCancel">取消</v-btn>
          <v-btn color="error" variant="text" @click="onConfirm">確認</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 訊息提示 -->
    <v-snackbar
      v-if="loading === false"
      v-model="snackbar"
      :color="mode"
      timeout="5000"
      location="top end"
    >
      <div class="text-center">
        {{ notice }}
        <v-btn dark text @click="closeSnackbar">Close</v-btn>
      </div>
    </v-snackbar>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useRelabelsStore } from '@/stores/relabels'
import { useProductsStore } from '@/stores/products'
import { useAppStore } from '@/stores/app'

const route = useRoute()
const router = useRouter()

// Store 實例
const relabelsStore = useRelabelsStore()
const productsStore = useProductsStore()
const appStore = useAppStore()

// 響應式數據
const validForm = ref()
const validDetail = ref()
const qrCodeInput = ref()
const formValid = ref(false)
const detailValid = ref(false)

// Vue2 風格的數據結構
const modalTitle = ref("新增Relabel NO MFD(明細)")
const modalText = ref("請掃Cap QRCode")
const addProductModal = ref(false)
const dialog = ref(false)
const dialogTitle = ref("Relabel NO MFD(明細)刪除確認")
const dialogText = ref("刪除該筆記錄?")
const classDateMenu = ref(false)
const errors = ref([])
const title = ref("")
const type = ref("NOMFD")
const relabelId = ref(0)
const relabellineId = ref<number | undefined>(undefined)
const categoryId = ref("100660")
const remarkId = ref<number | undefined>(undefined)
const isMFD = ref("N")
const color = ref("")
const selectedRelabelline = ref<any>(undefined)
const isYarnQRcode = ref<boolean | undefined>(undefined)
const query = ref("")
const isSaving = ref(false)  // 防止重複儲存
const lastSaveTime = ref(0)  // 記錄上次儲存時間，防止短時間內重複儲存

// 明細分頁狀態
const detailPagination = ref({
  page: 1,
  limit: 10,
  totalItems: 0,
  totalPages: 0,
  hasNextPage: false,
  hasPrevPage: false
})

// 表格標題
const headers = ref([
  { title: "序號", key: "countdown", align: "start" as const },
  { title: "品種", key: "productName", align: "start" as const },
  { title: "定長別", key: "gradeName" },
  { title: "開機日期", key: "workDate", align: "start" as const },
  { title: "Twister NO", key: "twisterNO" },
  { title: "Spindle NO", key: "spindleNO" },
  { title: "Is MFD", key: "isMFD" },
  { title: "", key: "actions", sortable: false }
])

const searchFilter = ref({ contain: { codeName: "" } })

// 計算屬性 - 從 store 獲取數據
const employees = computed(() => relabelsStore.employees)
const relabel = computed(() => relabelsStore.relabel)
const categories = computed(() => relabelsStore.categories)
const remarks = computed(() => relabelsStore.remarks)
const relabelline = computed(() => relabelsStore.relabellines)
const relabellinesWithIndex = computed(() => {
  if (relabel.value && relabel.value.relabellines) {
    return relabel.value.relabellines.map((item, index) => ({
      ...item,
      countdown: index + 1
    }))
  }
  return []
})
const product = computed(() => productsStore.products)
const loading = computed(() => appStore.loading)
const mode = computed(() => appStore.mode)
const snackbar = computed(() => appStore.snackbar)
const notice = computed(() => appStore.notice)

// 方法
const save = async () => {
  if (!relabel.value.id) {
    try {
      await relabelsStore.saveRelabel(relabel.value)
      saveRoute()
    } catch (error: any) {
      console.error("Error:", error.message)
    }
  } else {
    try {
      await relabelsStore.saveRelabel(relabel.value)
    } catch (error: any) {
      console.error("Error:", error.message)
    }
  }
}

const saveRoute = () => {
  relabelId.value = relabelsStore.relabelId
  router.push(`/relabel/${relabelId.value}`)
}

const getRelabelById = () => {
  relabelsStore.getRelabelById(route.params.id as string)
}

const getProduct = async () => {
  const codeName = searchFilter.value.contain.codeName
  const trimmedCodeName = codeName.trim()

  if (!trimmedCodeName) {
    appStore.sendErrorNotice("請輸入Cap QRcode!")
    appStore.closeNoticeWithDelay(3000)
    return ""
  }

  isYarnQRcode.value = /^\d{14}[a-zA-Z0-9\s.]*$/.test(trimmedCodeName)

  if (!isYarnQRcode.value) {
    appStore.sendErrorNotice("無效 Cap QRcode!")
    appStore.closeNoticeWithDelay(3000)
    return ""
  }

  if (isYarnQRcode.value) {
    query.value = trimmedCodeName.slice(0, 14)

    const isDuplicate = await relabelsStore.getDuplicateRelabellineByCode(query.value)
    if (isDuplicate) {
      appStore.sendErrorNotice("重複 Cap QRcode!")
      appStore.closeNoticeWithDelay(5000)
      return ""
    }

    try {
      await productsStore.getProductByQRCode(query.value)
      if (product.value && product.value.length > 0) {
        return product.value
      } else {
        appStore.sendErrorNotice("查無資料!")
        appStore.closeNoticeWithDelay(5000)
        return ""
      }
    } catch (error) {
      appStore.sendErrorNotice("查無資料!")
      appStore.closeNoticeWithDelay(5000)
      return ""
    }
  }
}

const cancel = () => {
  router.push({ name: "relabels" })
}

const remove = (item: any) => {
  selectedRelabelline.value = item
  dialog.value = true
}

const onConfirm = () => {
  relabelsStore.deleteRelabelline(selectedRelabelline.value)
  selectedRelabelline.value = null
  getRelabelById()
  dialog.value = false
}

const onCancel = () => {
  selectedRelabelline.value = null
  dialog.value = false
}

const addProduct = async () => {
  // 如果是新增模式且還沒有 ID，先保存主檔
  if (!relabel.value.id || relabel.value.id <= 0) {
    try {
      const relabelData = {
        ...relabel.value,
        shiftName: relabel.value.shiftName || "1",
        employId: relabel.value.employId,
        groupName: relabel.value.groupName || "A",
        typeName: relabel.value.typeName || "NOMFD",
        classDate: relabel.value.classDate || new Date().toISOString().slice(0, 10)
      }

      await relabelsStore.saveRelabel(relabelData)
      // 更新路由到編輯模式
      const newId = relabelsStore.relabelId
      router.replace(`/relabel/${newId}`)
    } catch (error) {
      console.error("保存主檔失敗:", error)
      appStore.sendErrorNotice("請先完成主檔資料並保存")
      return
    }
  }

  addProductModal.value = true
  query.value = ""
  searchFilter.value.contain.codeName = ""
  relabelId.value = relabel.value.id
  // 清空產品數據
  productsStore.resetState()

  nextTick(() => {
    if (validDetail.value) {
      validDetail.value.validate()
    }
    nextTick(() => {
      if (qrCodeInput.value) {
        qrCodeInput.value.focus()
      }
    })
  })
}

const saveRelabelline = async () => {
  const RelabelId = { relabelId: relabel.value.id }
  const CategoryId = { categoryId: categoryId.value }
  const RemarkId = { remarkId: remarkId.value }
  const IsMFD = { isMFD: isMFD.value }
  const addRelabelline = relabelline.value
  const addProduct = product.value[0]

  const newRelabelline = {
    ...RelabelId,
    ...CategoryId,
    ...RemarkId,
    ...addRelabelline,
    ...addProduct,
    ...IsMFD
  }

  try {
    await relabelsStore.addRelabellineToRelabel(newRelabelline)
    relabellineId.value = undefined
    // addRelabellineToRelabel 已經會重新載入數據，不需要再次調用
    addProductModal.value = false
    resetForm()
  } catch (error) {
    console.error('保存明細失敗:', error)
  }
}

const resetForm = () => {
  searchFilter.value.contain.codeName = ""
  productsStore.resetState()
  nextTick(() => {
    if (qrCodeInput.value) {
      qrCodeInput.value.focus()
    }
  })
}

const cancelAddProduct = () => {
  addProductModal.value = false
  query.value = ""
  searchFilter.value.contain.codeName = ""
  relabelsStore.clearRelabelline()
  productsStore.resetState()
  getRelabelById()
}

const closeSnackbar = () => {
  appStore.closeNotice()
}

// 處理明細分頁變更
const updateDetailOptions = (options: any) => {
  console.log('明細分頁選項變更:', options)

  // 更新分頁狀態
  if (detailPagination.value.page !== options.page || detailPagination.value.limit !== options.itemsPerPage) {
    detailPagination.value.page = options.page
    detailPagination.value.limit = options.itemsPerPage
    // 由於明細資料是本地的，不需要重新載入
  }
}

// 生命週期
onMounted(async () => {
  // 先載入基礎數據
  await relabelsStore.getEmployees()
  await relabelsStore.getCategories()
  await relabelsStore.getRemarks()

  window.scrollTo(0, 0)

  if (route.params.id) {
    // 編輯模式
    title.value = "Relabel NO MFD(明細)"
    relabelsStore.resetState()
    await relabelsStore.getRelabelById(route.params.id as string)
  } else {
    // 新增模式
    title.value = "Relabel NO MFD(新增)"
    relabelsStore.resetState()

    // 設置新增模式的默認值，參考 InspectForm 的處理方式
    const newRelabel = {
      id: undefined,
      typeName: type.value,
      classDate: new Date().toISOString().slice(0, 10),
      // 勤別和組別應為空值，由人員設定
      shiftName: undefined,
      employId: undefined,
      groupName: undefined,
      quantity: 0,
      relabellines: []
    }
    relabelsStore.setRelabel(newRelabel)

    nextTick(() => {
      if (validForm.value) {
        validForm.value.validate()
      }
    })
  }
})
</script>

<style scoped>
/* 自定義分頁按鈕顏色 */
:deep(.colored-pagination .v-data-table-footer .v-btn) {
  color: rgb(33, 150, 243) !important; /* 藍色 */
}

:deep(.colored-pagination .v-data-table-footer .v-btn:hover) {
  background-color: rgba(33, 150, 243, 0.1) !important;
}

:deep(.colored-pagination .v-data-table-footer .v-btn.v-btn--active) {
  background-color: rgb(33, 150, 243) !important;
  color: white !important;
}

:deep(.colored-pagination .v-data-table-footer .v-btn.v-btn--disabled) {
  color: rgba(0, 0, 0, 0.26) !important;
}

/* 分頁選擇器樣式 */
:deep(.colored-pagination .v-data-table-footer .v-select) {
  color: rgb(33, 150, 243) !important;
}

:deep(.colored-pagination .v-data-table-footer .v-select .v-field__input) {
  color: rgb(33, 150, 243) !important;
}
</style>
