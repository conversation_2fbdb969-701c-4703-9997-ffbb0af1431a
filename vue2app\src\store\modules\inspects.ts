import { getData, putData, postData, deleteData } from "@/utils/backend-api";
import {
  Employee,
  Inspect,
  Entity,
  Product,
  Category,
  Remark,
  Inspectline
} from "@/types";
import { getDefaultPagination, getPagination } from "@/utils/store-util";
import { appModule } from "./app";
import { get } from "lodash";
import {
  VuexModule,
  Module,
  Mutation,
  Action,
  getModule
} from "vuex-module-decorators";
import store from "@/store";

export interface InspectState {
  items: Entity[];
  pagination: Pagination;
  loading: boolean;
  employee: string;
  inspectId: number;
  inspect: Inspect;
  inspectline: Inspectline[];
  product: Product;
  employees: Employee[];
  categories: Category[];
  remarks: Remark[];
}

@Module({ store, dynamic: true, name: "inspects" })
class InspectModule extends VuexModule implements InspectState {
  public items: Entity[] = [];
  public pagination = getDefaultPagination();
  public loading = false;
  public employee = "";
  public inspectId = null;
  public inspect = {} as Inspect;
  public inspectline: Inspectline[] = [];
  public product = {} as Product;
  public employees: Employee[] = [];
  public categories: Category[] = [];
  public remarks: Remark[] = [];

  @Action
  getEmployees() {
    getData("employees/qi").then(res => {
      if (res.data) {
        const employees = res.data.map((c: Employee) => {
          c.employName = c.employNO + " " + c.userName;
          c.value = c.id;
          return c;
        });
        this.setEmployees(employees);
      }
    });
  }

  @Action
  async getAllInspectsByType(type: string) {
    this.setLoading(true);
    try {
      const res = await getData(`inspects/${type}`);
      const inspects = res.data;
      this.setInspect(inspects);
      this.setDataTable(inspects);
    } catch (error) {
      console.error(error);
    } finally {
      this.setLoading(false);
    }
  }

  @Action
  getInspectById(id: string) {
    if (id) {
      getData("inspects/" + id).then(
        res => {
          const _inspect = res.data;
          const inspect = _inspect[0];
          inspect.inspectlines.filter(
            (p: Inspect) => p !== null && p !== undefined
          );
          inspect.quantity = inspect.inspectlines?.length;
          this.setInspect(inspect);
          this.setDataTable(inspect.inspectlines);
        },
        (err: TODO) => {
          console.log(err);
        }
      );
    } else {
      const inspect = {} as Inspect;
      inspect.inspectlines = [];
      this.setInspect(inspect);
      this.setLoading(false);
    }
  }

  @Action
  async getProductById(id: string) {
    try {
      this.setLoading(true);
      if (id) {
        const res = await getData("products/" + id);
        const product = res.data;
        this.setProduct(product);
      } else {
        this.setProduct({} as Product);
      }
    } catch (err) {
        console.log(err);
    } finally {
      this.setLoading(false);
    }
  }

  @Action async getDuplicateInspectlineByCode(code: string): Promise<boolean> {
    try {
      this.setLoading(true);
      if (code) {
        const res = await getData("inspectlines/duplicate/" + code);
        const data = res.data;
        if (data !== undefined && data !== null) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    } catch (err) {
      console.log(err);
      return false;
    } finally {
      this.setLoading(false);
    }
  }

  @Action
  searchInspects(searchQuery: string) {
    getData("inspects" + searchQuery).then(res => {
      const inspects = res.data;
      inspects.forEach((item: Inspect) => {
        item.quantity = item.inspectlines?.length;
      });
      this.setDataTable(inspects);
      this.setLoading(false);
    });
  }

  @Action
  quickSearch(headers: TableHeader[], qsFilter: SeachQuery): void {
    getData("inspects").then(res => {
      const inspects = res.data.filter((r: TODO) =>
        headers.some((header: TableHeader) => {
          const val = get(r, [header.value]);
          return (
            (val &&
              val
                .toString()
                .toLowerCase()
                .includes(qsFilter)) ||
            false
          );
        })
      );
      inspects.forEach((item: Inspect) => {
        item.quantity = item.inspectlines?.length;
      });
      this.setDataTable(inspects);
      this.setLoading(false);
    });
  }

  @Action
  saveInspect(inspect: Inspect):Promise<void> {
    return new Promise((resolve, reject) => {
      if (!inspect.id) {
        postData("inspects/", inspect)
          .then(res => {
            const inspect = res.data;
            const InspectId = { id: inspect[0].id };
            const addInspect = { ...InspectId, ...this.inspect};
            this.setInspect(addInspect);
            this.setInspectId(addInspect.id);
            appModule.sendSuccessNotice("New record has been added.");
            appModule.closeNoticeWithDelay(3000);
            resolve();
          })
          .catch((err: TODO) => {
            console.log(err);
            appModule.sendErrorNotice("Operation failed! Please try again later.");
            appModule.closeNoticeWithDelay(5000);
            reject(err);
          });
      } else {
        putData("inspects/" + inspect.id.toString(), inspect)
          .then(() => {
            appModule.sendSuccessNotice("The record has been updated.");
            appModule.closeNoticeWithDelay(3000);
            resolve();
          })
          .catch((err: TODO) => {
            console.log(err);
            appModule.sendErrorNotice("Operation failed! Please try again later.");
            appModule.closeNoticeWithDelay(5000);
            reject(err);
          });
      }
    });
  }

  @Action
  addInspectlineToInspect(inspectline: Inspectline) {
    if (inspectline) {
      this.saveInspectline(inspectline);
      const inspectId = this.inspect.id;
      this.getInspectById(inspectId.toString());
      const newInspect = this.inspect;
      this.setInspect(newInspect);
    }
  }

  @Action
  saveInspectline(inspectline: Inspectline) {
    if (!inspectline.id) {
      postData("inspectlines/", inspectline)
        .then(res => {
          const inspectline = res.data;
          this.setInspectline(inspectline);
          appModule.sendSuccessNotice("New record has been added.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    } else {
      putData("inspects/" + inspectline.id.toString(), inspectline)
        .then(res => {
          const inspect = res.data;
          this.setInspect(inspect);
          appModule.sendSuccessNotice("The record has been updated.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    }
  }

  @Action
  async deleteInspect(id: number) {
    try {
      await deleteData(`inspects/${id.toString()}`);
      appModule.sendSuccessNotice("Operation is done.");
      appModule.closeNoticeWithDelay(3000);
    }
    catch (error) {
      console.error(error);
      appModule.sendErrorNotice("Operation failed! Please try again later.");
      appModule.closeNoticeWithDelay(5000);
    }
    finally {
      this.setLoading(false);
    }
  }

  @Action
  deleteInspectline(inspectline: Inspectline) {
    if (inspectline) {
      const inspectId = this.inspect.id;
      const inspectlineId = inspectline.id;
      const { inspectlines } = this.inspect;
      inspectlines.splice(
        inspectlines.findIndex((p: Inspectline) => p.id === inspectline.id),1
      );
      this.setInspectline(inspectlines);
      deleteData(`inspectlines/${inspectlineId.toString()}`)
        .then(() => {
          this.getInspectById(inspectId.toString());
          const newInspect = this.inspect;
          this.setInspect(newInspect);
          appModule.sendSuccessNotice("Operation is done.");
          appModule.closeNoticeWithDelay(3000);
        })
        .catch((err: TODO) => {
          console.log(err);
          appModule.sendErrorNotice("Operation failed! Please try again later.");
          appModule.closeNoticeWithDelay(5000);
        });
    }
  }

  @Action
  clearInspectline() {
    this.setLoading(true);
    const inspectline = [];
    this.setInspectline(inspectline);
    this.setLoading(false);
  }

  @Action
  setDataTable(items: Inspect[]) {
    const pagination = getPagination(items);
    this.setPagination(pagination);
    this.setItems(items);
  }

  @Mutation
  setEmployees(employees: Employee[]) {
    this.employees = employees;
  }

  @Mutation
  setCategories(categories: Category[]) {
    this.categories = categories;
  }

  @Mutation
  setInspectId(id: number | null) {
    this.inspectId = id;
  }

  @Mutation
  setInspect(inspect: Inspect) {
    this.inspect = inspect;
  }

  @Mutation
  setInspectline(inspectline: Inspectline[]) {
    this.inspectline = inspectline;
  }

  @Mutation setProduct(product: Product) {
    this.product = product;
  }

  @Mutation
  setItems(inspects: Inspect[]) {
    this.items = inspects;
  }

  @Mutation
  setPagination(pagination: Pagination) {
    this.pagination = pagination;
  }

  @Mutation
  setLoading(loading: boolean) {
    this.loading = loading;
  }
}

export const inspectModule = getModule(InspectModule);
