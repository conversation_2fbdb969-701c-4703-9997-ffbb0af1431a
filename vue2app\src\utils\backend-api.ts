import axios from "axios";
import { userModule } from "@/store/modules/user";

const API_URL: string = process.env.NODE_ENV === "production"
  ? process.env.VUE_APP_PROD_SERVER_URI
  : process.env.VUE_APP_DEV_SERVER_URI;

console.log("process.env is ",process.env);
console.log("API_URL is ",API_URL);

const instance = axios.create({
  baseURL: API_URL,
  // timeout: false,
  params: {} // do not remove this, its added to add params later in the config
});

// Add a request interceptor
instance.interceptors.request.use(
  function(config) {

    const token = userModule.token;
    console.log("token", token);
    if (token) {
      config.headers.common["Authorization"] = "Bearer " + token;
      config.headers.common["Access-Control-Allow-Origin"] = "*";
    } else {
      // Use application/x-www-form-urlencoded for login
      config.headers.common["Content-Type"] =
        "application/x-www-form-urlencoded";
    }
    return config;
  },
  function(error) {
    // Do something with request error
    return Promise.reject(error);
  }
);

instance.interceptors.response.use(
  response => response,
  error => {
    console.log(error.config);
    return Promise.reject(error);
  }
);

export function getData(action) {
    let url = `${API_URL}`;
    url += action;
    return instance.get(url);
  }
export function postData(action, data) {
    let url = `${API_URL}`;
    url += action;
    return instance.post(url, data);
  }
export function putData(action, data) {
    let url = `${API_URL}`;
    url += action;
    return instance.put(url, data);
  }
export function deleteData(action) {
    let url = `${API_URL}`;
    url += action;
    return instance.delete(url);
  }
export function login(action, data) {
    let url = `${API_URL}`;
    url += action;
    return instance.get(url, data);
  }

