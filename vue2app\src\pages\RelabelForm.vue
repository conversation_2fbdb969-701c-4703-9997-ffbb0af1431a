<template>
  <v-container fluid>
    <v-flex xs12>
      <v-card class="grey lighten-4 elevation-0">
        <v-form ref="validForm" v-model="formValid" lazy-validation>
          <v-card-title class="title">
            {{ title }}
            <v-spacer></v-spacer>
            <v-btn
              elevation="4"
              fab
              small
              dark
              class="grey mr-2"
              @click.native="cancel()"
            >
              <v-icon dark="">mdi-close-circle-outline</v-icon>
            </v-btn>
            <v-btn
              elevation="4"
              fab
              small
              dark
              class="purple mr-2"
              :disabled="!formValid"
              @click.native="save()"
            >
              <v-icon>mdi-content-save-all</v-icon>
            </v-btn>
            <v-btn
              elevation="4"
              fab
              small
              dark
              class="blue"
              :disabled="!formValid"
              @click.native="addProduct()"
            >
              <v-icon>mdi-plus</v-icon>
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-container fluid grid-list-md>
              <v-layout row wrap>
                <v-flex md4 xs12>
                  <v-text-field
                    name="id"
                    label="單號"
                    type="number"
                    hint="RelabelID is required"
                    value="Input text"
                    v-model="relabel.id"
                    class="input-group--focused"
                    readonly
                  ></v-text-field>
                </v-flex>
                <v-flex md4 xs12>
                  <v-menu
                    :close-on-content-click="false"
                    v-model="classDateMenu"
                    transition="v-scale-transition"
                    offset-y
                    :nudge-left="40"
                    max-width="290px"
                  >
                    <template v-slot:activator="{ on }">
                      <v-text-field
                        v-on="on"
                        label="日期"
                        v-model="relabel.classDate"
                        prepend-icon="mdi-calendar"
                        readonly
                      ></v-text-field>
                    </template>
                    <v-date-picker
                      v-model="relabel.classDate"
                      no-title
                      scrollable
                    >
                    </v-date-picker>
                  </v-menu>
                </v-flex>
                <v-flex md4 xs12>
                  <v-radio-group
                    name="shiftName"
                    label="勤別"
                    v-model="relabel.shiftName"
                    :rules="[value => !!value || '必要!!請選擇']"
                    required
                    row
                  >
                    <v-radio label="I" value="1"></v-radio>
                    <v-radio label="II" value="2"></v-radio>
                    <v-radio label="III" value="3"></v-radio>
                  </v-radio-group>
                </v-flex>

                <v-flex md6 xs12>
                  <v-autocomplete
                    v-bind:items="employees"
                    label="人員"
                    item-text="employName"
                    item-value="employId"
                    v-model="relabel.employId"
                    :rules="[value => !!value || '必要!!請選擇']"
                    required
                  ></v-autocomplete>
                </v-flex>
                <v-flex md4 xs12>
                  <v-radio-group
                    name="groupName"
                    label="組別"
                    v-model="relabel.groupName"
                    :rules="[value => !!value || '必要!!請選擇']"
                    required
                    row
                  >
                    <v-radio label="A" value="A"></v-radio>
                    <v-radio label="B" value="B"></v-radio>
                    <v-radio label="C" value="C"></v-radio>
                    <v-radio label="D" value="D"></v-radio>
                  </v-radio-group>
                </v-flex>
                <v-flex md4 xs12>
                  <v-text-field
                    name="quantity"
                    label="小計"
                    type="number"
                    v-model="relabel.quantity"
                    class="input-group--focused"
                    readonly
                  ></v-text-field>
                </v-flex>

                <v-flex xs12>
                  <v-card>
                    <Table
                      v-if="loading === false"
                      :headers="headers"
                      :items="relabel.relabellines"
                      :pagination="pagination"
                      :setSearch="true"
                      :setEdit="false"
                      :setRemove="true"
                      :disableSort="false"
                      @remove="remove"
                    >
                    </Table>
                  </v-card>
                </v-flex>
              </v-layout>
            </v-container>
          </v-card-text>
        </v-form>
      </v-card>
    </v-flex>

    <v-layout row justify-center>
      <v-dialog v-model="addProductModal" width="700" persistent>
        <v-form ref="validDetail" v-model="detailValid" lazy-validation>
          <v-card>
            <v-card-title
              >{{ title }}
              <v-spacer></v-spacer>
              <v-card-actions>
                <v-btn
                  class="green lighten-1"
                  text="text"
                  :disabled="!detailValid"
                  @click.native="saveRelabelline"
                  >Confirm</v-btn
                >
                <v-btn
                  class="orange lighten-1"
                  text="text"
                  @click.native="cancelAddProduct"
                  >Cancel</v-btn
                >
              </v-card-actions>
            </v-card-title>
            <v-card-text>
              1.請掃Cap QRCode
              <v-text-field
                ref="qrCodeInput"
                v-model="searchFilter.contain.codeName"
                append-icon="mdi-magnify"
                label="Cap QRCode"
                @keydown.enter.prevent="getProduct"
                counter="14"
                :rules="[value => !!value || '必要!!請選擇']"
                required
              ></v-text-field>
              <v-container fluid grid-list-md>
                <v-layout row wrap>
                  <v-flex
                    md6
                    xs12
                    v-for="(item, index) in product"
                    :key="index"
                  >
                    <v-text-field
                      v-model="item.productName"
                      label="品種"
                      readonly
                    ></v-text-field>
                    <v-text-field
                      v-model="item.gradeName"
                      label="定長別"
                      readonly
                    ></v-text-field>
                    <v-text-field
                      v-model="item.workDate"
                      label="開機日期"
                      readonly
                    ></v-text-field>
                    <v-text-field
                      v-model="item.twisterNO"
                      label="Twister NO"
                      readonly
                    ></v-text-field>
                    <v-text-field
                      v-model="item.spindleNO"
                      label="Spindle NO"
                      readonly
                    ></v-text-field>
                  </v-flex>
                </v-layout>
              </v-container>
            </v-card-text>
          </v-card>
        </v-form>
      </v-dialog>
    </v-layout>
    <confirm-dialog
      :dialog="dialog"
      :dialogTitle="dialogTitle"
      :dialogText="dialogText"
      @onConfirm="onConfirm"
      @onCancel="onCancel"
    ></confirm-dialog>
    <v-snackbar
      v-if="loading === false"
      :top="'top'"
      :right="true"
      :timeout="5000"
      :color="mode"
      v-model="snackbar"
    >
    <div class="text-center">
      {{ notice }}
      <v-btn dark text @click.native="closeSnackbar">Close</v-btn>
    </div>
    </v-snackbar>
  </v-container>
</template>
<script lang="ts">
import Vue from "vue";
import Table from "@/components/Table.vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
import { Component } from "vue-property-decorator";
import { productModule } from "@/store/modules/products";
import { relabelModule } from "@/store/modules/relabels";
import { appModule } from "@/store/modules/app";
import { buildSearchFilters, getISOClassDate } from "@/utils/app-util";

@Component({
  components: {
    Table,
    ConfirmDialog
  }
})
export default class RelabelForm extends Vue {
  private modalTitle = "新增Relabel NO MFD(明細)";
  private modalText = "請掃Cap QRCode";
  private addProductModal = false;
  private dialog = false;
  private dialogTitle = "Relabel NO MFD(明細)刪除確認";
  private dialogText = "刪除該筆記錄?";
  private classDateMenu = false;
  private errors = [];
  private formValid = false;
  private detailValid = false;
  private title = "";
  private type = "NOMFD";
  private relabelId = 0;
  private relabellineId = null;
  private categoryId = "100660";
  private remarkId = null;
  private isMFD = "N";
  private color = "";
  private selectedRelabelline: null;
  private isYarnQRcode = null;
  private query = "";

  search = "";
  headers = [
    { text: "序號", left: true, value: "countdown" },
    { text: "品種", left: true, value: "productName" },
    { text: "定長別", value: "gradeName" },
    { text: "開機日期", left: true, value: "workDate" },
    { text: "Twister NO", value: "twisterNO" },
    { text: "Spindle NO", value: "spindleNO" },
    { text: "Is MFD", value: "isMFD" },
    { text: "", value: "actions", sortable: false }
  ];
  searchFilter = { contain: { codeName: "" } };

  get employees() {
    return relabelModule.employees;
  }

  get relabel() {
    return relabelModule.relabel;
  }

  get categories() {
    return productModule.categories;
  }

  get remarks() {
    return productModule.remarks;
  }

  get relabelline() {
    return relabelModule.relabelline;
  }

  get product() {
    return productModule.product;
  }

  get loading() {
    return appModule.loading;
  }

  get mode() {
    return appModule.mode;
  }

  get snackbar() {
    return appModule.snackbar;
  }

  get notice() {
    return appModule.notice;
  }

  save() {
    if (!this.relabel.id) {
        relabelModule.saveRelabel(this.relabel)
      .then(() => {
        this.saveRoute();
      })
      .catch((err: Error) => {
        console.error("Error:",err.message);
      });
    } else {
      relabelModule.saveRelabel(this.relabel)
      .then(() => {
      })
      .catch((err: Error) => {
        console.error("Error:",err.message);
      });
    }
  }

  saveRoute() {
    this.relabelId = relabelModule.relabelId;
    this.$router.push(`relabel/${this.relabelId}`);
  }

  getRelabelById() {
    relabelModule.getRelabelById(this.$route.params.id);
  }

  async getProduct() {
    buildSearchFilters(this.searchFilter);
    const codeName = this.searchFilter.contain.codeName;
    const trimmedCodeName = codeName.trim();
    if (!trimmedCodeName) {
      appModule.sendErrorNotice("請輸入Cap QRcode!");
      appModule.closeNoticeWithDelay(3000);
      return "";
    }
    this.isYarnQRcode =/^\d{14}[a-zA-Z0-9\s.]*$/.test(trimmedCodeName);
    if (!this.isYarnQRcode) {
      appModule.sendErrorNotice("無效 Cap QRcode!");
      appModule.closeNoticeWithDelay(3000);
      return "";
    }
    if (this.isYarnQRcode) {
      this.query = trimmedCodeName.slice(0, 14);
      const isDuplicate = await relabelModule.getDuplicateRelabellineByCode(this.query);
      if (isDuplicate) {
        appModule.sendErrorNotice("重複 Cap QRcode!");
        appModule.closeNoticeWithDelay(5000);
        return "";
      }
      await relabelModule.getProductById(this.query);
      const qrcode = relabelModule.product;
      if (qrcode[0]=== undefined) {
        appModule.sendErrorNotice("查無資料!");
        appModule.closeNoticeWithDelay(5000);
        return "";
      } else {
        return relabelModule.product;
      }
    }
  }

  cancel() {
    this.$router.push({ name: "relabels" });
  }

  remove(item) {
    this.selectedRelabelline = item;
    this.dialog = true;
  }

  onConfirm() {
    relabelModule.deleteRelabelline(this.selectedRelabelline);
    this.selectedRelabelline = null;
    this.getRelabelById();
    this.dialog = false;
  }

  onCancel() {
    this.selectedRelabelline = null;
    this.dialog = false;
  }

  addProduct() {
    this.addProductModal = true;
    this.query = "";
    this.searchFilter.contain.codeName = "";
    this.relabelId = this.relabel.id;
    productModule.clearProducts();
    this.$nextTick(() => {
      const validDetailRef = this.$refs.validDetail as Vue & { validate: () => boolean };
      if (validDetailRef) {
        validDetailRef.validate();
      }
      this.$nextTick(() => {
        const qrCodeInput = this.$refs.qrCodeInput as HTMLElement;
        if (qrCodeInput) {
          qrCodeInput.focus();
        }
      });
    });
  }

  saveRelabelline() {
    const RelabelId = { relabelId: this.relabel.id };
    const CategoryId = { categoryId : this.categoryId};
    const RemarkId = { remarkId : this.remarkId};
    const IsMFD = { isMFD : this.isMFD};
    const addRelabelline = this.relabelline;
    const addProduct = this.product[0];
    const newRelabelline = {
      ...RelabelId,
      ...CategoryId,
      ...RemarkId,
      ...addRelabelline,
      ...addProduct,
      ...IsMFD
    };
    relabelModule.addRelabellineToRelabel(newRelabelline);
    this.relabellineId = null;
    this.getRelabelById();
    this.resetForm();
  }

  resetForm() {
    this.searchFilter.contain.codeName = "";
    productModule.clearProducts();
    this.$nextTick(() => {
      const qrCodeInput = this.$refs.qrCodeInput as HTMLElement;
      if (qrCodeInput) {
        qrCodeInput.focus();
      }
    });
  }

  cancelAddProduct() {
    this.addProductModal = false;
    this.query = "";
    this.searchFilter.contain.codeName = "";
    relabelModule.clearRelabelline();
    productModule.clearProducts();
    this.getRelabelById();
  }

  closeSnackbar() {
    appModule.closeNotice();
  }

  get pagination() {
    return relabelModule.pagination;
  }

  created() {
    this.getRelabelById();
    relabelModule.getEmployees();
    productModule.getCategories();
    productModule.getRemarks();
  }

  mounted() {
    window.scrollTo(0, 0);
    if (this.$route.params.id) {
      this.title = "Relabel NO MFD(明細)";
      relabelModule.clearRelabelline();
      relabelModule.getRelabelById(this.$route.params.id);
      this.$nextTick(() => {
      });
    } else {
      this.title = "Relabel NO MFD(新增)";
      relabelModule.setDataTable(this.relabel[0]);
      this.relabel.typeName = this.type;
      const toDate = getISOClassDate();
      this.relabel.classDate = toDate.slice(0, 10);
      this.$nextTick(() => {
        const validDetailRef = this.$refs.validForm as Vue & { validate: () => boolean };
          if (validDetailRef) {
            validDetailRef.validate();
          }
      });
    }
  }
}
</script>
