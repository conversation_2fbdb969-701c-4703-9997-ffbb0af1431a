import { 
  onMounted, 
  onUnmounted, 
  onBeforeMount, 
  onBeforeUnmount,
  onUpdated,
  onBeforeUpdate,
  onActivated,
  onDeactivated,
  nextTick,
  ref,
  watch
} from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 生命週期選項
export interface LifecycleOptions {
  onMounted?: () => void | Promise<void>
  onUnmounted?: () => void
  onBeforeMount?: () => void
  onBeforeUnmount?: () => void
  onUpdated?: () => void
  onBeforeUpdate?: () => void
  onActivated?: () => void
  onDeactivated?: () => void
  onRouteChanged?: (to: any, from: any) => void | Promise<void>
  autoCleanup?: boolean
}

// 生命週期管理
export function useLifecycle(options: LifecycleOptions = {}) {
  const {
    onMounted: mountedCallback,
    onUnmounted: unmountedCallback,
    onBeforeMount: beforeMountCallback,
    onBeforeUnmount: beforeUnmountCallback,
    onUpdated: updatedCallback,
    onBeforeUpdate: beforeUpdateCallback,
    onActivated: activatedCallback,
    onDeactivated: deactivatedCallback,
    onRouteChanged: routeChangedCallback,
    autoCleanup = true
  } = options

  const route = useRoute()
  const router = useRouter()
  const cleanupFunctions = ref<(() => void)[]>([])
  const isMounted = ref(false)

  // 添加清理函數
  const addCleanup = (cleanup: () => void) => {
    cleanupFunctions.value.push(cleanup)
  }

  // 執行所有清理函數
  const executeCleanup = () => {
    cleanupFunctions.value.forEach(cleanup => {
      try {
        cleanup()
      } catch (error) {
        console.error('Cleanup function error:', error)
      }
    })
    cleanupFunctions.value = []
  }

  // 註冊生命週期鉤子
  if (beforeMountCallback) {
    onBeforeMount(beforeMountCallback)
  }

  onMounted(async () => {
    isMounted.value = true
    if (mountedCallback) {
      try {
        await mountedCallback()
      } catch (error) {
        console.error('Mounted callback error:', error)
      }
    }
  })

  if (beforeUpdateCallback) {
    onBeforeUpdate(beforeUpdateCallback)
  }

  if (updatedCallback) {
    onUpdated(updatedCallback)
  }

  if (activatedCallback) {
    onActivated(activatedCallback)
  }

  if (deactivatedCallback) {
    onDeactivated(deactivatedCallback)
  }

  if (beforeUnmountCallback) {
    onBeforeUnmount(beforeUnmountCallback)
  }

  onUnmounted(() => {
    isMounted.value = false
    if (unmountedCallback) {
      try {
        unmountedCallback()
      } catch (error) {
        console.error('Unmounted callback error:', error)
      }
    }
    
    if (autoCleanup) {
      executeCleanup()
    }
  })

  // 監聽路由變化
  if (routeChangedCallback) {
    const stopWatcher = watch(
      () => route.path,
      async (to, from) => {
        if (to !== from) {
          try {
            await routeChangedCallback(to, from)
          } catch (error) {
            console.error('Route changed callback error:', error)
          }
        }
      }
    )
    
    addCleanup(stopWatcher)
  }

  return {
    isMounted,
    addCleanup,
    executeCleanup
  }
}

// 表單生命週期管理
export function useFormLifecycle(options: {
  onFormMounted?: () => void | Promise<void>
  onFormUnmounted?: () => void
  onDataLoaded?: (data: any) => void
  onDataSaved?: (data: any) => void
  onValidationFailed?: (errors: any) => void
  loadDataOnMount?: boolean
  resetOnUnmount?: boolean
}) {
  const {
    onFormMounted,
    onFormUnmounted,
    onDataLoaded,
    onDataSaved,
    onValidationFailed,
    loadDataOnMount = true,
    resetOnUnmount = false
  } = options

  const route = useRoute()
  const isLoading = ref(false)
  const isInitialized = ref(false)

  // 初始化表單
  const initializeForm = async () => {
    if (isInitialized.value) return
    
    isLoading.value = true
    try {
      if (loadDataOnMount && route.params.id) {
        // 載入數據的邏輯應該在調用方實現
        // 這裡只是觸發回調
        if (onDataLoaded) {
          onDataLoaded(route.params.id)
        }
      }
      
      if (onFormMounted) {
        await onFormMounted()
      }
      
      isInitialized.value = true
    } catch (error) {
      console.error('Form initialization error:', error)
    } finally {
      isLoading.value = false
    }
  }

  // 清理表單
  const cleanupForm = () => {
    if (onFormUnmounted) {
      onFormUnmounted()
    }
    
    if (resetOnUnmount) {
      isInitialized.value = false
    }
  }

  // 數據保存成功回調
  const handleDataSaved = (data: any) => {
    if (onDataSaved) {
      onDataSaved(data)
    }
  }

  // 驗證失敗回調
  const handleValidationFailed = (errors: any) => {
    if (onValidationFailed) {
      onValidationFailed(errors)
    }
  }

  // 使用基礎生命週期
  useLifecycle({
    onMounted: initializeForm,
    onUnmounted: cleanupForm
  })

  return {
    isLoading,
    isInitialized,
    initializeForm,
    cleanupForm,
    handleDataSaved,
    handleValidationFailed
  }
}

// 事件處理管理
export function useEventHandlers() {
  const eventListeners = ref<Map<string, EventListener>>(new Map())

  // 添加事件監聽器
  const addEventListener = (
    element: EventTarget,
    event: string,
    handler: EventListener,
    options?: boolean | AddEventListenerOptions
  ) => {
    const key = `${event}_${Date.now()}_${Math.random()}`
    element.addEventListener(event, handler, options)
    eventListeners.value.set(key, () => {
      element.removeEventListener(event, handler, options)
    })
    return key
  }

  // 移除事件監聽器
  const removeEventListener = (key: string) => {
    const cleanup = eventListeners.value.get(key)
    if (cleanup) {
      cleanup()
      eventListeners.value.delete(key)
    }
  }

  // 移除所有事件監聽器
  const removeAllEventListeners = () => {
    eventListeners.value.forEach(cleanup => cleanup())
    eventListeners.value.clear()
  }

  // 防抖處理
  const debounce = <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let timeoutId: NodeJS.Timeout
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func(...args), delay)
    }
  }

  // 節流處理
  const throttle = <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let lastCall = 0
    return (...args: Parameters<T>) => {
      const now = Date.now()
      if (now - lastCall >= delay) {
        lastCall = now
        func(...args)
      }
    }
  }

  // 清理函數
  onUnmounted(() => {
    removeAllEventListeners()
  })

  return {
    addEventListener,
    removeEventListener,
    removeAllEventListeners,
    debounce,
    throttle
  }
}

// 鍵盤事件處理
export function useKeyboardEvents(options: {
  onEnter?: (event: KeyboardEvent) => void
  onEscape?: (event: KeyboardEvent) => void
  onTab?: (event: KeyboardEvent) => void
  onArrowUp?: (event: KeyboardEvent) => void
  onArrowDown?: (event: KeyboardEvent) => void
  onArrowLeft?: (event: KeyboardEvent) => void
  onArrowRight?: (event: KeyboardEvent) => void
  onCtrlS?: (event: KeyboardEvent) => void
  onCtrlZ?: (event: KeyboardEvent) => void
  onCtrlY?: (event: KeyboardEvent) => void
  preventDefault?: boolean
  stopPropagation?: boolean
} = {}) {
  const {
    onEnter,
    onEscape,
    onTab,
    onArrowUp,
    onArrowDown,
    onArrowLeft,
    onArrowRight,
    onCtrlS,
    onCtrlZ,
    onCtrlY,
    preventDefault = false,
    stopPropagation = false
  } = options

  const { addEventListener } = useEventHandlers()

  const handleKeydown = (event: KeyboardEvent) => {
    if (preventDefault) event.preventDefault()
    if (stopPropagation) event.stopPropagation()

    switch (event.key) {
      case 'Enter':
        if (onEnter) onEnter(event)
        break
      case 'Escape':
        if (onEscape) onEscape(event)
        break
      case 'Tab':
        if (onTab) onTab(event)
        break
      case 'ArrowUp':
        if (onArrowUp) onArrowUp(event)
        break
      case 'ArrowDown':
        if (onArrowDown) onArrowDown(event)
        break
      case 'ArrowLeft':
        if (onArrowLeft) onArrowLeft(event)
        break
      case 'ArrowRight':
        if (onArrowRight) onArrowRight(event)
        break
      case 's':
        if (event.ctrlKey && onCtrlS) {
          event.preventDefault()
          onCtrlS(event)
        }
        break
      case 'z':
        if (event.ctrlKey && !event.shiftKey && onCtrlZ) {
          event.preventDefault()
          onCtrlZ(event)
        }
        break
      case 'y':
        if (event.ctrlKey && onCtrlY) {
          event.preventDefault()
          onCtrlY(event)
        }
        break
    }
  }

  onMounted(() => {
    addEventListener(document, 'keydown', handleKeydown)
  })

  return {
    handleKeydown
  }
}

// 窗口事件處理
export function useWindowEvents(options: {
  onResize?: (event: Event) => void
  onScroll?: (event: Event) => void
  onBeforeUnload?: (event: BeforeUnloadEvent) => void
  onFocus?: (event: FocusEvent) => void
  onBlur?: (event: FocusEvent) => void
  debounceDelay?: number
} = {}) {
  const {
    onResize,
    onScroll,
    onBeforeUnload,
    onFocus,
    onBlur,
    debounceDelay = 100
  } = options

  const { addEventListener, debounce } = useEventHandlers()

  onMounted(() => {
    if (onResize) {
      const debouncedResize = debounce(onResize, debounceDelay)
      addEventListener(window, 'resize', debouncedResize)
    }

    if (onScroll) {
      const debouncedScroll = debounce(onScroll, debounceDelay)
      addEventListener(window, 'scroll', debouncedScroll)
    }

    if (onBeforeUnload) {
      addEventListener(window, 'beforeunload', onBeforeUnload)
    }

    if (onFocus) {
      addEventListener(window, 'focus', onFocus)
    }

    if (onBlur) {
      addEventListener(window, 'blur', onBlur)
    }
  })
}
