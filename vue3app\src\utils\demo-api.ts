import { Component, Vue } from 'vue-facing-decorator'
import { buildSearchFilters } from './app-util'
import url from 'url'
import querystring from 'querystring'

interface TODO {
  [key: string]: any
}

const DB: TODO = {
  users: [
    { id: 1, username: 'admin', password: 'admin', email: '<EMAIL>', roles: ['admin'] }
  ]
}

@Component({
  name: 'Demo<PERSON><PERSON>'
})
export class <PERSON><PERSON><PERSON><PERSON> extends Vue {
  private ds: TODO = DB

  getModel(pathname: string | null): string {
    if (!pathname) return ''
    const parts = pathname.split('/')
    return parts[1] || ''
  }

  getId(pathname: string | null): string {
    if (!pathname) return ''
    const parts = pathname.split('/')
    return parts[2] || ''
  }

  getExpand(qs: querystring.ParsedUrlQuery): string[] {
    const expand = qs._expand || []
    return Array.isArray(expand) ? expand : [expand]
  }

  getEmbed(qs: querystring.ParsedUrlQuery): string[] {
    const embed = qs._embed || []
    return Array.isArray(embed) ? embed : [embed]
  }

  getParent(name: string, id: string | number): TODO | null {
    return Object.keys(this.ds)
      .map(key => this.ds[key])
      .find(x => x[name + 'Id'] === id)
  }

  processRequest(req: string): TODO {
    const parsedUrl = url.parse(req)
    const parsedQs = querystring.parse(parsedUrl.query || '')
    const model = this.getModel(parsedUrl.pathname)
    const id = this.getId(parsedUrl.pathname)
    const exp = this.getExpand(parsedQs)
    const emb = this.getEmbed(parsedQs)

    let results = this.ds[model] || []

    if (id) {
      results = results.filter((x: TODO) => x.id.toString() === id.toString())
    }

    if (exp.length > 0) {
      results = results.map((x: TODO) => {
        const expanded = { ...x }
        exp.forEach(e => {
          const parent = this.getParent(e, x.id)
          if (parent) {
            expanded[e] = parent
          }
        })
        return expanded
      })
    }

    if (emb.length > 0) {
      results = results.map((x: TODO) => {
        const embedded = { ...x }
        emb.forEach(e => {
          embedded[e] = this.ds[e].filter((y: TODO) => y[model + 'Id'] === x.id)
        })
        return embedded
      })
    }

    return results
  }

  login(username: string, password: string): Promise<TODO> {
    return new Promise((resolve, reject) => {
      const user = this.ds.users.find((u: TODO) =>
        u.username === username && u.password === password
      )
      if (user) {
        resolve({
          data: {
            token: 'fake-jwt-token',
            user: { ...user, password: undefined }
          }
        })
      } else {
        reject(new Error('Username or password is incorrect'))
      }
    })
  }
}

const demoApi = new DemoApi({ name: 'DemoApi' }, {})

// Export functions to match existing usage
export const login = (action: string, data: any) => demoApi.login(data.username, data.password)
export const processRequest = (req: string) => demoApi.processRequest(req)
