import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getData, putData, postData, deleteData } from '@/utils/backend-api'
import type { Employee, Disposal, Entity, Product, Category, Remark, Disposalline, Pagination } from '@/types'

export const useDisposalsStore = defineStore('disposals', () => {
  // State
  const items = ref<Entity[]>([])
  const pagination = ref<Pagination>({
    page: 1,
    limit: 10,
    totalItems: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false
  })
  const loading = ref(false)
  const employee = ref('')
  const disposalId = ref<number | null>(null)
  const disposal = ref<Disposal>({} as Disposal)
  const disposallines = ref<Disposalline[]>([])
  const product = ref<Product[]>([])
  const employees = ref<Employee[]>([])
  const categories = ref<Category[]>([])
  const remarks = ref<Remark[]>([])

  // Getters
  const isLoading = computed(() => loading.value)
  const currentDisposal = computed(() => disposal.value)
  const currentDisposallines = computed(() => disposallines.value)

  // Actions
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setDisposal = (value: Disposal) => {
    // 如果是部分更新，合併現有資料
    if (disposal.value && Object.keys(disposal.value).length > 0) {
      disposal.value = { ...disposal.value, ...value }
    } else {
      disposal.value = value
    }
  }

  const setDisposallines = (value: Disposalline[]) => {
    disposallines.value = value
  }

  const setEmployees = (value: Employee[]) => {
    employees.value = value
  }

  const setCategories = (value: Category[]) => {
    categories.value = value
  }

  const setRemarks = (value: Remark[]) => {
    remarks.value = value
  }

  // API Actions
  const getEmployees = async () => {
    try {
      setLoading(true)
      const res = await getData("employees/tw")
      if (res.data) {
        // 先去重，再處理數據
        const uniqueData = res.data.filter((employee: any, index: number, self: any[]) =>
          index === self.findIndex((e: any) => e.employId === employee.employId)
        )

        const employeeList = uniqueData.map((c: any, index: number) => {
          return {
            // 確保每個項目都有唯一的 key，使用 employId 作為主鍵
            employId: c.employId,
            employNO: c.employNO,
            userName: c.userName,
            // 根據 Vue2 的邏輯，顯示格式為 employNO + userName
            employName: c.employNO + " " + c.userName,
            // 使用 employId 作為 value，與後端 API 一致
            value: c.employId,
            // 為 Vuetify 提供唯一的 key
            key: `employee_${c.employId}_${index}`,
            // 其他可能需要的欄位
            ...c
          }
        })

        console.log('Store: 員工數據處理完成:', employeeList)
        setEmployees(employeeList)
      }
    } catch (error) {
      console.error('獲取員工列表失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const getCategories = async () => {
    try {
      const res = await getData("categories")
      if (res.data) {
        setCategories(res.data)
      }
    } catch (error) {
      console.error('獲取分類列表失敗:', error)
      throw error
    }
  }

  const getRemarks = async () => {
    try {
      const res = await getData("remarks")
      if (res.data) {
        setRemarks(res.data)
      }
    } catch (error) {
      console.error('獲取備註列表失敗:', error)
      throw error
    }
  }

  const getDisposalById = async (id: string | number) => {
    try {
      setLoading(true)
      console.log('Store: 開始獲取報廢記錄，ID:', id)
      const res = await getData(`downgrades/${id}`)
      console.log('Store: API 響應:', res)

      if (res.data && Array.isArray(res.data) && res.data.length > 0) {
        // 後端返回的是數組，取第一個元素
        const disposalData = res.data[0]
        // 將downgradelines轉換為disposallines以保持一致性
        if (disposalData.downgradelines) {
          disposalData.disposallines = disposalData.downgradelines
          delete disposalData.downgradelines
        }
        console.log('Store: 設置主檔數據:', disposalData)
        setDisposal(disposalData)
        disposalId.value = Number(id)

        // 同時獲取明細檔
        if (disposalData.disposallines && Array.isArray(disposalData.disposallines)) {
          console.log('Store: 設置明細檔數據:', disposalData.disposallines)
          setDisposallines(disposalData.disposallines)
        } else {
          console.log('Store: 沒有明細檔數據')
          setDisposallines([])
        }
      } else {
        console.log('Store: API 響應格式不正確或無數據')
        setDisposal({} as Disposal)
        setDisposallines([])
      }
      return res.data
    } catch (error) {
      console.error('獲取報廢記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const saveDisposal = async (data: Disposal) => {
    try {
      setLoading(true)
      let res

      if (data.id && data.id > 0) {
        // 更新現有記錄 - 使用 PUT 方法
        console.log('Store: 更新現有記錄，ID:', data.id)
        res = await putData(`downgrades/${data.id}`, data)
        if (res.data) {
          // PUT 請求通常返回更新後的單個對象
          const updatedDisposal = Array.isArray(res.data) ? res.data[0] : res.data
          setDisposal(updatedDisposal)
          disposalId.value = updatedDisposal.id
        }
      } else {
        // 創建新記錄 - 使用 POST 方法
        console.log('Store: 創建新記錄')
        res = await postData('downgrades/', data)
        if (res.data && Array.isArray(res.data) && res.data.length > 0) {
          // 後端返回數組，取第一個元素
          const newDisposal = res.data[0]
          setDisposal(newDisposal)
          disposalId.value = newDisposal.id
        }
      }

      return res.data
    } catch (error) {
      console.error('保存報廢記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const updateDisposal = async (data: Disposal) => {
    return await saveDisposal(data)
  }

  const deleteDisposal = async (id: number) => {
    try {
      setLoading(true)
      await deleteData(`downgrades/${id}`)

      // 如果刪除的是當前記錄，清空狀態
      if (disposalId.value === id) {
        disposal.value = {} as Disposal
        disposallines.value = []
        disposalId.value = null
      }
    } catch (error) {
      console.error('刪除報廢記錄失敗:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 明細檔操作
  const addDisposallineToDisposal = async (data: Disposalline) => {
    try {
      const res = await postData('downgradelines/', data)
      if (res.data) {
        // 重新載入明細檔
        if (disposalId.value) {
          await getDisposalById(disposalId.value)
        }
      }
      return res.data
    } catch (error) {
      console.error('新增明細記錄失敗:', error)
      throw error
    }
  }

  const updateDisposalline = async (data: Disposalline) => {
    try {
      const res = await putData(`downgradelines/${data.id}`, data)
      if (res.data) {
        // 重新載入明細檔
        if (disposalId.value) {
          await getDisposalById(disposalId.value)
        }
      }
      return res.data
    } catch (error) {
      console.error('更新明細記錄失敗:', error)
      throw error
    }
  }

  const deleteDisposalline = async (data: Disposalline) => {
    try {
      await deleteData(`downgradelines/${data.id}`)

      // 重新載入明細檔
      if (disposalId.value) {
        await getDisposalById(disposalId.value)
      }
    } catch (error) {
      console.error('刪除明細記錄失敗:', error)
      throw error
    }
  }

  // 檢查重複的 QR Code
  const getDuplicateDisposallineByCode = async (code: string): Promise<boolean> => {
    try {
      setLoading(true)
      if (code) {
        const res = await getData(`downgradelines/duplicate/${code}`)
        const data = res.data
        if (data !== undefined && data !== null && Array.isArray(data) && data.length > 0) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    } catch (error: any) {
      // 404 錯誤表示沒有重複，這是正常情況
      if (error?.response?.status === 404) {
        console.log('沒有重複的 QR Code，可以繼續')
        return false
      }
      console.error('檢查重複 QR Code 失敗:', error)
      return false
    } finally {
      setLoading(false)
    }
  }

  // 載入報廢列表
  const loadDisposals = async (page: number = 1, limit: number = 10) => {
    try {
      setLoading(true)
      const response = await getData('downgrades/disposal')

      if (response.data && Array.isArray(response.data)) {
        // 為每筆記錄獲取明細檔數量
        const disposalsWithQuantity = await Promise.all(
          response.data.map(async (item: any) => {
            try {
              // 獲取每筆記錄的明細檔
              const detailResponse = await getData(`downgrades/${item.id}`)
              let quantity = 0

              if (detailResponse.data && Array.isArray(detailResponse.data) && detailResponse.data.length > 0) {
                const detailData = detailResponse.data[0]
                quantity = detailData.downgradelines?.length || 0
              }

              return {
                ...item,
                quantity
              }
            } catch (error) {
              console.warn(`獲取記錄 ${item.id} 的明細檔失敗:`, error)
              return {
                ...item,
                quantity: 0
              }
            }
          })
        )

        // 由於後端沒有分頁，我們在前端模擬分頁
        const totalItems = disposalsWithQuantity.length
        const startIndex = (page - 1) * limit
        const endIndex = startIndex + limit
        const paginatedItems = disposalsWithQuantity.slice(startIndex, endIndex)

        items.value = paginatedItems
        pagination.value = {
          ...pagination.value,
          page,
          limit,
          totalItems,
          totalPages: Math.ceil(totalItems / limit),
          hasNextPage: page < Math.ceil(totalItems / limit),
          hasPrevPage: page > 1
        }
      } else {
        items.value = []
        pagination.value = {
          ...pagination.value,
          page: 1,
          limit: 10,
          totalItems: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPrevPage: false
        }
      }
      return items.value
    } catch (error) {
      console.error('載入報廢列表失敗:', error)
      items.value = []
      pagination.value = {
        ...pagination.value,
        page: 1,
        limit: 10,
        totalItems: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPrevPage: false
      }
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 搜尋報廢記錄
  const searchDisposals = async (searchQuery: string) => {
    try {
      setLoading(true)
      const response = await getData(`disposals${searchQuery}`)
      if (response.data && Array.isArray(response.data)) {
        // 計算每筆記錄的數量
        const disposalsWithQuantity = response.data.map((item: Disposal) => ({
          ...item,
          quantity: item.disposallines?.length || 0
        }))
        items.value = disposalsWithQuantity
      } else {
        items.value = []
      }
    } catch (error) {
      console.error('搜尋報廢記錄失敗:', error)
      items.value = []
    } finally {
      setLoading(false)
    }
  }

  // 快速搜尋
  const quickSearch = async (headers: any[], qsFilter: string) => {
    try {
      setLoading(true)
      const response = await getData('disposals')
      if (response.data && Array.isArray(response.data)) {
        const filteredData = response.data.filter((r: any) =>
          headers.some((header: any) => {
            const val = r[header.value]
            return (
              (val &&
                val
                  .toString()
                  .toLowerCase()
                  .includes(qsFilter.toLowerCase())) ||
              false
            )
          })
        )

        // 計算每筆記錄的數量
        const disposalsWithQuantity = filteredData.map((item: Disposal) => ({
          ...item,
          quantity: item.disposallines?.length || 0
        }))
        items.value = disposalsWithQuantity
      } else {
        items.value = []
      }
    } catch (error) {
      console.error('快速搜尋失敗:', error)
      items.value = []
    } finally {
      setLoading(false)
    }
  }

  // 根據類型獲取所有報廢記錄
  const getAllDisposalsByType = async (type: string) => {
    try {
      setLoading(true)
      // 修正API端點，使用正確的路徑
      const response = await getData('disposals')
      if (response.data && Array.isArray(response.data)) {
        // 計算每筆記錄的數量
        const disposalsWithQuantity = response.data.map((item: Disposal) => ({
          ...item,
          quantity: item.disposallines?.length || 0
        }))
        items.value = disposalsWithQuantity
      } else {
        items.value = []
      }
    } catch (error) {
      console.error('獲取報廢記錄失敗:', error)
      items.value = []
    } finally {
      setLoading(false)
    }
  }

  // 清空明細檔
  const clearDisposalline = () => {
    setLoading(true)
    setDisposallines([])
    setLoading(false)
  }

  // 獲取產品資料 (與vue2app相同的API)
  const getProductById = async (id: string) => {
    try {
      setLoading(true)
      if (id) {
        const res = await getData(`products/${id}`)
        const productData = res.data
        // 設置到products store中，保持與現有邏輯一致
        if (productData && Array.isArray(productData)) {
          // 如果返回的是數組，直接使用
          product.value = productData
        } else if (productData) {
          // 如果返回的是單個對象，包裝成數組
          product.value = [productData]
        } else {
          product.value = []
        }
        return productData
      } else {
        product.value = []
        return null
      }
    } catch (error) {
      console.error('獲取產品資料失敗:', error)
      product.value = []
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 重置狀態
  const resetState = () => {
    items.value = []
    disposal.value = {} as Disposal
    disposallines.value = []
    disposalId.value = null
    employee.value = ''
    loading.value = false
  }

  return {
    // State
    items,
    pagination,
    loading,
    employee,
    disposalId,
    disposal,
    disposallines,
    product,
    employees,
    categories,
    remarks,

    // Getters
    isLoading,
    currentDisposal,
    currentDisposallines,

    // Actions
    setLoading,
    setDisposal,
    setDisposallines,
    setEmployees,
    setCategories,
    setRemarks,
    getEmployees,
    getCategories,
    getRemarks,
    getDisposalById,
    saveDisposal,
    updateDisposal,
    deleteDisposal,
    addDisposallineToDisposal,
    updateDisposalline,
    deleteDisposalline,
    getDuplicateDisposallineByCode,
    loadDisposals,
    searchDisposals,
    quickSearch,
    getAllDisposalsByType,
    clearDisposalline,
    getProductById,
    resetState
  }
})
