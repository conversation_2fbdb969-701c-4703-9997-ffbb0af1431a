import { Component, Vue } from 'vue-facing-decorator'
import { createApp } from 'vue'
import { getData, putData, postData, deleteData } from '@/utils/backend-api'
import { Product, Entity, Category, Remark, Pagination, TableHeader } from '@/types'
import { getDefaultPagination, getPagination } from '@/utils/store-util'
import { appModule } from '@/stores/app'
import { get } from 'lodash'

export interface ProductState {
  items: Entity[]
  pagination: Pagination
  loading: boolean
  product: Product[]
  categories: Category[]
  remarks: Remark[]
}

@Component({
  name: 'ProductModule'
})
class ProductModule extends Vue implements ProductState {
  items: Entity[] = []
  pagination = getDefaultPagination()
  loading = false
  product: Product[] = []
  categories: Category[] = []
  remarks: Remark[] = []

  getCategories = () => {
    getData("categories").then(res => {
      if (res.data) {
        const categories = res.data.map((c: Category) => {
          c.text = c.categoryName
          c.value = c.id
          return c
        })
        this.setCategories(categories)
      }
    })
  }

  getRemarks = () => {
    getData("remarks").then(res => {
      if (res.data) {
        const remarks = res.data.map((r: Remark) => {
          r.text = r.remarkName
          r.value = r.id
          return r
        })
        this.setRemarks(remarks)
      }
    })
  }

  getProductById = async (id: string) => {
    try {
      this.setLoading(true)
      if (id) {
        const res = await getData("products/" + id)
        const product = res.data
        this.setProduct(product)
      } else {
        this.setProduct([])
      }
    } catch (err) {
      console.log(err)
    } finally {
      this.setLoading(false)
    }
  }

  clearProducts = () => {
    this.setLoading(true)
    this.setProduct([])
    this.setLoading(false)
  }

  setCategories = (categories: Category[]) => {
    this.categories = categories
  }

  setRemarks = (remarks: Remark[]) => {
    this.remarks = remarks
  }

  setProduct = (product: Product[]) => {
    this.product = product
  }

  setItems = (products: Product[]) => {
    this.items = products
  }

  setPagination = (pagination: Pagination) => {
    this.pagination = pagination
  }

  setLoading = (loading: boolean) => {
    this.loading = loading
  }
}

// Create and export a singleton instance
const app = createApp(ProductModule)
const vm = app.mount(document.createElement('div'))
export const productModule = vm as InstanceType<typeof ProductModule>
