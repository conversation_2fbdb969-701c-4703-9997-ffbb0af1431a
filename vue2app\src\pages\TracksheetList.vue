<template>
  <v-container fluid>
    <v-flex xs12>
      <v-card>
        <v-card-title>
          <span class="title"
            >{{title}}
            {{ pagination ? "(" + pagination.totalItems + ")" : "" }}
          </span>
          <v-spacer></v-spacer>
          <table-header-buttons :reloadData="clearData"></table-header-buttons>
        </v-card-title>
        <v-card-text>
          <v-text-field
            ref="searchInput"
            v-model="searchFilter.contain.trackNO"
            append-icon="mdi-magnify"
            label="傳票QRCode 或 Cap QRCode"
            @change="getTrack"
            counter="27"
            single-line
            hide-details
          ></v-text-field>
        </v-card-text>
        <Table
          v-if="loading === false"
          :headers="selectedHeaders"
          :items="items"
          :pagination="pagination"
          :setSearch="false"
          :setEdit="false"
          :setRemove="false"
          :disableSort="true"

        ></Table>
      </v-card>
    </v-flex>
    <search-panel
      :rightDrawer="rightDrawer"
      @cancelSearch="cancelSearch"
      @searchData="searchTracksheets"
    >
    </search-panel>
    <v-snackbar
      v-if="loading === false"
      :top="'top'"
      :right="true"
      :timeout="5000"
      :color="mode"
      v-model="snackbar"
    >
    <div class="text-center">
      {{ notice }}
      <v-btn dark text @click.native="closeSnackbar">Close</v-btn>
    </div>
    </v-snackbar>
  </v-container>
</template>
<script lang="ts">
import Table from "@/components/Table.vue";
import TableHeaderButtons from "@/components/TableHeaderButtons.vue";
import SearchPanel from "@/components/SearchPanel.vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
import { debounce } from "lodash";
import {
  buildSearchFilters,
  buildJsonServerQuery,
  clearSearchFilters
} from "@/utils/app-util";
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { productModule } from "@/store/modules/products";
import { tracksheetModule } from "@/store/modules/tracksheets";
import { appModule } from "@/store/modules/app";

@Component({
  components: {
    Table,
    TableHeaderButtons,
    SearchPanel,
    ConfirmDialog
  }
})
export default class TrackSheetList extends Vue {
  dialog = false;
  dialogTitle = "TrackSheet Delete Dialog";
  dialogText = "Do you want to delete this record?";
  showSearchPanel = false;
  right = true;
  search = "";
  // Define computed property to select appropriate headers based on a condition
  get selectedHeaders() {
    if (this.isCakeTrackSheetNO) {
      //console.log("this.isCakeTrackSheetNO is ", this.isCakeTrackSheetNO);
      return this.cakeTrackSheetHeaders;
    } else if (this.isYarnTrackSheetNO) {
      //console.log("this.isYarnTrackSheetNO is ", this.isYarnTrackSheetNO);
      return this.yarnTrackSheetHeaders;
    } else if (this.isYarnQRcode) {
      //console.log("this.isYarnQRcode is ", this.isYarnQRcode);
      return this.yarnQRcodeHeaders;
    }
    // Default headers if no condition matches
    return this.headers;
  }

  headers = [
    { text: "追踨傳票單號", left: true, value: "tracksheetNO" },
    { text: "日期時間", left: true, value: "tracksheetTime" },
    { text: "爐別", left: true, value: "furnaceName" },
    { text: "品種", left: true, value: "productName" },
    { text: "個數", left: true, value: "tracksheetQty" },
    { text: "淨重(kg)", left: true, value: "tracksheetNetWeight" },
    { text: "", value: "actions", sortable: false }
  ];

  searchFilter = { contain: { trackNO: "" } };

  private title = "";
  private type = "";
  private trackId = "";
  private tracks : string[] = [];
  private isYarnQRcode = null;
  private isCakeTrackSheetNO = null;
  private isYarnTrackSheetNO = null;
  private query = "";
  private snackbarStatus = false;
  private timeout = 2000;
  private color = "";
  private quickSearchFilter = "";
  private itemId = -1;


  // Define Yarn QRcode header arrays for different conditions
  get yarnQRcodeHeaders() {
    return [
    { text: "BI檢測", value: "biName" },
    { text: "TEX檢測", value: "texName" },
    { text: "Lot NO", value: "batchName" },
    { text: "乾燥時間(hrs)", value: "dryTime" },
    { text: "過磅日期", left: true, value: "productDate" },
    { text: "品種", left: true, value: "productName" },
    { text: "等級", value: "gradeName" },
    { text: "爐別", left: true, value: "furnaceName" },
    { text: "Bushing NO", value: "bushingNO" },
    { text: "Cake位置", value: "positionName" },
    { text: "Cake重量(g)", value: "cakeWeight" },
    { text: "開機日期", left: true, value: "workDate" },
    { text: "Twister NO", value: "twisterNO" },
    { text: "Spindle NO", value: "spindleNO" },
    { text: "Is MFD", value: "isMFD" },
    { text: "", value: "actions", sortable: false }
    ];
  }

  // Define Cake header arrays for different conditions
  get cakeTrackSheetHeaders() {
    return [
    { text: "追踪傳票單號", left: true, value: "tracksheetNO" },
    { text: "日期時間", left: true, value: "tracksheetTime" },
    { text: "爐別", left: true, value: "furnaceName" },
    { text: "品種", left: true, value: "productName" },
    { text: "等級", left: true, value: "gradeName" },
    { text: "個數", left: true, value: "tracksheetQty" },
    { text: "淨重(kg)", left: true, value: "tracksheetNetWeight" },
    { text: "", value: "actions", sortable: false }
    ];
  }

    // Define Yarn header arrays for different conditions
  get yarnTrackSheetHeaders() {
    return [
    { text: "追踪傳票單號", left: true, value: "documentNO" },
    { text: "日期時間", left: true, value: "tracksheetTime" },
    { text: "品種", left: true, value: "productName" },
    { text: "Twister NO", value: "twisterNO" },
    { text: "開機T1個數", left: true, value: "trackT1Qty" },
    { text: "開機T2個數", left: true, value: "trackT2Qty" },
    { text: "品檢T1小計", left: true, value: "icountT1Sum" },
    { text: "品檢T2小計", left: true, value: "icountT2Sum" },
    { text: "追踪傳票序號", left: true, value: "tracksheetNO" },
    { text: "爐別", left: true, value: "furnaceName" },
    { text: "品檢T1個數", left: true, value: "icountT1Qty" },
    { text: "品檢T2個數", left: true, value: "icountT2Qty" },
    { text: "", value: "actions", sortable: false }
    ];
  }

  getTrack() {
    buildSearchFilters(this.searchFilter);

    const trackNO = this.searchFilter.contain.trackNO;
    const trimmedTrackNO = trackNO.trim();

    if (!trimmedTrackNO) {
      appModule.sendErrorNotice("請輸入傳票單號or Cap QRCode");
      appModule.closeNoticeWithDelay(3000);
      return "";
    }

    this.isYarnQRcode = /^\d{14}[a-zA-Z0-9\s.]*$/.test(trimmedTrackNO);
    this.isCakeTrackSheetNO = !!(
      /^F\d{11}\s*,[A-Za-z0-9]{10}$/.test(trimmedTrackNO) ||
      /^D\d{11}\s*$/.test(trimmedTrackNO)
    );
    this.isYarnTrackSheetNO = /^G\d{12}-\d{1}$/.test(trimmedTrackNO) ||  /^G\d{12}-\d{1}-S\d{2}$/.test(trimmedTrackNO);

    if (!this.isYarnQRcode &&!this.isCakeTrackSheetNO && !this.isYarnTrackSheetNO) {
      appModule.sendErrorNotice("無效的傳票單號or Cap QRCode!");
      appModule.closeNoticeWithDelay(3000);
      productModule.clearProducts();
      tracksheetModule.clearTracksheets();
      return "";
    }

      if (this.isYarnQRcode) {
      this.query = trimmedTrackNO.slice(0, 14);
      productModule.getProductById(this.query);
      return productModule.product;
    }

    if (this.isCakeTrackSheetNO) {
      this.type = "CAKE";
      this.trackId = trimmedTrackNO.slice(0, 12);
      this.query = trimmedTrackNO.slice(1, 12);
      this.tracks = [this.type,this.query,this.trackId];
      tracksheetModule.getTracksheetByCode(this.tracks);
      return tracksheetModule.tracksheet;
    }

    if (this.isYarnTrackSheetNO) {
      this.type = "QI";
      this.trackId = trimmedTrackNO.slice(0, 15);
      this.query = trimmedTrackNO.slice(1, 13);
      this.tracks = [this.type,this.query,this.trackId];
      tracksheetModule.getTracksheetByCode(this.tracks);
      return tracksheetModule.tracksheet;
    }
  }

  searchTracksheets() {
    this.showSearchPanel = !this.showSearchPanel;
    buildSearchFilters(this.searchFilter);
    this.query = buildJsonServerQuery(this.searchFilter);
    this.quickSearch = "";
    tracksheetModule.searchTracksheets(this.query);
  }

  clearSearchFilters() {
    this.showSearchPanel = !this.showSearchPanel;
    clearSearchFilters(this.searchFilter);
  }

  clearData() {
    this.query = "";
    this.searchFilter.contain.trackNO = "";
    productModule.clearProducts();
    tracksheetModule.clearTracksheets();
    this.$nextTick(() => {
        const searchInput = this.$refs.searchInput as HTMLElement;
          if (searchInput) {
          searchInput.focus();
          }
    });
  }

  updateSearchPanel() {
    this.rightDrawer = !this.rightDrawer;
  }

  cancelSearch() {
    this.showSearchPanel = false;
  }

  closeSnackbar() {
    appModule.closeNotice();
  }

  quickSearchTrackSheets = debounce(function() {
    tracksheetModule.quickSearch(this.headers, this.quickSearchFilter);
  }, 500);

  get items() {
    if (this.isYarnQRcode) {
      return productModule.items;
    }
    else{
      return tracksheetModule.items;
    }
  }
  get pagination() {
    if (this.isYarnQRcode) {
      return productModule.pagination;
    }
    else{
      return tracksheetModule.pagination;
    }
  }
  get loading() {
    return appModule.loading;
  }
  get mode() {
    return appModule.mode;
  }
  get snackbar() {
    return appModule.snackbar;
  }
  get notice() {
    return appModule.notice;
  }

  get rightDrawer() {
    return this.showSearchPanel;
  }

  set rightDrawer(rightDrawer: boolean) {
    this.showSearchPanel = rightDrawer;
  }

  get quickSearch() {
    return this.quickSearchFilter;
  }
  set quickSearch(val) {
    this.quickSearchFilter = val;
    this.quickSearchFilter && this.quickSearchTrackSheets();
  }

  created() {
    productModule.clearProducts();
    tracksheetModule.clearTracksheets();
  }

  mounted() {
    window.scrollTo(0, 0);
    this.$nextTick(() => {
      this.title = "EM即時生產查詢";
      this.$nextTick(() => {
        const searchInput = this.$refs.searchInput as HTMLElement;
          if (searchInput) {
          searchInput.focus();
          }
      });
    });
  }
}
</script>
