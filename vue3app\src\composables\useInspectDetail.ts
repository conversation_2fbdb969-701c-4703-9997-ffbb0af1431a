import { ref, computed } from 'vue'
import { useInspectsStore } from '@/stores/inspects'
import type { Inspectline } from '@/types'

export interface InspectDetailOptions {
  inspectId: number | null
  onDetailSaved?: (detail: Inspectline) => void
  onDetailDeleted?: (detail: Inspectline) => void
  onError?: (error: any, operation: string) => void
}

export function useInspectDetail(options: InspectDetailOptions) {
  const inspectsStore = useInspectsStore()

  // 明細檔數據
  const detailItems = ref<Inspectline[]>([])
  const detailDialog = ref(false)
  const detailForm = ref()
  const detailFormValid = ref(false)
  const editingDetailId = ref<number | null>(null)
  const loading = ref(false)

  const detailData = ref<Partial<Inspectline>>({
    codeName: '',
    remarkName: '',
    quantity: 1
  })

  // 明細表格標題
  const detailHeaders = [
    { title: '序號', key: 'id' },
    { title: 'Cap QRCode', key: 'codeName' },
    { title: '異常原因', key: 'remarkName' },
    { title: '數量', key: 'quantity' },
    { title: '操作', key: 'actions', sortable: false }
  ]

  // 驗證規則
  const validationRules = {
    required: [(value: any) => !!value || '此欄位為必填'],
    number: [(value: any) => !isNaN(Number(value)) || '請輸入有效數字']
  }

  // 計算屬性
  const currentInspectlines = computed(() => inspectsStore.currentInspectlines)
  const hasDetails = computed(() => detailItems.value.length > 0)
  const isEditing = computed(() => !!editingDetailId.value)

  // 通知訊息
  const snackbar = ref({
    show: false,
    message: '',
    color: 'success'
  })

  const showMessage = (message: string, color: string = 'success') => {
    snackbar.value = {
      show: true,
      message,
      color
    }
  }

  // 明細檔操作
  const openDetailDialog = () => {
    editingDetailId.value = null
    detailData.value = {
      codeName: '',
      remarkName: '',
      quantity: 1
    }
    detailDialog.value = true
  }

  const editDetailItem = (item: Inspectline) => {
    editingDetailId.value = item.id
    detailData.value = { ...item }
    detailDialog.value = true
  }

  const deleteDetailItem = async (item: Inspectline) => {
    if (confirm('確定要刪除此明細記錄嗎？')) {
      try {
        loading.value = true
        await inspectsStore.deleteInspectline(item)
        await loadDetailData()
        showMessage('刪除成功', 'success')
        options.onDetailDeleted?.(item)
      } catch (error) {
        showMessage('刪除失敗', 'error')
        options.onError?.(error, 'delete')
      } finally {
        loading.value = false
      }
    }
  }

  const saveDetailItem = async () => {
    if (!options.inspectId) {
      showMessage('請先保存主檔', 'warning')
      return
    }

    try {
      loading.value = true
      
      if (editingDetailId.value) {
        // 編輯明細
        await inspectsStore.updateInspectline({
          ...detailData.value,
          id: editingDetailId.value,
          inspectId: options.inspectId
        } as Inspectline)
      } else {
        // 新增明細
        await inspectsStore.addInspectlineToInspect({
          ...detailData.value,
          inspectId: options.inspectId
        } as Inspectline)
      }

      await loadDetailData()
      closeDetailDialog()
      showMessage('明細儲存成功', 'success')
      options.onDetailSaved?.(detailData.value as Inspectline)
    } catch (error) {
      showMessage('明細儲存失敗', 'error')
      options.onError?.(error, 'save')
    } finally {
      loading.value = false
    }
  }

  const closeDetailDialog = () => {
    detailDialog.value = false
    detailForm.value?.resetValidation()
  }

  // 載入明細數據
  const loadDetailData = async () => {
    if (options.inspectId) {
      try {
        await inspectsStore.getInspectById(options.inspectId.toString())
        detailItems.value = inspectsStore.currentInspectlines
      } catch (error) {
        console.error('載入明細數據失敗:', error)
        options.onError?.(error, 'load')
      }
    }
  }

  // 批量操作
  const deleteSelectedItems = async (selectedItems: Inspectline[]) => {
    if (selectedItems.length === 0) {
      showMessage('請選擇要刪除的項目', 'warning')
      return
    }

    if (confirm(`確定要刪除選中的 ${selectedItems.length} 個項目嗎？`)) {
      try {
        loading.value = true
        
        for (const item of selectedItems) {
          await inspectsStore.deleteInspectline(item)
        }
        
        await loadDetailData()
        showMessage(`成功刪除 ${selectedItems.length} 個項目`, 'success')
      } catch (error) {
        showMessage('批量刪除失敗', 'error')
        options.onError?.(error, 'batchDelete')
      } finally {
        loading.value = false
      }
    }
  }

  // 驗證明細數據
  const validateDetailData = (): boolean => {
    if (!detailData.value.codeName) {
      showMessage('請輸入 Cap QRCode', 'warning')
      return false
    }
    if (!detailData.value.remarkName) {
      showMessage('請輸入異常原因', 'warning')
      return false
    }
    if (!detailData.value.quantity || detailData.value.quantity <= 0) {
      showMessage('請輸入有效的數量', 'warning')
      return false
    }
    return true
  }

  // 重置明細數據
  const resetDetailData = () => {
    detailData.value = {
      codeName: '',
      remarkName: '',
      quantity: 1
    }
    editingDetailId.value = null
  }

  return {
    // 響應式數據
    detailItems,
    detailDialog,
    detailForm,
    detailFormValid,
    editingDetailId,
    loading,
    detailData,
    snackbar,
    
    // 配置
    detailHeaders,
    validationRules,
    
    // 計算屬性
    currentInspectlines,
    hasDetails,
    isEditing,
    
    // 方法
    showMessage,
    openDetailDialog,
    editDetailItem,
    deleteDetailItem,
    saveDetailItem,
    closeDetailDialog,
    loadDetailData,
    deleteSelectedItems,
    validateDetailData,
    resetDetailData
  }
}
