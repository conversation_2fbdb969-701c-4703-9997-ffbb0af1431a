import { defineStore } from 'pinia'
import { ref } from 'vue'

const SUCCESS = "success"
const ERROR = "error"

export const useAppStore = defineStore('app', () => {
  // State
  const loading = ref<boolean>(false)
  const mode = ref<string>("")
  const snackbar = ref<boolean>(false)
  const notice = ref<string>("")

  // Actions
  function closeNotice(): void {
    setNotice("")
    setMode("")
    setSnackbar(false)
  }

  function closeNoticeWithDelay(timeout = 2000): void {
    setTimeout(() => {
      setNotice("")
      setMode("")
      setSnackbar(false)
    }, timeout)
  }

  function sendSuccessNotice(noticeText: string): void {
    setNotice(noticeText)
    setMode(SUCCESS)
    setSnackbar(true)
    closeNoticeWithDelay()
  }

  function sendErrorNotice(noticeText: string): void {
    setNotice(noticeText)
    setMode(ERROR)
    setSnackbar(true)
    closeNoticeWithDelay()
  }

  function setLoading(loadingState: boolean): void {
    loading.value = loadingState
  }

  function setNotice(noticeText: string): void {
    notice.value = noticeText
  }

  function setSnackbar(snackbarState: boolean): void {
    snackbar.value = snackbarState
  }

  function setMode(modeValue: string): void {
    mode.value = modeValue
  }

  return {
    // State
    loading,
    mode,
    snackbar,
    notice,
    // Actions
    closeNotice,
    closeNoticeWithDelay,
    sendSuccessNotice,
    sendErrorNotice,
    setLoading,
    setNotice,
    setSnackbar,
    setMode
  }
})

// 臨時的 appModule 導出，為了兼容舊代碼
export const appModule = {
  sendSuccessNotice: (message: string) => {
    console.log('✅ Success:', message)
    // 可以在這裡添加實際的通知邏輯
  },
  sendErrorNotice: (message: string) => {
    console.error('❌ Error:', message)
    // 可以在這裡添加實際的通知邏輯
  },
  closeNoticeWithDelay: (delay: number) => {
    console.log(`⏰ Notice will close in ${delay}ms`)
    // 可以在這裡添加實際的延遲關閉邏輯
  },
  setLoading: (loading: boolean) => {
    console.log('🔄 Loading:', loading)
    // 可以在這裡添加實際的載入狀態邏輯
  }
}

// Export state interface for root store
export interface AppState {
  loading: boolean
  mode: string
  snackbar: boolean
  notice: string
}
