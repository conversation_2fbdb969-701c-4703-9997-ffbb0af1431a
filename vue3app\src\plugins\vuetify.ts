// Styles
import '@mdi/font/css/materialdesignicons.css'
import 'vuetify/styles'

// Vuetify
import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'
import { aliases, mdi } from 'vuetify/iconsets/mdi'

// Create vuetify instance with Android optimizations
const vuetify = createVuetify({
  components,
  directives,
  icons: {
    defaultSet: 'mdi',
    aliases,
    sets: {
      mdi,
    },
  },
  // Android-optimized display settings
  display: {
    mobileBreakpoint: 'sm',
    thresholds: {
      xs: 0,
      sm: 600,
      md: 960,
      lg: 1280,
      xl: 1920,
    },
  },
  defaults: {
    // Navigation drawer optimizations for mobile/Android
    VNavigationDrawer: {
      width: 280,
      temporary: false,
      rail: false,
    },
    // App bar optimizations for Android
    VAppBar: {
      height: 64,
      elevation: 4,
    },
    // Button optimizations for touch interfaces
    VBtn: {
      minHeight: 44, // Android minimum touch target size
      ripple: true,
    },
    // Text field optimizations for mobile input
    VTextField: {
      variant: 'outlined',
      density: 'comfortable',
    },
    // Card optimizations for mobile viewing
    VCard: {
      elevation: 2,
    },
    // Data table optimizations for mobile
    VDataTable: {
      density: 'comfortable',
      itemsPerPage: 10,
    },
    // List optimizations for touch interfaces
    VList: {
      density: 'comfortable',
    },
    VListItem: {
      minHeight: 48, // Android recommended minimum
    },
  },
  theme: {
    defaultTheme: 'light',
    themes: {
      light: {
        colors: {
          primary: '#1976D2',
          secondary: '#424242',
          accent: '#82B1FF',
          error: '#FF5252',
          info: '#2196F3',
          success: '#4CAF50',
          warning: '#FFC107',
          surface: '#FFFFFF',
          background: '#F5F5F5',
        },
      },
      // Optional dark theme for Android users who prefer it
      dark: {
        colors: {
          primary: '#2196F3',
          secondary: '#616161',
          accent: '#82B1FF',
          error: '#FF5252',
          info: '#2196F3',
          success: '#4CAF50',
          warning: '#FFC107',
          surface: '#121212',
          background: '#000000',
        },
      },
    },
  },
})

export default vuetify
